(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[763],{53607:(e,r,i)=>{Promise.resolve().then(i.bind(i,91225))},91225:(e,r,i)=>{"use strict";i.r(r),i.d(r,{default:()=>E});var t=i(95155),n=i(12115),s=i(54581),l=i(700),a=i(68534),o=i(18407),c=i(78449),d=i(33989),x=i(27088),u=i(17348),h=i(68104),A=i(76380),p=i(41218),j=i(16632),v=i(41101),m=i(74964),y=i(96490),g=i(36114),f=i(54492),C=i(99927),b=i(72705),S=i(71977),w=i(40857),I=i(63954),k=i(45129),W=i(88806),P=i(28890),z=i(19505),B=i(268),N=i(2730),T=i(98648);function E(){let{services:e,fetchServices:r,addService:i,updateService:E,deleteService:F,authUser:L}=(0,N.A)(),[_,D]=(0,n.useState)(""),[O,U]=(0,n.useState)(!1),[q,$]=(0,n.useState)(!1),[G,H]=(0,n.useState)({name:"",price:"",duration:"",unit:"",description:"",category:""}),[K,M]=(0,n.useState)(!1),[R,J]=(0,n.useState)(null),[Q,V]=(0,n.useState)("all"),[X,Y]=(0,n.useState)("all");(0,n.useEffect)(()=>{L.token&&r()},[L.token,r]);let Z=(0,n.useMemo)(()=>Array.from(new Set(e.map(e=>e.category).filter(e=>!!e))).sort(),[e]),ee=e.filter(e=>{let r=e.name.toLowerCase().includes(_.toLowerCase())||e.description&&e.description.toLowerCase().includes(_.toLowerCase()),i=!0;"hourly"===Q?i=!!e.duration:"per-unit"===Q?i=!!e.unit:"fixed"===Q&&(i=!e.duration&&!e.unit);let t="all"===X||e.category===X;return r&&i&&t}),er=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],r=arguments.length>1?arguments[1]:void 0;if(e&&r){var i;H({id:r.id,name:r.name,price:r.price.toString(),duration:(null==(i=r.duration)?void 0:i.toString())||"",unit:r.unit||"",description:r.description||"",category:r.category||""}),$(!0)}else H({name:"",price:"",duration:"",unit:"",description:"",category:""}),$(!1);U(!0)},ei=()=>{U(!1)},et=e=>{let{name:r,value:i}=e.target;H(e=>({...e,[r]:i}))},en=async()=>{let e={name:G.name,price:parseFloat(G.price),duration:G.duration?parseInt(G.duration):void 0,unit:G.unit||void 0,description:G.description||void 0,category:G.category||void 0};try{if(q&&void 0!==G.id)await E(G.id,e);else{let{id:e,...r}=G;await i({name:r.name,price:parseFloat(r.price),duration:r.duration?parseInt(r.duration):void 0,unit:r.unit||void 0,description:r.description||"",category:r.category||void 0})}ei()}catch(e){console.error("Failed to save service:",e)}},es=e=>{J(e),M(!0)},el=async()=>{if(null!==R)try{await F(R),M(!1),J(null)}catch(e){console.error("Failed to delete service:",e)}};return(0,t.jsxs)(s.A,{sx:{flexGrow:1},children:[(0,t.jsxs)(s.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,t.jsx)(l.A,{variant:"h4",children:"Services"}),(0,t.jsx)(a.A,{variant:"contained",startIcon:(0,t.jsx)(w.A,{}),onClick:()=>er(),children:"Add Service"})]}),(0,t.jsx)(o.A,{sx:{p:2,mb:3},children:(0,t.jsxs)(s.A,{sx:{display:"flex",flexWrap:"wrap",gap:2},children:[(0,t.jsx)(s.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:(0,t.jsx)(c.A,{fullWidth:!0,placeholder:"Search services...",value:_,onChange:e=>D(e.target.value),InputProps:{startAdornment:(0,t.jsx)(d.A,{position:"start",children:(0,t.jsx)(I.A,{})})}})}),(0,t.jsx)(s.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:(0,t.jsxs)(x.A,{fullWidth:!0,children:[(0,t.jsx)(u.A,{id:"service-type-label",children:"Service Type"}),(0,t.jsxs)(h.A,{labelId:"service-type-label",value:Q,label:"Service Type",onChange:e=>{V(e.target.value)},children:[(0,t.jsx)(A.A,{value:"all",children:"All Services"}),(0,t.jsx)(A.A,{value:"hourly",children:"Hourly"}),(0,t.jsx)(A.A,{value:"fixed",children:"Fixed Price"}),(0,t.jsx)(A.A,{value:"per-unit",children:"Per Unit"})]})]})}),(0,t.jsx)(s.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:(0,t.jsxs)(x.A,{fullWidth:!0,children:[(0,t.jsx)(u.A,{id:"service-category-label",children:"Category"}),(0,t.jsxs)(h.A,{labelId:"service-category-label",value:X,label:"Category",onChange:e=>Y(e.target.value),children:[(0,t.jsx)(A.A,{value:"all",children:"All Categories"}),Z.map(e=>(0,t.jsx)(A.A,{value:e,children:e},e))]})]})})]})}),(0,t.jsx)(s.A,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:ee.length>0?ee.map(e=>(0,t.jsx)(s.A,{sx:{flexBasis:{xs:"100%",sm:"45%",md:"30%"}},children:(0,t.jsxs)(p.A,{elevation:2,children:[(0,t.jsxs)(j.A,{children:[(0,t.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:e.name}),(0,t.jsxs)(s.A,{sx:{display:"flex",flexWrap:"wrap",gap:1,mb:2},children:[e.category&&(0,t.jsx)(v.A,{label:e.category,size:"small",color:"primary",variant:"outlined"}),e.duration&&(0,t.jsx)(v.A,{icon:(0,t.jsx)(k.A,{fontSize:"small"}),label:"".concat(e.duration," min"),size:"small"}),e.unit&&(0,t.jsx)(v.A,{icon:(0,t.jsx)(W.A,{fontSize:"small"}),label:"Per ".concat(e.unit),size:"small"})]}),(0,t.jsx)(l.A,{variant:"body2",color:"text.secondary",sx:{mb:2},children:e.description}),(0,t.jsx)(m.A,{sx:{mb:2}}),(0,t.jsx)(s.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:(0,t.jsxs)(l.A,{variant:"h6",color:"primary",children:["$",Number(e.price).toFixed(2)]})})]}),(0,t.jsxs)(y.A,{children:[(0,t.jsx)(a.A,{size:"small",startIcon:(0,t.jsx)(P.A,{}),onClick:()=>er(!0,e),children:"Edit"}),(0,t.jsx)(a.A,{size:"small",color:"error",startIcon:(0,t.jsx)(z.A,{}),onClick:()=>es(e.id),children:"Delete"})]})]})},e.id)):(0,t.jsx)(s.A,{sx:{width:"100%",p:4,textAlign:"center"},children:(0,t.jsxs)(o.A,{sx:{p:4,borderRadius:2},children:[(0,t.jsx)(l.A,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"No services found"}),(0,t.jsx)(l.A,{variant:"body1",color:"text.secondary",children:"Try adjusting your search or category filter, or add a new service."}),(0,t.jsx)(a.A,{variant:"contained",startIcon:(0,t.jsx)(w.A,{}),sx:{mt:2},onClick:()=>er(),children:"Add Service"})]})})}),(0,t.jsxs)(g.A,{open:O,onClose:ei,maxWidth:"sm",fullWidth:!0,children:[(0,t.jsx)(f.A,{children:q?"Edit Service":"Add New Service"}),(0,t.jsx)(C.A,{children:(0,t.jsxs)(b.A,{spacing:2,sx:{mt:1},children:[(0,t.jsx)(c.A,{label:"Service Name",name:"name",value:G.name,onChange:et,fullWidth:!0,required:!0}),(0,t.jsx)(c.A,{label:"Price",name:"price",type:"number",value:G.price,onChange:et,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,t.jsx)(d.A,{position:"start",children:"$"})}}),(0,t.jsx)(c.A,{label:"Duration (minutes)",name:"duration",type:"number",value:G.duration,onChange:et,fullWidth:!0,helperText:"Leave empty for non-time-based services",InputProps:{startAdornment:(0,t.jsx)(d.A,{position:"start",children:(0,t.jsx)(k.A,{})})}}),(0,t.jsx)(c.A,{label:"Unit (e.g., 'per page', 'per person')",name:"unit",value:G.unit,onChange:et,fullWidth:!0,helperText:"Leave empty for fixed-price services",InputProps:{startAdornment:(0,t.jsx)(d.A,{position:"start",children:(0,t.jsx)(W.A,{})})}}),(0,t.jsx)(c.A,{label:"Description",name:"description",value:G.description,onChange:et,fullWidth:!0,multiline:!0,rows:3,InputProps:{startAdornment:(0,t.jsx)(d.A,{position:"start",children:(0,t.jsx)(B.A,{})})}}),(0,t.jsxs)(x.A,{fullWidth:!0,children:[(0,t.jsx)(u.A,{id:"service-category-label",children:"Category"}),(0,t.jsxs)(h.A,{labelId:"service-category-label",value:G.category,label:"Category",onChange:e=>H(r=>({...r,category:e.target.value})),children:[(0,t.jsx)(A.A,{value:"all",children:"All Categories"}),Z.map(e=>(0,t.jsx)(A.A,{value:e,children:e},e))]})]})]})}),(0,t.jsxs)(S.A,{children:[(0,t.jsx)(a.A,{onClick:ei,children:"Cancel"}),(0,t.jsx)(a.A,{onClick:en,variant:"contained",disabled:!G.name||!G.price,children:q?"Update":"Add"})]})]}),(0,t.jsx)(T.A,{open:K,title:"Confirm Delete",message:"Are you sure you want to delete this service? This action cannot be undone.",onConfirm:el,onCancel:()=>{M(!1),J(null)},confirmText:"Delete",confirmButtonColor:"error"})]})}},98648:(e,r,i)=>{"use strict";i.d(r,{A:()=>d});var t=i(95155);i(12115);var n=i(36114),s=i(54492),l=i(99927),a=i(700),o=i(71977),c=i(68534);let d=e=>{let{open:r,title:i,message:d,onConfirm:x,onCancel:u,infoMode:h=!1,confirmText:A,confirmButtonColor:p="primary"}=e;return(0,t.jsxs)(n.A,{open:r,onClose:u,children:[(0,t.jsx)(s.A,{children:i}),(0,t.jsx)(l.A,{children:(0,t.jsx)(a.A,{children:d})}),(0,t.jsxs)(o.A,{children:[!h&&(0,t.jsx)(c.A,{onClick:u,children:"Cancel"}),(0,t.jsx)(c.A,{onClick:x,variant:"contained",color:h?"primary":p,children:h?A||"OK":A||"Confirm"})]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[319,692,317,687,257,700,930,730,441,684,358],()=>r(53607)),_N_E=e.O()}]);