(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[752],{11793:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ee});var a=r(95155),n=r(12115),l=r(54581),s=r(92302),o=r(36114),i=r(54492),c=r(99927),d=r(27088),u=r(17348),x=r(68104),h=r(76380),g=r(71977),p=r(68534),m=r(35726),b=r(30392),A=r(97607),j=r(54673),f=r(35330),y=r(2730),v=r(35884),C=r(16324),w=r(98648),S=r(700),k=r(18407),I=r(78449),R=r(33989),T=r(16950),z=r(97644),F=r(49994),M=r(18096),W=r(40857),D=r(63954),E=r(95679),_=r(48771),N=r(21984);let B=e=>{let{onAddResource:t,searchQuery:r,onSearchChange:n,viewMode:s,onViewModeChange:o,resourceTypes:i,tabValue:c,onTabChange:d}=e;return(0,a.jsxs)(l.A,{children:[(0,a.jsxs)(l.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,a.jsx)(S.A,{variant:"h4",component:"h1",gutterBottom:!0,children:"Resource Management"}),(0,a.jsx)(p.A,{variant:"contained",color:"primary",startIcon:(0,a.jsx)(W.A,{}),onClick:t,children:"Add Resource"})]}),(0,a.jsxs)(k.A,{elevation:2,sx:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:2,mb:3,p:2,borderRadius:2},children:[(0,a.jsx)(I.A,{placeholder:"Search Resources...",value:r,onChange:n,InputProps:{startAdornment:(0,a.jsx)(R.A,{position:"start",children:(0,a.jsx)(D.A,{})})},sx:{flexGrow:1}}),(0,a.jsxs)(T.A,{value:s,exclusive:!0,onChange:(e,t)=>{null!==t&&o(t)},"aria-label":"View mode",children:[(0,a.jsxs)(z.A,{value:"list","aria-label":"List view",children:[(0,a.jsx)(E.A,{sx:{mr:.5}}),"List"]}),(0,a.jsxs)(z.A,{value:"grid","aria-label":"Grid view",children:[(0,a.jsx)(_.A,{sx:{mr:.5}}),"Grid"]}),(0,a.jsxs)(z.A,{value:"floorPlan","aria-label":"Floor plan view",children:[(0,a.jsx)(N.A,{sx:{mr:.5}}),"Map"]})]})]}),(0,a.jsx)(l.A,{sx:{borderBottom:1,borderColor:"divider",mb:2},children:(0,a.jsx)(F.A,{value:c,onChange:d,"aria-label":"resource type tabs",variant:"scrollable",scrollButtons:"auto",children:i.map((e,t)=>(0,a.jsx)(M.A,{label:e.charAt(0).toUpperCase()+e.slice(1),id:"resource-tab-".concat(t),"aria-controls":"resource-tabpanel-".concat(t)},t))})})]})};var P=r(72705);let U=e=>{let{open:t,editMode:r,formData:n,onClose:m,onInputChange:b,onSelectChange:A,onSubmit:j,error:f,loading:y,formSubmitted:v}=e;return(0,a.jsxs)(o.A,{open:t,onClose:m,maxWidth:"md",fullWidth:!0,"aria-labelledby":"resource-dialog-title",children:[(0,a.jsx)(i.A,{id:"resource-dialog-title",children:r?"Edit Resource":"Add New Resource"}),(0,a.jsxs)(c.A,{dividers:!0,children:[f&&(0,a.jsx)(s.A,{severity:"error",sx:{mb:2},children:f}),(0,a.jsxs)(P.A,{direction:{xs:"column",md:"row"},spacing:3,children:[(0,a.jsxs)(l.A,{sx:{width:{xs:"100%",md:"50%"}},children:[(0,a.jsx)(I.A,{autoFocus:!0,margin:"dense",name:"name",label:"Resource Name",type:"text",fullWidth:!0,value:n.name,onChange:b,required:!0,error:v&&!n.name,helperText:v&&!n.name?"Name is required":"",sx:{mb:2}}),(0,a.jsxs)(d.A,{fullWidth:!0,margin:"dense",sx:{mb:2},children:[(0,a.jsx)(u.A,{id:"type-label",children:"Type"}),(0,a.jsxs)(x.A,{labelId:"type-label",name:"type",value:n.type,onChange:A,label:"Type",children:[(0,a.jsx)(h.A,{value:"table",children:"Table"}),(0,a.jsx)(h.A,{value:"room",children:"Room"}),(0,a.jsx)(h.A,{value:"desk",children:"Desk"}),(0,a.jsx)(h.A,{value:"equipment",children:"Equipment"})]})]}),(0,a.jsxs)(d.A,{fullWidth:!0,margin:"dense",sx:{mb:2},children:[(0,a.jsx)(u.A,{id:"status-label",children:"Status"}),(0,a.jsxs)(x.A,{labelId:"status-label",name:"status",value:n.status,onChange:A,label:"Status",children:[(0,a.jsx)(h.A,{value:"available",children:"Available"}),(0,a.jsx)(h.A,{value:"booked",children:"Booked"}),(0,a.jsx)(h.A,{value:"maintenance",children:"Maintenance"}),(0,a.jsx)(h.A,{value:"in-use",children:"In Use"})]})]}),(0,a.jsx)(I.A,{margin:"dense",name:"hourly_rate",label:"Hourly Rate",type:"number",fullWidth:!0,value:n.hourly_rate,onChange:b,sx:{mb:2}}),"room"===n.type&&(0,a.jsx)(I.A,{margin:"dense",name:"capacity",label:"Capacity",type:"number",fullWidth:!0,value:n.capacity||"",onChange:b})]}),(0,a.jsxs)(l.A,{sx:{width:{xs:"100%",md:"50%"}},children:[(0,a.jsxs)(d.A,{fullWidth:!0,margin:"dense",sx:{mb:2},children:[(0,a.jsx)(u.A,{id:"floor-label",children:"Floor"}),(0,a.jsxs)(x.A,{labelId:"floor-label",name:"floor",value:n.floor||"1",onChange:A,label:"Floor",children:[(0,a.jsx)(h.A,{value:"1",children:"Floor 1"}),(0,a.jsx)(h.A,{value:"2",children:"Floor 2"}),(0,a.jsx)(h.A,{value:"3",children:"Floor 3"})]})]}),(0,a.jsx)(I.A,{margin:"dense",name:"zone",label:"Zone (Optional)",type:"text",fullWidth:!0,value:n.zone||"",onChange:b,sx:{mb:2}}),(0,a.jsx)(S.A,{variant:"subtitle2",sx:{mt:2,mb:1},children:"Floor Plan Details (Optional)"}),(0,a.jsxs)(P.A,{direction:"row",spacing:2,children:[(0,a.jsx)(I.A,{name:"x",label:"X",type:"number",value:n.x,onChange:b,sx:{width:"50%"}}),(0,a.jsx)(I.A,{name:"y",label:"Y",type:"number",value:n.y,onChange:b,sx:{width:"50%"}})]}),(0,a.jsxs)(P.A,{direction:"row",spacing:2,sx:{mt:1},children:[(0,a.jsx)(I.A,{name:"width",label:"Width",type:"number",value:n.width,onChange:b,sx:{width:"50%"}}),(0,a.jsx)(I.A,{name:"height",label:"Height",type:"number",value:n.height,onChange:b,sx:{width:"50%"}})]})]})]})]}),(0,a.jsxs)(g.A,{sx:{px:3,py:2},children:[(0,a.jsx)(p.A,{onClick:m,color:"inherit",disabled:y,children:"Cancel"}),(0,a.jsx)(p.A,{onClick:j,variant:"contained",color:"primary",disabled:y,children:y?"Saving...":r?"Save Changes":"Add Resource"})]})]})},Z=e=>{let{selectedFloor:t,onFloorChange:r,floors:n,selectedZone:s,onZoneChange:o,zones:i}=e;return(0,a.jsxs)(k.A,{sx:{p:2,mb:3,mt:-1},children:[" ",(0,a.jsxs)(l.A,{sx:{display:"flex",flexWrap:"wrap",gap:2},children:[(0,a.jsx)(l.A,{sx:{flexBasis:{xs:"100%",sm:"calc(50% - 8px)",md:"calc(33.333% - 11px)"},minWidth:"150px"},children:(0,a.jsxs)(d.A,{fullWidth:!0,size:"small",children:[(0,a.jsx)(u.A,{id:"floor-filter-label",children:"Filter by Floor"}),(0,a.jsxs)(x.A,{labelId:"floor-filter-label",value:null===t?"":t.toString(),onChange:r,label:"Filter by Floor",children:[(0,a.jsx)(h.A,{value:"",children:(0,a.jsx)("em",{children:"All Floors"})}),n.map(e=>(0,a.jsxs)(h.A,{value:e.toString(),children:["Floor ",e]},e))]})]})}),(0,a.jsx)(l.A,{sx:{flexBasis:{xs:"100%",sm:"calc(50% - 8px)",md:"calc(33.333% - 11px)"},minWidth:"150px"},children:(0,a.jsxs)(d.A,{fullWidth:!0,size:"small",children:[(0,a.jsx)(u.A,{id:"zone-filter-label",children:"Filter by Zone"}),(0,a.jsxs)(x.A,{labelId:"zone-filter-label",value:s,onChange:o,label:"Filter by Zone",children:[(0,a.jsx)(h.A,{value:"all",children:(0,a.jsx)("em",{children:"All Zones"})}),i.map(e=>(0,a.jsx)(h.A,{value:e,children:e},e))]})]})})]})]})};var O=r(41218),q=r(16632),G=r(83729),L=r(41101),H=r(96490),Y=r(28890),V=r(19505);let X=e=>{let{resource:t,onEdit:r,onDelete:n,onStatusClick:s,isDarkMode:o,getResourceIcon:i,getStatusChipColor:c}=e;return(0,a.jsxs)(O.A,{elevation:3,sx:{display:"flex",flexDirection:"column",height:"100%",border:"1px solid",borderColor:"divider",transition:"box-shadow 0.3s ease-in-out, transform 0.2s ease-in-out","&:hover":{boxShadow:o?"0 8px 16px rgba(0,0,0,0.5)":"0 8px 16px rgba(0,0,0,0.2)",transform:"translateY(-4px)"}},children:[(0,a.jsxs)(q.A,{sx:{flexGrow:1,pb:1},children:[" ",(0,a.jsxs)(l.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:1.5},children:[(0,a.jsxs)(l.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(l.A,{sx:{mr:1.5,p:.5,borderRadius:"50%",backgroundColor:o?"grey.700":"grey.200",display:"flex",alignItems:"center",justifyContent:"center"},children:i(t.type)}),(0,a.jsx)(l.A,{sx:{maxWidth:140,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:(0,a.jsx)(G.A,{title:t.name,placement:"top-start",children:(0,a.jsx)(S.A,{variant:"h6",component:"div",sx:{fontWeight:"medium",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:t.name})})})]}),(0,a.jsx)(L.A,{label:t.status.charAt(0).toUpperCase()+t.status.slice(1),color:c(t.status),size:"small",onClick:()=>s(t.id,t.status),sx:{cursor:"pointer",fontWeight:"medium"}})]}),(0,a.jsxs)(l.A,{sx:{mb:1.5,flex:1},children:[" ",(0,a.jsxs)(P.A,{spacing:.5,children:[" ",(0,a.jsxs)(l.A,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,a.jsx)(S.A,{variant:"body2",color:"text.secondary",children:"Rate:"}),(0,a.jsxs)(S.A,{variant:"body2",sx:{fontWeight:"medium"},children:["$",t.hourly_rate,"/hr"]})]}),("room"===t.type||"desk"===t.type)&&t.capacity&&(0,a.jsxs)(l.A,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,a.jsx)(S.A,{variant:"body2",color:"text.secondary",children:"Capacity:"}),(0,a.jsx)(S.A,{variant:"body2",sx:{fontWeight:"medium"},children:t.capacity})]}),t.floor&&(0,a.jsxs)(l.A,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,a.jsx)(S.A,{variant:"body2",color:"text.secondary",children:"Floor:"}),(0,a.jsx)(S.A,{variant:"body2",sx:{fontWeight:"medium"},children:t.floor})]}),t.zone&&(0,a.jsxs)(l.A,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,a.jsx)(S.A,{variant:"body2",color:"text.secondary",children:"Zone:"}),(0,a.jsx)(S.A,{variant:"body2",sx:{fontWeight:"medium"},children:t.zone})]})]})]})]}),(0,a.jsxs)(H.A,{sx:{justifyContent:"flex-end",pt:0,pb:1.5,px:2},children:[" ",(0,a.jsx)(p.A,{size:"small",startIcon:(0,a.jsx)(Y.A,{}),onClick:()=>r(t),color:"primary",children:"Edit"}),(0,a.jsx)(p.A,{size:"small",startIcon:(0,a.jsx)(V.A,{}),onClick:()=>n(t.id),color:"error",children:"Delete"})]})]})},$=e=>{let{resources:t,...r}=e;return 0===t.length?(0,a.jsx)(l.A,{sx:{width:"100%",p:4,textAlign:"center"},children:(0,a.jsxs)(k.A,{sx:{p:4,borderRadius:2,backgroundColor:r.isDarkMode?"grey.800":"grey.100"},children:[(0,a.jsx)(S.A,{variant:"h6",color:"text.secondary",children:"No resources found."}),(0,a.jsx)(S.A,{variant:"body1",color:"text.secondary",children:"Try adjusting your search or filters."})]})}):(0,a.jsx)(l.A,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",sm:"repeat(2, 1fr)",md:"repeat(3, 1fr)",lg:"repeat(4, 1fr)"},gap:3},children:t.map(e=>(0,a.jsx)(X,{resource:e,...r},e.id))})},K=e=>{let{resources:t,onEdit:r,onDelete:n,onStatusClick:s,getResourceIcon:o,getStatusChipColor:i}=e;return 0===t.length?(0,a.jsx)(l.A,{sx:{width:"100%",textAlign:"center",py:5},children:(0,a.jsx)(S.A,{variant:"subtitle1",color:"textSecondary",children:"No resources to display."})}):(0,a.jsx)(P.A,{spacing:2,children:t.map(e=>(0,a.jsxs)(O.A,{elevation:2,sx:{display:"flex",alignItems:"center",p:2},children:[(0,a.jsx)(l.A,{sx:{mr:2,display:"flex",alignItems:"center"},children:o(e.type)}),(0,a.jsxs)(l.A,{sx:{flexGrow:1},children:[(0,a.jsx)(S.A,{variant:"h6",children:e.name}),(0,a.jsxs)(S.A,{variant:"body2",color:"text.secondary",children:["Type: ",e.type," | Rate: $",e.hourly_rate,"/hr",e.capacity&&" | Capacity: ".concat(e.capacity),e.floor&&" | Floor: ".concat(e.floor),e.zone&&" | Zone: ".concat(e.zone)]})]}),(0,a.jsxs)(P.A,{direction:"row",spacing:1,alignItems:"center",children:[(0,a.jsx)(L.A,{label:e.status.charAt(0).toUpperCase()+e.status.slice(1),color:i(e.status),size:"small",onClick:()=>s(e.id,e.status),sx:{cursor:"pointer"}}),(0,a.jsx)(p.A,{size:"small",startIcon:(0,a.jsx)(Y.A,{}),onClick:()=>r(e),children:"Edit"}),(0,a.jsx)(p.A,{size:"small",startIcon:(0,a.jsx)(V.A,{}),onClick:()=>n(e.id),color:"error",children:"Delete"})]})]},e.id))})},Q=e=>{let{resources:t,onEdit:r,onStatusClick:n,onResourcePositionUpdate:s,selectedFloor:o,isDarkMode:i,getResourceColor:c,getStatusChipColor:d}=e,u=(0,C.A)(),x=t.filter(e=>e.floor===o),h=Math.max(...x.map(e=>{var t,r;return(null!=(t=e.x)?t:0)+(null!=(r=e.width)?r:1)}),12),g=Math.max(...x.map(e=>{var t,r;return(null!=(t=e.y)?t:0)+(null!=(r=e.height)?r:1)}),8),p=Math.max(12,h+2),m=Math.max(8,g+2),b=64*m,A=e=>{let t=e.width||1,r=e.height||1;return{width:60*t+(t-1)*4,height:60*r+(r-1)*4}},j=e=>{var t,r;return{left:64*(null!=(t=e.x)?t:0),top:(null!=(r=e.y)?r:0)*64}};return null===o?(0,a.jsx)(S.A,{children:"Please select a floor to view the plan."}):(0,a.jsxs)(l.A,{sx:{position:"relative",width:"100%",height:"".concat(b+100,"px"),border:"1px solid ".concat(i?"rgba(255, 255, 255, 0.12)":"rgba(0, 0, 0, 0.12)"),borderRadius:1,overflow:"auto",p:2,bgcolor:i?"rgba(0, 0, 0, 0.05)":"rgba(0, 0, 0, 0.02)"},children:[(0,a.jsxs)(l.A,{sx:{display:"flex",justifyContent:"center",flexWrap:"wrap",gap:2,mb:2,p:1},children:[(0,a.jsxs)(l.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(l.A,{sx:{width:16,height:16,backgroundColor:"rgba(76, 175, 80, 0.7)",borderRadius:.5,mr:.5}}),(0,a.jsx)(S.A,{variant:"caption",children:"Available"})]}),(0,a.jsxs)(l.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(l.A,{sx:{width:16,height:16,backgroundColor:"rgba(244, 67, 54, 0.7)",borderRadius:.5,mr:.5}}),(0,a.jsx)(S.A,{variant:"caption",children:"Booked"})]}),(0,a.jsxs)(l.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(l.A,{sx:{width:16,height:16,backgroundColor:"rgba(255, 152, 0, 0.7)",borderRadius:.5,mr:.5}}),(0,a.jsx)(S.A,{variant:"caption",children:"Maintenance"})]}),["in-use"].map(e=>{if("in-use"===e){let t=c(e);return(0,a.jsxs)(l.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(l.A,{sx:{width:16,height:16,backgroundColor:t.bg,borderRadius:.5,mr:.5}}),(0,a.jsx)(S.A,{variant:"caption",children:e.charAt(0).toUpperCase()+e.slice(1)})]},e)}return null})]}),(0,a.jsxs)(l.A,{className:"floor-plan-grid",sx:{position:"relative",width:"".concat(64*p,"px"),height:"".concat(b,"px"),margin:"0 auto",background:i?"linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px)":"linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px)",backgroundSize:"".concat(60,"px ").concat(60,"px")},onDragOver:e=>{e.preventDefault()},children:[(0,a.jsx)(l.A,{sx:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backgroundImage:"linear-gradient(#ddd 1px, transparent 1px), linear-gradient(90deg, #ddd 1px, transparent 1px)",backgroundSize:"".concat(60,"px ").concat(60,"px"),opacity:.5}}),0===x.length&&(0,a.jsx)(l.A,{sx:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",position:"absolute"},children:(0,a.jsxs)(S.A,{variant:"body1",color:"text.secondary",children:["No resources found on Floor ",o,". Try adjusting your filters or adding resources with position data."]})}),x.map(e=>{let t=A(e),o=j(e),i=c(e.status),x="T-".concat(e.id.toString().padStart(2,"0"));return(0,a.jsxs)(l.A,{"data-resource-id":e.id,sx:{position:"absolute",left:"".concat(o.left,"px"),top:"".concat(o.top,"px"),width:"".concat(t.width,"px"),height:"".concat(t.height,"px"),backgroundColor:i.bg,border:"2px solid",borderColor:i.border,borderRadius:2,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:1,cursor:"move",transition:"all 0.2s ease","&:hover":{boxShadow:u.shadows[3],transform:"scale(1.02)"},userSelect:"none",touchAction:"none"},draggable:!0,onDragStart:t=>{t.currentTarget.setAttribute("dragging","true"),t.dataTransfer.setData("text/plain",e.id.toString()),t.dataTransfer.effectAllowed="move";let r=t.currentTarget.getBoundingClientRect(),a=t.clientX-r.left,n=t.clientY-r.top;t.currentTarget.setAttribute("data-drag-offset-x",a.toString()),t.currentTarget.setAttribute("data-drag-offset-y",n.toString());let l=t.currentTarget.cloneNode(!0);l.style.position="absolute",l.style.left="-9999px",l.style.width="".concat(t.currentTarget.offsetWidth,"px"),l.style.height="".concat(t.currentTarget.offsetHeight,"px"),l.style.opacity="0.7",l.style.zIndex="10000",document.body.appendChild(l),t.dataTransfer.setDragImage(l,a,n),setTimeout(()=>{document.body.contains(l)&&document.body.removeChild(l)},0)},onDrag:e=>{e.currentTarget.hasAttribute("dragging")||e.currentTarget.setAttribute("dragging","true")},onDragEnd:t=>{var r,a;if(!t.currentTarget.hasAttribute("dragging"))return;t.currentTarget.removeAttribute("dragging");let n=t.currentTarget.closest(".floor-plan-grid");if(!n)return;let l=n.getBoundingClientRect(),o=parseInt(t.currentTarget.getAttribute("data-drag-offset-x")||"0"),i=parseInt(t.currentTarget.getAttribute("data-drag-offset-y")||"0"),c=t.clientX-l.left-o,d=t.clientY-l.top-i,u=Math.round(c/64),x=Math.round(d/64),h=e.width||1,g=e.height||1;u=Math.max(0,Math.min(u,p-h)),x=Math.max(0,Math.min(x,m-g)),(u!==(null!=(r=e.x)?r:0)||x!==(null!=(a=e.y)?a:0))&&s(e.id,u,x),t.currentTarget.removeAttribute("data-drag-offset-x"),t.currentTarget.removeAttribute("data-drag-offset-y")},onClick:t=>{t.currentTarget.hasAttribute("dragging")||r(e)},children:[(0,a.jsx)(S.A,{variant:"subtitle2",sx:{fontWeight:"bold",color:u.palette.getContrastText(i.bg),textAlign:"center",fontSize:"0.8rem"},children:x}),t.width>=120&&(0,a.jsx)(S.A,{variant:"body2",sx:{color:u.palette.getContrastText(i.bg),textAlign:"center",fontSize:"0.7rem",mt:.5},children:e.name}),"available"!==e.status&&(0,a.jsx)(L.A,{label:e.status.charAt(0).toUpperCase()+e.status.slice(1),size:"small",color:d(e.status),sx:{fontSize:"0.6rem",height:"20px",mt:.5,"& .MuiChip-label":{padding:"0 6px"}},onClick:t=>{t.stopPropagation(),n(e.id,e.status)}})]},e.id)})]})]})},J=e=>{let{viewMode:t,resources:r,onEditResource:n,onDeleteResource:s,onStatusClick:o,onResourcePositionUpdate:i,selectedFloor:c,isDarkMode:d,getResourceIcon:u,getStatusChipColor:x,getResourceColor:h,loading:g,error:p}=e;if(g)return(0,a.jsx)(l.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"200px"},children:(0,a.jsx)(S.A,{children:"Loading resources..."})});if(0===r.length&&!g&&!p)return(0,a.jsx)(l.A,{sx:{width:"100%",p:4,textAlign:"center"},children:(0,a.jsxs)(k.A,{sx:{p:4,borderRadius:2,backgroundColor:d?"grey.800":"grey.100"},children:[(0,a.jsx)(S.A,{variant:"h6",color:"text.secondary",children:"No Resources Found"}),(0,a.jsx)(S.A,{variant:"body1",color:"text.secondary",children:"There are no resources matching your current filters. Try adjusting your search or filter criteria."})]})});switch(t){case"list":return(0,a.jsx)(K,{resources:r,onEdit:n,onDelete:s,onStatusClick:o,getResourceIcon:u,getStatusChipColor:x});case"grid":return(0,a.jsx)($,{resources:r,onEdit:n,onDelete:s,onStatusClick:o,isDarkMode:d,getResourceIcon:u,getStatusChipColor:x});case"floorPlan":return(0,a.jsx)(Q,{resources:r,onEdit:n,onDelete:s,onStatusClick:o,onResourcePositionUpdate:i,selectedFloor:c,isDarkMode:d,getResourceColor:h,getStatusChipColor:x});default:return(0,a.jsx)(S.A,{children:"Unknown view mode."})}};function ee(){let{resources:e,addResource:t,updateResource:r,deleteResource:S,updateResourceStatus:k,fetchResources:I,setResources:R,authUser:T}=(0,y.A)(),[z,F]=(0,n.useState)(""),[M,W]=(0,n.useState)(!1),[D,E]=(0,n.useState)(!1),[_,N]=(0,n.useState)(!1),[P,O]=(0,n.useState)(null),q=(0,n.useMemo)(()=>({id:0,name:"",type:"table",status:"available",hourly_rate:"",capacity:"",floor:"1",zone:"",x:1,y:1,width:1,height:1}),[]),[G,L]=(0,n.useState)(q),[H,Y]=(0,n.useState)(0),[V,X]=(0,n.useState)(!1),[$,K]=(0,n.useState)(null),[Q,ee]=(0,n.useState)("available"),[et,er]=(0,n.useState)("grid"),[ea,en]=(0,n.useState)(1),[el,es]=(0,n.useState)("all"),[eo,ei]=(0,n.useState)(!1),[ec,ed]=(0,n.useState)(""),[eu,ex]=(0,n.useState)("success"),[eh,eg]=(0,n.useState)([]),[ep,em]=(0,n.useState)(!1),{mode:eb}=(0,v.D)(),eA=(0,C.A)(),ej="dark"===eb,[ef,ey]=(0,n.useState)(!1),[ev,eC]=(0,n.useState)(null),[ew,eS]=(0,n.useState)(null);(0,n.useEffect)(()=>{T.token&&(ey(!0),eC(null),I().catch(e=>{console.error("Failed to fetch resources:",e),eC("Could not load resources. Please try again later."),ed("Could not load resources."),ex("error"),ei(!0)}).finally(()=>ey(!1)))},[I,T.token]);let ek=(0,n.useMemo)(()=>Array.from(new Set((Array.isArray(e)?e:[]).map(e=>e.floor).filter(Boolean).map(e=>parseInt(e.toString(),10)))).sort((e,t)=>e-t),[e]),eI=(0,n.useMemo)(()=>Array.from(new Set((Array.isArray(e)?e:[]).map(e=>e.zone).filter(Boolean))),[e]),eR=(0,n.useMemo)(()=>["all","room","desk","equipment","table"],[]);(0,n.useEffect)(()=>{if(ef&&!e.length)return;let t=Array.isArray(e)?e:[],r=new Map;t.forEach(e=>{r.has(e.id)||r.set(e.id,e)}),eg(Array.from(r.values()).filter(e=>{var t;let r=e.name.toLowerCase().includes(z.toLowerCase()),a=H>0&&H<eR.length?eR[H]:"all",n="all"===a||e.type.toLowerCase()===a,l=null===ea||(null==(t=e.floor)?void 0:t.toString())===ea.toString(),s="all"===el||e.zone===el;return r&&n&&l&&s}))},[e,z,H,ea,el,eR,ef]);let eT=e=>{let t=e.target.value;en(""===t?null:Number(t))},ez=e=>{es(e.target.value)},eF=(0,n.useCallback)(t=>{if(t){var r,a,n,l,s,o,i;return{...q,...t,floor:(null==(r=t.floor)?void 0:r.toString())||"1",hourly_rate:(null==(a=t.hourly_rate)?void 0:a.toString())||"",capacity:(null==(n=t.capacity)?void 0:n.toString())||"",x:null!=(l=t.x)?l:q.x,y:null!=(s=t.y)?s:q.y,width:null!=(o=t.width)?o:q.width,height:null!=(i=t.height)?i:q.height}}let c=e&&e.length>0?Math.max(...e.map(e=>e.id))+1:1;return{...q,id:c}},[e,q]),eM=(0,n.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;E(e),L(eF(t)),W(!0),eS(null),em(!1)},[eF]),eW=()=>{W(!1),eS(null),L(q)},eD=async()=>{if(em(!0),!G.name){eS("Resource name is required."),ed("Resource name is required."),ex("error"),ei(!0);return}ey(!0),eS(null);let e={name:G.name,type:G.type,status:G.status,hourly_rate:parseFloat(G.hourly_rate.toString())||0,capacity:G.capacity?parseInt(G.capacity.toString()):0,floor:parseInt(G.floor.toString()),zone:G.zone?G.zone.toString():"",x:parseInt(G.x.toString()),y:parseInt(G.y.toString()),width:parseInt(G.width.toString()),height:parseInt(G.height.toString())};try{D&&G.id?(await r(G.id,e),ed("Resource updated successfully")):(await t(e),ed("Resource added successfully")),ex("success"),eW(),I()}catch(t){let e=t instanceof Error?t.message:"An unexpected error occurred.";eS(e),ed(e),ex("error")}finally{ey(!1),ei(!0)}},eE=()=>{X(!1),K(null)},e_=async()=>{if(null!==$){ey(!0);try{await k($,Q),ed("Resource status updated."),ex("success"),eE(),I()}catch(e){ed(e instanceof Error?e.message:"Failed to update status."),ex("error")}finally{ey(!1),ei(!0)}}},eN=async()=>{if(null!==P){ey(!0);try{await S(P),ed("Resource deleted."),ex("success"),I()}catch(e){ed(e instanceof Error?e.message:"Failed to delete resource."),ex("error")}finally{ey(!1),N(!1),O(null),ei(!0)}}},eB=(0,n.useCallback)(e=>{switch(e){case"room":default:return(0,a.jsx)(b.A,{});case"desk":return(0,a.jsx)(A.A,{});case"equipment":return(0,a.jsx)(j.A,{});case"table":return(0,a.jsx)(f.A,{})}},[]),eP=(0,n.useCallback)(e=>({available:"success",booked:"error",maintenance:"warning","in-use":"info"})[e]||"default",[]),eU=(0,n.useCallback)(e=>{if(!eA)return{bg:"rgba(158, 158, 158, 0.7)",border:"#9e9e9e"};let t={available:{bg:ej?"rgba(76, 175, 80, 0.8)":"rgba(76, 175, 80, 0.7)",border:eA.palette.success.main},booked:{bg:ej?"rgba(244, 67, 54, 0.8)":"rgba(244, 67, 54, 0.7)",border:eA.palette.error.main},maintenance:{bg:ej?"rgba(255, 152, 0, 0.8)":"rgba(255, 152, 0, 0.7)",border:eA.palette.warning.main},"in-use":{bg:ej?"rgba(33, 150, 243, 0.8)":"rgba(33, 150, 243, 0.7)",border:eA.palette.info.main}},r={bg:ej?"rgba(158, 158, 158, 0.8)":"rgba(158, 158, 158, 0.7)",border:eA.palette.grey[500]};return t[e]||r},[ej,eA]),eZ=async(t,a,n)=>{let l=e.find(e=>e.id===t);if(!l)return;let s={...l,x:a,y:n},o=[...e];R(e.map(e=>e.id===t?s:e));try{await r(t,{x:a,y:n}),ed("Position updated for '".concat(l.name,"'.")),ex("success")}catch(e){R(o),ed("Failed to update position for '".concat(l.name,"'. ").concat(e instanceof Error?e.message:"")),ex("error")}finally{ei(!0)}},eO=(e,t)=>{"clickaway"!==t&&ei(!1)};return(0,a.jsxs)(l.A,{sx:{flexGrow:1},children:[(0,a.jsx)(B,{onAddResource:()=>eM(!1),searchQuery:z,onSearchChange:e=>{F(e.target.value)},viewMode:et,onViewModeChange:e=>{er(e)},selectedFloor:ea,onFloorChange:eT,floors:ek,selectedZone:el,onZoneChange:ez,zones:eI,resourceTypes:eR,tabValue:H,onTabChange:(e,t)=>{Y(t)}}),ev&&(0,a.jsx)(s.A,{severity:"error",sx:{my:2},children:ev}),(0,a.jsx)(Z,{selectedFloor:ea,onFloorChange:eT,floors:ek,selectedZone:el,onZoneChange:ez,zones:eI,viewMode:et}),(0,a.jsx)(l.A,{sx:{mt:"floorPlan"===et&&ek.length>0?0:2},children:(0,a.jsx)(J,{viewMode:et,resources:eh,onEditResource:e=>eM(!0,e),onDeleteResource:e=>{O(e),N(!0)},onStatusClick:(e,t)=>{K(e),ee(t),X(!0)},onResourcePositionUpdate:eZ,selectedFloor:ea,isDarkMode:ej,getResourceIcon:eB,getStatusChipColor:eP,getResourceColor:eU,loading:ef&&0===eh.length&&!ev,error:ev&&0===eh.length?ev:null})}),(0,a.jsx)(U,{open:M,editMode:D,formData:G,onClose:eW,onInputChange:e=>{let{name:t,value:r}=e.target,a=["hourly_rate","capacity","x","y","width","height"];L(e=>({...e,[t]:a.includes(t)&&""!==r?parseFloat(r):r}))},onSelectChange:e=>{let{name:t,value:r}=e.target;L(e=>({...e,[t]:r}))},onSubmit:eD,error:ew,loading:ef,formSubmitted:ep}),(0,a.jsx)(w.A,{open:_,title:"Confirm Delete",message:"Are you sure you want to delete this resource? This action cannot be undone.",onConfirm:eN,onCancel:()=>{N(!1),O(null)},confirmButtonColor:"error"}),(0,a.jsxs)(o.A,{open:V,onClose:eE,children:[(0,a.jsx)(i.A,{children:"Update Resource Status"}),(0,a.jsx)(c.A,{children:(0,a.jsxs)(d.A,{fullWidth:!0,sx:{mt:1},children:[(0,a.jsx)(u.A,{id:"new-status-label",children:"New Status"}),(0,a.jsxs)(x.A,{labelId:"new-status-label",value:Q,onChange:e=>{ee(e.target.value)},label:"New Status",children:[(0,a.jsx)(h.A,{value:"available",children:"Available"}),(0,a.jsx)(h.A,{value:"booked",children:"Booked"}),(0,a.jsx)(h.A,{value:"maintenance",children:"Maintenance"}),(0,a.jsx)(h.A,{value:"in-use",children:"In Use"})]})]})}),(0,a.jsxs)(g.A,{children:[(0,a.jsx)(p.A,{onClick:eE,children:"Cancel"}),(0,a.jsx)(p.A,{onClick:e_,variant:"contained",disabled:ef,children:"Update Status"})]})]}),(0,a.jsx)(m.A,{open:eo,autoHideDuration:4e3,onClose:eO,anchorOrigin:{vertical:"bottom",horizontal:"center"},children:(0,a.jsx)(s.A,{onClose:eO,severity:eu,sx:{width:"100%"},variant:"filled",children:ec})})]})}},35884:(e,t,r)=>{"use strict";r.d(t,{D:()=>l}),r(95155);var a=r(12115);let n=(0,a.createContext)({mode:"light",toggleMode:()=>{},theme:"light",toggleTheme:()=>{}}),l=()=>(0,a.useContext)(n)},52622:(e,t,r)=>{Promise.resolve().then(r.bind(r,11793))},98648:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(95155);r(12115);var n=r(36114),l=r(54492),s=r(99927),o=r(700),i=r(71977),c=r(68534);let d=e=>{let{open:t,title:r,message:d,onConfirm:u,onCancel:x,infoMode:h=!1,confirmText:g,confirmButtonColor:p="primary"}=e;return(0,a.jsxs)(n.A,{open:t,onClose:x,children:[(0,a.jsx)(l.A,{children:r}),(0,a.jsx)(s.A,{children:(0,a.jsx)(o.A,{children:d})}),(0,a.jsxs)(i.A,{children:[!h&&(0,a.jsx)(c.A,{onClick:x,children:"Cancel"}),(0,a.jsx)(c.A,{onClick:u,variant:"contained",color:h?"primary":p,children:h?g||"OK":g||"Confirm"})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[319,692,317,687,257,700,13,216,273,726,406,730,441,684,358],()=>t(52622)),_N_E=e.O()}]);