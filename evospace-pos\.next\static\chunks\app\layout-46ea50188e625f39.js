(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{30347:()=>{},43304:(e,t,r)=>{"use strict";r.d(t,{default:()=>H});var n=r(95155),s=r(12115),l=r(30529),o=r(700),i=r(74964),a=r(63148),c=r(65528),d=r(22376),h=r(39489),x=r(85222),u=r(54581),m=r(22548),p=r(71169),b=r(3127),f=r(42663),j=r(21963),A=r(76380),v=r(12939),k=r(12522),g=r(13895),w=r(63289),y=r(32922),C=r(75512),S=r(8025),E=r(30660),M=r(11247),P=r(44389),_=r(27371),z=r(66027),L=r(77934),F=r(98387),D=r(60807),O=r(15808),I=r(6874),R=r.n(I),T=r(35695),W=r(2730),N=r(90038);let G=[{text:"Dashboard",icon:(0,n.jsx)(k.A,{}),path:"/"},{text:"Serve",icon:(0,n.jsx)(g.A,{}),path:"/pos"},{text:"Resources",icon:(0,n.jsx)(w.A,{}),path:"/resources"},{text:"Products",icon:(0,n.jsx)(y.A,{}),path:"/products"},{text:"Services",icon:(0,n.jsx)(C.A,{}),path:"/services"},{text:"Members",icon:(0,n.jsx)(S.A,{}),path:"/members"},{text:"Reports",icon:(0,n.jsx)(E.A,{}),path:"/reports"},{text:"Settings",icon:(0,n.jsx)(M.A,{}),path:"/settings"}];function H(e){var t;let{children:r}=e,[k,g]=(0,s.useState)(!1),[w,y]=(0,s.useState)(null),[C,S]=(0,s.useState)(!1),E=(0,T.usePathname)(),{currentUser:M,logout:I,authUser:H,loadAuthToken:q}=(0,W.A)(),{mode:B,toggleTheme:J}=(0,N.W)();(0,s.useEffect)(()=>{q()},[q]);let K=!H.token;(0,s.useEffect)(()=>{let e=()=>{S(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",e),()=>{document.removeEventListener("fullscreenchange",e)}},[]);let Q=()=>{g(!k)},U=()=>{y(null)},V=()=>{document.fullscreenElement?document.exitFullscreen&&document.exitFullscreen():document.documentElement.requestFullscreen().catch(e=>{console.error("Error attempting to enable fullscreen: ".concat(e.message))})};(0,s.useEffect)(()=>{let e=e=>{"F11"===e.key&&(e.preventDefault(),V())};return window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e)}},[]);let X=(0,n.jsxs)("div",{children:[(0,n.jsx)(l.A,{sx:{justifyContent:"center"},children:(0,n.jsx)(o.A,{variant:"h6",component:"div",sx:{fontWeight:"bold"},children:"EvoSpace"})}),(0,n.jsx)(i.A,{}),(0,n.jsx)(a.A,{children:G.map(e=>(0,n.jsx)(c.Ay,{disablePadding:!0,children:(0,n.jsx)(R(),{href:e.path,style:{textDecoration:"none",width:"100%",color:"inherit"},children:(0,n.jsxs)(d.A,{selected:E===e.path,children:[(0,n.jsx)(h.A,{children:e.icon}),(0,n.jsx)(x.A,{primary:e.text})]})})},e.text))})]});return(0,n.jsxs)(u.A,{sx:{display:"flex"},children:[(0,n.jsx)(m.Ay,{}),!K&&(0,n.jsx)(p.A,{position:"fixed",sx:{width:{sm:"calc(100% - ".concat(240,"px)")},ml:{sm:"".concat(240,"px")}},children:(0,n.jsxs)(l.A,{children:[(0,n.jsx)(b.A,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:Q,sx:{mr:2,display:{sm:"none"}},children:(0,n.jsx)(P.A,{})}),(0,n.jsx)(o.A,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:(null==(t=G.find(e=>e.path===E))?void 0:t.text)||"EvoSpace"}),M&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(b.A,{color:"inherit",onClick:V,sx:{ml:1},children:C?(0,n.jsx)(_.A,{}):(0,n.jsx)(z.A,{})}),(0,n.jsx)(b.A,{color:"inherit",onClick:()=>{J()},sx:{ml:1},children:"dark"===B?(0,n.jsx)(L.A,{}):(0,n.jsx)(F.A,{})}),(0,n.jsx)(b.A,{onClick:e=>{y(e.currentTarget)},size:"small",sx:{ml:2},"aria-controls":"menu-appbar","aria-haspopup":"true",children:(0,n.jsx)(f.A,{sx:{width:32,height:32},children:M.name.charAt(0)})}),(0,n.jsxs)(j.A,{id:"menu-appbar",anchorEl:w,anchorOrigin:{vertical:"bottom",horizontal:"right"},keepMounted:!0,transformOrigin:{vertical:"top",horizontal:"right"},open:!!w,onClose:U,children:[(0,n.jsxs)(A.A,{onClick:U,children:[(0,n.jsx)(h.A,{children:(0,n.jsx)(D.A,{fontSize:"small"})}),(0,n.jsx)(x.A,{children:"Profile"})]}),(0,n.jsxs)(A.A,{onClick:()=>{I(),U()},children:[(0,n.jsx)(h.A,{children:(0,n.jsx)(O.A,{fontSize:"small"})}),(0,n.jsx)(x.A,{children:"Logout"})]})]})]})]})}),!K&&(0,n.jsxs)(u.A,{component:"nav",sx:{width:{sm:240},flexShrink:{sm:0}},"aria-label":"mailbox folders",children:[(0,n.jsx)(v.Ay,{variant:"temporary",open:k,onClose:Q,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",sm:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:240}},children:X}),(0,n.jsx)(v.Ay,{variant:"permanent",sx:{display:{xs:"none",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:240}},open:!0,children:X})]}),(0,n.jsxs)(u.A,{component:"main",sx:{flexGrow:1,p:3,width:K?"100%":{sm:"calc(100% - ".concat(240,"px)")}},children:[!K&&(0,n.jsx)(l.A,{})," ",r]})]})}},78969:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,62093,23)),Promise.resolve().then(r.t.bind(r,27735,23)),Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.bind(r,43304)),Promise.resolve().then(r.bind(r,90038))},90038:(e,t,r)=>{"use strict";r.d(t,{W:()=>x,default:()=>m});var n=r(95155),s=r(12115),l=r(25041),o=r(35695),i=r(64453),a=r(29839),c=r(33196),d=r(22548);let h=s.createContext({mode:"light",toggleTheme:()=>{}}),x=()=>s.useContext(h),u=e=>(0,a.A)({palette:{mode:e,primary:{main:"#1976d2"},secondary:{main:"#dc004e"},background:{default:"light"===e?"#f5f5f5":"#121212",paper:"light"===e?"#ffffff":"#1e1e1e"}},typography:{fontFamily:"var(--font-geist-sans)"},components:{MuiCssBaseline:{styleOverrides:{body:{scrollbarColor:"dark"===e?"#6b6b6b #2b2b2b":"#959595 #f5f5f5","&::-webkit-scrollbar, & *::-webkit-scrollbar":{width:"8px",height:"8px"},"&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb":{borderRadius:8,backgroundColor:"dark"===e?"#6b6b6b":"#959595"},"&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track":{borderRadius:8,backgroundColor:"dark"===e?"#2b2b2b":"#f5f5f5"}}}}}});function m(e){let{children:t}=e,[r,a]=s.useState("light"),x=s.useMemo(()=>u(r),[r]);s.useEffect(()=>{var e,t,r,n,s;let l=null==(t=localStorage)||null==(e=t.getItem)?void 0:e.call(t,"themeMode");l&&("light"===l||"dark"===l)?a(l):(null==(s=window)||null==(n=s.matchMedia)||null==(r=n.call(s,"(prefers-color-scheme: dark)"))?void 0:r.matches)&&a("dark")},[]);let m=s.useCallback(()=>{a(e=>{var t,r;let n="light"===e?"dark":"light";return null==(r=localStorage)||null==(t=r.setItem)||t.call(r,"themeMode",n),n})},[]),p=s.useMemo(()=>({mode:r,toggleTheme:m}),[r,m]),[{cache:b,flush:f}]=s.useState(()=>{let e=(0,l.A)({key:"mui"});e.compat=!0;let t=e.insert,r=[];return e.insert=function(){for(var n=arguments.length,s=Array(n),l=0;l<n;l++)s[l]=arguments[l];let o=s[1];return void 0===e.inserted[o.name]&&r.push(o.name),t(...s)},{cache:e,flush:()=>{let e=r;return r=[],e}}});return(0,o.useServerInsertedHTML)(()=>{let e=f();if(0===e.length)return null;let t="";for(let r of e)t+=b.inserted[r];return(0,n.jsx)("style",{"data-emotion":"".concat(b.key," ").concat(e.join(" ")),dangerouslySetInnerHTML:{__html:t}},"emotion-style")}),(0,n.jsx)(i.C,{value:b,children:(0,n.jsx)(h.Provider,{value:p,children:(0,n.jsxs)(c.A,{theme:x,children:[(0,n.jsx)(d.Ay,{}),t]})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[360,319,692,221,159,722,730,441,684,358],()=>t(78969)),_N_E=e.O()}]);