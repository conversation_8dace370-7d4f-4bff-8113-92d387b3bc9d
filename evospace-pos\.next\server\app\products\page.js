(()=>{var e={};e.id=571,e.ids=[571],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3297:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(23428),a=r(60687);let o=(0,s.A)((0,a.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"Add")},4793:(e,t,r)=>{Promise.resolve().then(r.bind(r,8547))},8547:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\yunsell\\\\evospace\\\\evospace-pos\\\\src\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\products\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29041:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(23428),a=r(60687);let o=(0,s.A)((0,a.jsx)("path",{d:"M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3m3-10H5V5h10z"}),"Save")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37860:(e,t,r)=>{"use strict";r.d(t,{D:()=>o}),r(60687);var s=r(43210);let a=(0,s.createContext)({mode:"light",toggleMode:()=>{},theme:"light",toggleTheme:()=>{}}),o=()=>(0,s.useContext)(a)},46380:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(23428),a=r(60687);let o=(0,s.A)((0,a.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit")},59692:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8547)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\products\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\yunsell\\evospace\\evospace-pos\\src\\app\\products\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75417:(e,t,r)=>{Promise.resolve().then(r.bind(r,78815))},78815:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var s=r(60687),a=r(43210),o=r(88931),n=r(87088),i=r(16184),l=r(51067),c=r(16393),d=r(51711),p=r(23789),x=r(52260),u=r(41629),h=r(47651),m=r(80986),g=r(70031),A=r(86862),j=r(11830),v=r(76533),f=r(82681),y=r(12362),b=r(24296),C=r(90764),w=r(9133),P=r(27674),k=r(3297),S=r(41896),I=r(46380),z=r(43323),W=r(24330),_=r(84122);let M=(0,r(23428).A)((0,s.jsx)("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2M8.5 13.5l2.5 3.01L14.5 12l4.5 6H5z"}),"Image");var q=r(29041),D=r(28840),R=r(37860),L=r(54534);function E(){let{products:e,categories:t,addProduct:r,updateProduct:E,deleteProduct:G,fetchProducts:F,authUser:H}=(0,D.A)(),[T,N]=(0,a.useState)(""),[V,B]=(0,a.useState)("all"),[$,U]=(0,a.useState)(!1),[K,O]=(0,a.useState)(!1),[X,Y]=(0,a.useState)({name:"",category:"",price:"",stock:"",image:""}),[J,Q]=(0,a.useState)(!1),[Z,ee]=(0,a.useState)(null),{theme:et}=(0,R.D)(),er=e.filter(e=>(e.name.toLowerCase().includes(T.toLowerCase())||e.category.toLowerCase().includes(T.toLowerCase()))&&("all"===V||e.category===V)),es=(e=!1,r)=>{e&&r?(Y({id:r.id,name:r.name,category:r.category,price:r.price.toString(),stock:r.stock.toString(),image:r.image||""}),O(!0)):(Y({name:"",category:t[0]?.name||"",price:"",stock:"",image:""}),O(!1)),U(!0)},ea=()=>{U(!1)},eo=e=>{let{name:t,value:r}=e.target;Y(e=>({...e,[t]:r}))},en=async()=>{let e={name:X.name,category:X.category,price:parseFloat(X.price),stock:parseInt(X.stock),image:X.image};try{K&&void 0!==X.id?await E(X.id,e):await r(e),ea()}catch(e){console.error("Failed to save product:",e)}},ei=e=>{ee(e),Q(!0)},el=async()=>{if(null!==Z)try{await G(Z),Q(!1),ee(null)}catch(e){console.error("Failed to delete product:",e)}};return(0,s.jsxs)(o.A,{sx:{flexGrow:1},children:[(0,s.jsxs)(o.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,s.jsx)(n.A,{variant:"h4",children:"Products"}),(0,s.jsx)(i.A,{variant:"contained",startIcon:(0,s.jsx)(k.A,{}),onClick:()=>es(),children:"Add Product"})]}),(0,s.jsx)(l.A,{sx:{p:2,mb:3},children:(0,s.jsx)(o.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:2},children:(0,s.jsxs)(o.A,{sx:{display:"flex",gap:2,flexGrow:1,maxWidth:{xs:"100%",sm:"70%"}},children:[(0,s.jsx)(c.A,{placeholder:"Search products...",value:T,onChange:e=>N(e.target.value),InputProps:{startAdornment:(0,s.jsx)(d.A,{position:"start",children:(0,s.jsx)(S.A,{})})},fullWidth:!0}),(0,s.jsxs)(p.A,{sx:{minWidth:"200px"},children:[(0,s.jsx)(x.A,{children:"Category"}),(0,s.jsxs)(u.A,{value:V,label:"Category",onChange:e=>B(e.target.value),children:[(0,s.jsx)(h.A,{value:"all",children:"All Categories"}),Array.from(new Set(e.map(e=>e.category))).sort().map(e=>(0,s.jsx)(h.A,{value:e,children:e},e))]})]})]})})}),(0,s.jsx)(o.A,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:er.length>0?er.map(e=>(0,s.jsx)(o.A,{sx:{flexBasis:{xs:"100%",sm:"45%",md:"30%",lg:"22%"}},children:(0,s.jsxs)(m.A,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"transform 0.2s, box-shadow 0.2s","&:hover":{transform:"translateY(-4px)",boxShadow:6}},children:[(0,s.jsx)(g.A,{sx:{height:160,backgroundColor:e.image?"grey.200":"dark"===et?"primary.dark":"primary.main",color:"white",display:"flex",alignItems:"center",justifyContent:"center",position:"relative"},children:e.image?(0,s.jsx)(o.A,{component:"img",sx:{height:140,width:"100%",objectFit:"cover",borderRadius:1},src:e.image||"/placeholder.jpg",alt:e.name}):(0,s.jsx)(o.A,{sx:{textAlign:"center",p:2,width:"100%",height:"100%",display:"flex",flexDirection:"column",justifyContent:"center"},children:(0,s.jsx)(n.A,{variant:"h5",component:"div",sx:{fontWeight:"bold",textShadow:"1px 1px 3px rgba(0,0,0,0.5)",mb:1},children:e.name})})}),(0,s.jsxs)(A.A,{sx:{flexGrow:1,display:"flex",flexDirection:"column",justifyContent:"space-between"},children:[(0,s.jsxs)(o.A,{children:[(0,s.jsx)(n.A,{gutterBottom:!0,variant:"h6",component:"div",children:e.name}),(0,s.jsxs)(j.A,{direction:"row",spacing:1,sx:{mb:2,flexWrap:"wrap",gap:1},children:[(0,s.jsx)(v.A,{label:e.category,size:"small"}),(0,s.jsx)(v.A,{label:`Stock: ${e.stock}`,size:"small",color:e.stock>10?"success":e.stock>0?"warning":"error"})]})]}),(0,s.jsxs)(n.A,{variant:"h6",color:"primary",children:["$",Number(e.price).toFixed(2)]})]}),(0,s.jsxs)(f.A,{children:[(0,s.jsx)(i.A,{size:"small",startIcon:(0,s.jsx)(I.A,{}),onClick:()=>es(!0,e),children:"Edit"}),(0,s.jsx)(i.A,{size:"small",color:"error",startIcon:(0,s.jsx)(z.A,{}),onClick:()=>ei(e.id),children:"Delete"})]})]})},e.id)):(0,s.jsx)(o.A,{sx:{width:"100%",p:4,textAlign:"center"},children:(0,s.jsxs)(l.A,{sx:{p:4,borderRadius:2},children:[(0,s.jsx)(n.A,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"No products found"}),(0,s.jsx)(n.A,{variant:"body1",color:"text.secondary",children:"Try adjusting your search or category filter, or add a new product."}),(0,s.jsx)(i.A,{variant:"contained",startIcon:(0,s.jsx)(k.A,{}),sx:{mt:2},onClick:()=>es(),children:"Add Product"})]})})}),(0,s.jsxs)(y.A,{open:$,onClose:ea,maxWidth:"sm",fullWidth:!0,children:[(0,s.jsx)(b.A,{children:(0,s.jsxs)(o.A,{sx:{display:"flex",alignItems:"center",gap:1},children:[K?(0,s.jsx)(I.A,{color:"primary"}):(0,s.jsx)(k.A,{color:"primary"}),(0,s.jsx)(n.A,{variant:"h6",children:K?"Edit Product":"Add New Product"})]})}),(0,s.jsx)(C.A,{dividers:!0,children:(0,s.jsxs)(j.A,{spacing:3,sx:{mt:1},children:[(0,s.jsx)(c.A,{label:"Product Name",name:"name",value:X.name,onChange:eo,fullWidth:!0,required:!0,autoFocus:!0,InputProps:{startAdornment:(0,s.jsx)(d.A,{position:"start",children:(0,s.jsx)(W.A,{fontSize:"small"})})}}),(0,s.jsxs)(p.A,{fullWidth:!0,required:!0,children:[(0,s.jsx)(x.A,{children:"Category"}),(0,s.jsx)(u.A,{name:"category",value:X.category,label:"Category",onChange:e=>eo(e),children:t.map(e=>(0,s.jsx)(h.A,{value:e.name,children:e.name},e.id))}),(0,s.jsx)(w.A,{children:"Select a product category"})]}),(0,s.jsx)(c.A,{label:"Price",name:"price",type:"number",value:X.price,onChange:eo,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,s.jsx)(d.A,{position:"start",children:"$"})}}),(0,s.jsx)(c.A,{label:"Stock",name:"stock",type:"number",value:X.stock,onChange:eo,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,s.jsx)(d.A,{position:"start",children:(0,s.jsx)(_.A,{fontSize:"small"})})}}),(0,s.jsx)(c.A,{label:"Image URL",name:"image",value:X.image,onChange:eo,fullWidth:!0,placeholder:"https://example.com/image.jpg",helperText:"Leave empty to use product name as display",InputProps:{startAdornment:(0,s.jsx)(d.A,{position:"start",children:(0,s.jsx)(M,{fontSize:"small","aria-label":"Product image URL"})})}}),X.image&&(0,s.jsxs)(o.A,{sx:{width:"100%",textAlign:"center"},children:[(0,s.jsx)(o.A,{component:"img",src:X.image,alt:`Preview of ${X.name||"product"}`,sx:{maxWidth:"100%",maxHeight:"150px",objectFit:"contain",borderRadius:1,border:"1px solid",borderColor:"divider"},onError:e=>{e.target.src="/placeholder.jpg"}}),(0,s.jsx)(n.A,{variant:"caption",color:"text.secondary",display:"block",children:"Image Preview"})]})]})}),(0,s.jsxs)(P.A,{sx:{px:3,py:2},children:[(0,s.jsx)(i.A,{onClick:ea,color:"inherit",children:"Cancel"}),(0,s.jsx)(i.A,{onClick:en,variant:"contained",disabled:!X.name||!X.category||!X.price||!X.stock,startIcon:K?(0,s.jsx)(q.A,{}):(0,s.jsx)(k.A,{}),children:K?"Update":"Add"})]})]}),(0,s.jsx)(L.A,{open:J,title:"Confirm Delete",message:"Are you sure you want to delete this product? This action cannot be undone.",onConfirm:el,onCancel:()=>{Q(!1),ee(null)},confirmText:"Delete",confirmButtonColor:"error"})]})}},79551:e=>{"use strict";e.exports=require("url")},82681:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(43210),a=r(49384),o=r(99282),n=r(13555),i=r(84754),l=r(4144),c=r(82816);function d(e){return(0,c.Ay)("MuiCardActions",e)}(0,l.A)("MuiCardActions",["root","spacing"]);var p=r(60687);let x=e=>{let{classes:t,disableSpacing:r}=e;return(0,o.A)({root:["root",!r&&"spacing"]},d,t)},u=(0,n.Ay)("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,variants:[{props:{disableSpacing:!1},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),h=s.forwardRef(function(e,t){let r=(0,i.b)({props:e,name:"MuiCardActions"}),{disableSpacing:s=!1,className:o,...n}=r,l={...r,disableSpacing:s},c=x(l);return(0,p.jsx)(u,{className:(0,a.A)(c.root,o),ownerState:l,ref:t,...n})})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,991,619,111,117,575,154,79,98],()=>r(59692));module.exports=s})();