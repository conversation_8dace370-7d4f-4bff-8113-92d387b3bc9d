(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[246],{3784:(e,n,s)=>{"use strict";s.r(n),s.d(n,{default:()=>F});var l=s(95155),r=s(12115),i=s(54581),t=s(700),a=s(68534),o=s(18407),d=s(78449),c=s(33989),h=s(44296),x=s(17976),m=s(56033),A=s(90404),j=s(64263),u=s(99776),p=s(42663),b=s(3127),g=s(42515),y=s(36114),v=s(54492),C=s(99927),f=s(72705),w=s(71977),S=s(74964),k=s(40857),P=s(63954),E=s(83558),N=s(28890),W=s(19505),I=s(60807),M=s(2582),B=s(26290),T=s(2730),_=s(98648);function F(){let{members:e,addMember:n,updateMember:s,deleteMember:F,fetchMembers:D,authUser:L}=(0,T.A)(),[O,q]=(0,r.useState)(""),[z,H]=(0,r.useState)(!1),[V,U]=(0,r.useState)(!1),[$,G]=(0,r.useState)({name:"",email:"",phone:""}),[K,R]=(0,r.useState)(!1),[J,Q]=(0,r.useState)(null),[X,Y]=(0,r.useState)(0),[Z,ee]=(0,r.useState)(10),[en,es]=(0,r.useState)(!1),[el,er]=(0,r.useState)(null),[ei,et]=(0,r.useState)(!1),[ea,eo]=(0,r.useState)(null),ed=(Array.isArray(e)?e:[]).filter(e=>{var n,s,l;return(null!=(n=e.name)?n:"").toLowerCase().includes(O.toLowerCase())||(null!=(s=e.email)?s:"").toLowerCase().includes(O.toLowerCase())||(null!=(l=e.phone)?l:"").includes(O)});(0,r.useEffect)(()=>{L.token&&D().catch(e=>{console.error("Failed to fetch members on mount/auth change:",e),eo("Could not load members. Please try again later.")})},[D,L.token]);let ec=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1?arguments[1]:void 0;e&&n?(G({id:n.id,name:n.name,email:n.email,phone:n.phone}),U(!0)):(G({name:"",email:"",phone:""}),U(!1)),H(!0),eo(null)},eh=()=>{H(!1),eo(null)},ex=e=>{let{name:n,value:s}=e.target;G(e=>({...e,[n]:s}))},em=async()=>{et(!0),eo(null);try{V?$.id&&V&&await s($.id,{name:$.name,email:$.email,phone:$.phone}):await n({name:$.name,email:$.email,phone:$.phone}),eh()}catch(e){e instanceof Error?eo(e.message):eo("An unexpected error occurred.")}finally{et(!1)}},eA=e=>{Q(e),R(!0)},ej=e=>{er(e),es(!0)},eu=()=>{es(!1),er(null)};return(0,l.jsxs)(i.A,{sx:{flexGrow:1},children:[(0,l.jsxs)(i.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,l.jsx)(t.A,{variant:"h4",children:"Members"}),(0,l.jsx)(a.A,{variant:"contained",startIcon:(0,l.jsx)(k.A,{}),onClick:()=>ec(),children:"Add Member"})]}),(0,l.jsx)(o.A,{sx:{p:2,mb:3},children:(0,l.jsx)(d.A,{fullWidth:!0,placeholder:"Search members by name, email, or phone...",value:O,onChange:e=>q(e.target.value),InputProps:{startAdornment:(0,l.jsx)(c.A,{position:"start",children:(0,l.jsx)(P.A,{})})}})}),(0,l.jsxs)(o.A,{sx:{width:"100%",mb:2},children:[(0,l.jsx)(h.A,{children:(0,l.jsxs)(x.A,{sx:{minWidth:650},"aria-label":"members table",children:[(0,l.jsx)(m.A,{children:(0,l.jsxs)(A.A,{children:[(0,l.jsx)(j.A,{children:"Name"}),(0,l.jsx)(j.A,{children:"Email"}),(0,l.jsx)(j.A,{children:"Phone"}),(0,l.jsx)(j.A,{children:"Visits"}),(0,l.jsx)(j.A,{children:"Total Spent"}),(0,l.jsx)(j.A,{align:"right",children:"Actions"})]})}),(0,l.jsxs)(u.A,{children:[ed.slice(X*Z,X*Z+Z).map(e=>(0,l.jsxs)(A.A,{children:[(0,l.jsx)(j.A,{component:"th",scope:"row",children:(0,l.jsxs)(i.A,{sx:{display:"flex",alignItems:"center"},children:[(0,l.jsx)(p.A,{sx:{mr:2,bgcolor:"primary.main"},children:e.name&&e.name.length>0?e.name.charAt(0):"?"}),e.name]})}),(0,l.jsx)(j.A,{children:e.email}),(0,l.jsx)(j.A,{children:e.phone}),(0,l.jsx)(j.A,{children:e.visits}),(0,l.jsxs)(j.A,{children:["$",Number(e.totalSpent).toFixed(2)]}),(0,l.jsxs)(j.A,{align:"right",children:[(0,l.jsx)(b.A,{size:"small",onClick:()=>ej(e),title:"View History",children:(0,l.jsx)(E.A,{})}),(0,l.jsx)(b.A,{size:"small",onClick:()=>ec(!0,e),title:"Edit Member",children:(0,l.jsx)(N.A,{})}),(0,l.jsx)(b.A,{size:"small",color:"error",onClick:()=>eA(e.id),title:"Delete Member",children:(0,l.jsx)(W.A,{})})]})]},e.id)),0===ed.length&&(0,l.jsx)(A.A,{children:(0,l.jsx)(j.A,{colSpan:6,align:"center",children:(0,l.jsx)(t.A,{variant:"body1",sx:{py:2},children:"No members found"})})})]})]})}),(0,l.jsx)(g.A,{rowsPerPageOptions:[5,10,25],component:"div",count:ed.length,rowsPerPage:Z,page:X,onPageChange:(e,n)=>{Y(n)},onRowsPerPageChange:e=>{ee(parseInt(e.target.value,10)),Y(0)}})]}),(0,l.jsxs)(y.A,{open:z,onClose:eh,maxWidth:"sm",fullWidth:!0,children:[(0,l.jsx)(v.A,{children:V?"Edit Member":"Add New Member"}),(0,l.jsx)(C.A,{children:(0,l.jsxs)(f.A,{spacing:3,sx:{mt:1},children:[ea&&(0,l.jsx)(t.A,{color:"error",textAlign:"center",children:ea}),(0,l.jsx)(d.A,{autoFocus:!0,label:"Full Name",name:"name",value:$.name,onChange:ex,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,l.jsx)(c.A,{position:"start",children:(0,l.jsx)(I.A,{})})}}),(0,l.jsx)(d.A,{label:"Email Address",name:"email",type:"email",value:$.email,onChange:ex,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,l.jsx)(c.A,{position:"start",children:(0,l.jsx)(M.A,{})})}}),(0,l.jsx)(d.A,{label:"Phone Number",name:"phone",value:$.phone,onChange:ex,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,l.jsx)(c.A,{position:"start",children:(0,l.jsx)(B.A,{})})}})]})}),(0,l.jsxs)(w.A,{children:[(0,l.jsx)(a.A,{onClick:eh,children:"Cancel"}),(0,l.jsx)(a.A,{onClick:em,variant:"contained",disabled:!$.name||!$.email||!$.phone||ei,children:ei?V?"Updating...":"Adding...":V?"Update":"Add"})]})]}),(0,l.jsx)(_.A,{open:K,title:"Confirm Delete",message:"Are you sure you want to delete this member? This action cannot be undone.",onConfirm:()=>{null!==J&&F(J),R(!1),Q(null)},onCancel:()=>{R(!1),Q(null)},confirmText:"Delete",confirmButtonColor:"error"}),(0,l.jsxs)(y.A,{open:en,onClose:eu,maxWidth:"md",fullWidth:!0,children:[(0,l.jsxs)(v.A,{children:["Member History",el&&(0,l.jsx)(t.A,{variant:"subtitle1",color:"text.secondary",children:el.name})]}),(0,l.jsx)(C.A,{dividers:!0,children:el&&(0,l.jsxs)(i.A,{children:[(0,l.jsxs)(i.A,{sx:{mb:3},children:[(0,l.jsx)(t.A,{variant:"h6",gutterBottom:!0,children:"Member Details"}),(0,l.jsxs)(i.A,{sx:{display:"flex",flexWrap:"wrap",gap:2},children:[(0,l.jsxs)(i.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:[(0,l.jsx)(t.A,{variant:"body2",color:"text.secondary",children:"Email"}),(0,l.jsx)(t.A,{variant:"body1",children:el.email})]}),(0,l.jsxs)(i.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:[(0,l.jsx)(t.A,{variant:"body2",color:"text.secondary",children:"Phone"}),(0,l.jsx)(t.A,{variant:"body1",children:el.phone})]}),(0,l.jsxs)(i.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:[(0,l.jsx)(t.A,{variant:"body2",color:"text.secondary",children:"Total Visits"}),(0,l.jsx)(t.A,{variant:"body1",children:el.visits})]})]})]}),(0,l.jsx)(S.A,{sx:{my:2}}),(0,l.jsx)(t.A,{variant:"h6",gutterBottom:!0,children:"Purchase History"}),(0,l.jsxs)(t.A,{variant:"body1",color:"text.secondary",sx:{mb:2},children:["Total Spent: $",Number(el.totalSpent).toFixed(2)]}),(0,l.jsx)(o.A,{variant:"outlined",sx:{p:2,bgcolor:"background.default"},children:(0,l.jsx)(t.A,{variant:"body2",color:"text.secondary",align:"center",children:"Transaction history will be displayed here when connected to a backend."})})]})}),(0,l.jsx)(w.A,{children:(0,l.jsx)(a.A,{onClick:eu,children:"Close"})})]})]})}},54288:(e,n,s)=>{Promise.resolve().then(s.bind(s,3784))},98648:(e,n,s)=>{"use strict";s.d(n,{A:()=>c});var l=s(95155);s(12115);var r=s(36114),i=s(54492),t=s(99927),a=s(700),o=s(71977),d=s(68534);let c=e=>{let{open:n,title:s,message:c,onConfirm:h,onCancel:x,infoMode:m=!1,confirmText:A,confirmButtonColor:j="primary"}=e;return(0,l.jsxs)(r.A,{open:n,onClose:x,children:[(0,l.jsx)(i.A,{children:s}),(0,l.jsx)(t.A,{children:(0,l.jsx)(a.A,{children:c})}),(0,l.jsxs)(o.A,{children:[!m&&(0,l.jsx)(d.A,{onClick:x,children:"Cancel"}),(0,l.jsx)(d.A,{onClick:h,variant:"contained",color:m?"primary":j,children:m?A||"OK":A||"Confirm"})]})]})}}},e=>{var n=n=>e(e.s=n);e.O(0,[319,692,317,687,700,221,425,536,730,441,684,358],()=>n(54288)),_N_E=e.O()}]);