"use client";

import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  Avatar,
  ListItemAvatar,
  Stack,
  Button,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  AttachMoney,
  TrendingUp,
  People,
  Room,
  ArrowUpward,
  ArrowDownward,
  AddShoppingCart,
  PersonAdd,
  Inventory,
  MoreVert,
  Warning,
  Refresh
} from '@mui/icons-material';
import { Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement
} from 'chart.js';
import { useTheme } from '@/lib/theme';
import usePosStore from '@/lib/store';
import ProtectedPage from '@/components/ProtectedPage';
import { reportService } from '@/lib/apiService';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement
);

interface DashboardData {
  daily_sales: number;
  monthly_revenue: number;
  sales_growth: number;
  orders_today: number;
  members_today: number;
  resource_utilization: number;
  recent_transactions: Array<{
    id: number;
    member: string;
    amount: number;
    time: string;
  }>;
  date: string;
}

interface SalesByCategoryData {
  categories: Array<{
    category: string;
    total_amount: number;
    transaction_count: number;
    percentage: number;
  }>;
}

interface ProductPerformanceData {
  top_products: Array<{
    id: number;
    name: string;
    category: string;
    quantity_sold: number;
    revenue: number;
    stock_level: number;
    stock_status: string;
  }>;
  low_stock_products: Array<{
    id: number;
    name: string;
    category: string;
    stock_level: number;
    stock_status: string;
  }>;
}

export default function Home() {
  const { products } = usePosStore();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // State for dashboard data
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [salesByCategoryData, setSalesByCategoryData] = useState<SalesByCategoryData | null>(null);
  const [productPerformanceData, setProductPerformanceData] = useState<ProductPerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const today = new Date();
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      const [dashboardResponse, salesByCategoryResponse, productPerformanceResponse] = await Promise.all([
        reportService.getDashboardSummary(),
        reportService.getSalesByCategory({
          start_date: thirtyDaysAgo.toISOString().split('T')[0],
          end_date: today.toISOString().split('T')[0]
        }),
        reportService.getProductPerformance({
          start_date: thirtyDaysAgo.toISOString().split('T')[0],
          end_date: today.toISOString().split('T')[0],
          limit: 5
        })
      ]);

      if (dashboardResponse.success) {
        setDashboardData(dashboardResponse.data);
      }

      if (salesByCategoryResponse.success) {
        setSalesByCategoryData(salesByCategoryResponse.data);
      }

      if (productPerformanceResponse.success) {
        setProductPerformanceData(productPerformanceResponse.data);
      }

      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  // Get low stock products from API data or fallback to store data
  const lowStockProducts = productPerformanceData?.low_stock_products ||
    products.filter(p => p.stock < 20).slice(0, 5).map(p => ({
      id: p.id,
      name: p.name,
      category: p.category || 'Uncategorized',
      stock_level: p.stock,
      stock_status: p.stock < 10 ? 'low' : 'in_stock'
    }));
  
  // Sales by category chart data
  const salesByCategoryChartData = {
    labels: salesByCategoryData?.categories.map(item => item.category) || [],
    datasets: [
      {
        label: 'Sales Amount',
        data: salesByCategoryData?.categories.map(item => item.total_amount) || [],
        backgroundColor: isDarkMode ? [
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 99, 132, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(75, 192, 192, 0.7)',
        ] : [
          'rgba(54, 162, 235, 0.6)',
          'rgba(255, 99, 132, 0.6)',
          'rgba(255, 206, 86, 0.6)',
          'rgba(75, 192, 192, 0.6)',
        ],
        borderColor: [
          'rgba(54, 162, 235, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Popular products chart data
  const popularProductsChartData = {
    labels: productPerformanceData?.top_products.map(product => product.name) || [],
    datasets: [
      {
        label: 'Units Sold',
        data: productPerformanceData?.top_products.map(product => product.quantity_sold) || [],
        backgroundColor: isDarkMode ? [
          'rgba(75, 192, 192, 0.7)',
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(255, 99, 132, 0.7)',
          'rgba(153, 102, 255, 0.7)',
        ] : [
          'rgba(75, 192, 192, 0.6)',
          'rgba(54, 162, 235, 0.6)',
          'rgba(255, 206, 86, 0.6)',
          'rgba(255, 99, 132, 0.6)',
          'rgba(153, 102, 255, 0.6)',
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(153, 102, 255, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  return (
    <ProtectedPage>
      <Box sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <Typography variant="h4">
              Dashboard
            </Typography>
            {lastUpdated && (
              <Typography variant="body2" color="text.secondary">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </Typography>
            )}
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={loading ? <CircularProgress size={16} /> : <Refresh />}
              onClick={fetchDashboardData}
              disabled={loading}
              size="small"
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<AddShoppingCart />}
              href="/pos"
            >
              New Sale
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        {/* Quick Actions */}
        <Paper 
          elevation={isDarkMode ? 2 : 1} 
          sx={{ 
            p: 2, 
            mb: 4, 
            bgcolor: isDarkMode ? 'background.paper' : '#fff',
            borderRadius: 2
          }}
        >
          <Typography variant="h6" gutterBottom>Quick Actions</Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
            <Button variant="outlined" startIcon={<PersonAdd />} href="/members">Add Member</Button>
            <Button variant="outlined" startIcon={<Inventory />} href="/products">Manage Inventory</Button>
            <Button variant="outlined" startIcon={<Room />} href="/resources">Resource Booking</Button>
          </Stack>
        </Paper>
        
        {/* Stats Cards */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 4 }}>
          <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
            <Card 
              elevation={isDarkMode ? 2 : 1} 
              sx={{ 
                borderLeft: '4px solid',
                borderColor: 'primary.main',
                bgcolor: isDarkMode ? 'rgba(0, 123, 255, 0.1)' : 'rgba(0, 123, 255, 0.05)',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 3
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <AttachMoney />
                  </Avatar>
                  <Box>
                    <Typography color="textSecondary" variant="body2">
                      Daily Sales
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="h5">
                        {loading ? (
                          <CircularProgress size={24} />
                        ) : (
                          `$${(dashboardData?.daily_sales || 0).toFixed(2)}`
                        )}
                      </Typography>
                      {!loading && dashboardData && (
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          ml: 1,
                          color: dashboardData.sales_growth > 0 ? 'success.main' : 'error.main'
                        }}>
                          {dashboardData.sales_growth > 0 ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />}
                          <Typography variant="body2" sx={{ ml: 0.5 }}>
                            {Math.abs(dashboardData.sales_growth)}%
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>
          
          <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
            <Card 
              elevation={isDarkMode ? 2 : 1} 
              sx={{ 
                borderLeft: '4px solid',
                borderColor: 'secondary.main',
                bgcolor: isDarkMode ? 'rgba(108, 117, 125, 0.1)' : 'rgba(108, 117, 125, 0.05)',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 3
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                    <TrendingUp />
                  </Avatar>
                  <Box>
                    <Typography color="textSecondary" variant="body2">
                      Monthly Revenue
                    </Typography>
                    <Typography variant="h5">
                      {loading ? (
                        <CircularProgress size={24} />
                      ) : (
                        `$${(dashboardData?.monthly_revenue || 0).toFixed(2)}`
                      )}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>
          
          <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
            <Card 
              elevation={isDarkMode ? 2 : 1} 
              sx={{ 
                borderLeft: '4px solid',
                borderColor: 'success.main',
                bgcolor: isDarkMode ? 'rgba(40, 167, 69, 0.1)' : 'rgba(40, 167, 69, 0.05)',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 3
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <People />
                  </Avatar>
                  <Box>
                    <Typography color="textSecondary" variant="body2">
                      Members Today
                    </Typography>
                    <Typography variant="h5">
                      {loading ? (
                        <CircularProgress size={24} />
                      ) : (
                        dashboardData?.members_today || 0
                      )}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>
          
          <Box sx={{ flex: '1 1 250px', minWidth: { xs: '100%', sm: 'calc(50% - 12px)', md: 'calc(25% - 18px)' } }}>
            <Card 
              elevation={isDarkMode ? 2 : 1} 
              sx={{ 
                borderLeft: '4px solid',
                borderColor: 'warning.main',
                bgcolor: isDarkMode ? 'rgba(255, 193, 7, 0.1)' : 'rgba(255, 193, 7, 0.05)',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 3
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <Room />
                  </Avatar>
                  <Box>
                    <Typography color="textSecondary" variant="body2">
                      Orders Today
                    </Typography>
                    <Typography variant="h5">
                      {loading ? (
                        <CircularProgress size={24} />
                      ) : (
                        dashboardData?.orders_today || 0
                      )}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>
        </Box>
        
        {/* Charts */}
        <Stack spacing={3}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <Box sx={{ flex: '1 1 auto', minWidth: { xs: '100%', md: 'calc(66.666% - 12px)' } }}>
              <Paper 
                elevation={isDarkMode ? 2 : 1} 
                sx={{ 
                  p: 3, 
                  borderRadius: 2,
                  bgcolor: isDarkMode ? 'background.paper' : '#fff'
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6">
                    Sales by Category
                  </Typography>
                  <Tooltip title="More options">
                    <IconButton size="small">
                      <MoreVert fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
                <Box sx={{ height: 300 }}>
                  {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <CircularProgress />
                    </Box>
                  ) : salesByCategoryChartData.labels.length > 0 ? (
                    <Bar
                      data={salesByCategoryChartData}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'top',
                            labels: {
                              color: isDarkMode ? '#fff' : '#666'
                            }
                          },
                          title: {
                            display: false,
                          },
                        },
                        scales: {
                          x: {
                            grid: {
                              color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                              color: isDarkMode ? '#fff' : '#666'
                            }
                          },
                          y: {
                            grid: {
                              color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                              color: isDarkMode ? '#fff' : '#666'
                            }
                          }
                        }
                      }}
                    />
                  ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <Typography variant="body2" color="text.secondary">
                        No sales data available
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Paper>
            </Box>
            
            <Box sx={{ flex: '1 1 auto', minWidth: { xs: '100%', md: 'calc(33.333% - 12px)' } }}>
              <Paper 
                elevation={isDarkMode ? 2 : 1} 
                sx={{ 
                  p: 3, 
                  borderRadius: 2,
                  bgcolor: isDarkMode ? 'background.paper' : '#fff'
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6">
                    Popular Products
                  </Typography>
                  <Tooltip title="More options">
                    <IconButton size="small">
                      <MoreVert fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
                <Box sx={{ height: 300 }}>
                  {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <CircularProgress />
                    </Box>
                  ) : popularProductsChartData.labels.length > 0 ? (
                    <Doughnut
                      data={popularProductsChartData}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'bottom',
                            labels: {
                              color: isDarkMode ? '#fff' : '#666'
                            }
                          },
                        },
                      }}
                    />
                  ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <Typography variant="body2" color="text.secondary">
                        No product data available
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Paper>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <Box sx={{ flex: '1 1 auto', minWidth: { xs: '100%', md: 'calc(50% - 12px)' } }}>
              <Paper 
                elevation={isDarkMode ? 2 : 1} 
                sx={{ 
                  p: 3, 
                  borderRadius: 2,
                  bgcolor: isDarkMode ? 'background.paper' : '#fff'
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6">
                    Recent Transactions
                  </Typography>
                  <Button size="small" href="/reports">View All</Button>
                </Box>
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                    <CircularProgress />
                  </Box>
                ) : dashboardData?.recent_transactions && dashboardData.recent_transactions.length > 0 ? (
                  <List>
                    {dashboardData.recent_transactions.map((transaction) => (
                      <React.Fragment key={transaction.id}>
                        <ListItem>
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: 'primary.main' }}>
                              <AttachMoney />
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={transaction.member}
                            secondary={transaction.time}
                          />
                          <Typography variant="body1" fontWeight="bold">
                            ${transaction.amount.toFixed(2)}
                          </Typography>
                        </ListItem>
                        <Divider variant="inset" component="li" />
                      </React.Fragment>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body1" color="text.secondary">
                      No recent transactions
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Box>
            
            <Box sx={{ flex: '1 1 auto', minWidth: { xs: '100%', md: 'calc(50% - 12px)' } }}>
              <Paper 
                elevation={isDarkMode ? 2 : 1} 
                sx={{ 
                  p: 3, 
                  borderRadius: 2,
                  bgcolor: isDarkMode ? 'background.paper' : '#fff'
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6">
                    Low Stock Items
                  </Typography>
                  <Button size="small" href="/products">Manage Inventory</Button>
                </Box>
                {lowStockProducts.length > 0 ? (
                  <List>
                    {lowStockProducts.slice(0, 5).map((product) => (
                      <ListItem key={product.id}>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: product.stock_level < 10 ? 'error.main' : 'warning.main' }}>
                            <Warning />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={product.name}
                          secondary={`Category: ${product.category}`}
                        />
                        <Chip
                          label={`${product.stock_level} left`}
                          color={product.stock_level < 10 ? "error" : "warning"}
                          size="small"
                        />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="body1" color="text.secondary">
                      All products are well-stocked
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Box>
          </Box>
        </Stack>
      </Box>
    </ProtectedPage>
  );
}
