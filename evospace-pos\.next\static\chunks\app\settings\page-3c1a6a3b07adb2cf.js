(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{35884:(e,s,a)=>{"use strict";a.d(s,{D:()=>t}),a(95155);var l=a(12115);let n=(0,l.createContext)({mode:"light",toggleMode:()=>{},theme:"light",toggleTheme:()=>{}}),t=()=>(0,l.useContext)(n)},72608:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var l=a(95155),n=a(12115),t=a(54581),i=a(700),r=a(68534),o=a(18407),x=a(49994),c=a(18096),d=a(41218),h=a(16632),A=a(74964),j=a(78449),m=a(4915),u=a(82449),g=a(42663),p=a(72705),f=a(33989),b=a(27088),v=a(76380),y=a(92302),k=a(63148),C=a(65528),w=a(39489),B=a(85222),D=a(35726),S=a(51935),W=a(55099),R=a(39194),M=a(60807),E=a(19672),F=a(2582),I=a(40117),P=a(96253),T=a(50086),L=a(35884);function z(e){let{children:s,value:a,index:n,...i}=e;return(0,l.jsx)("div",{role:"tabpanel",hidden:a!==n,id:"settings-tabpanel-".concat(n),"aria-labelledby":"settings-tab-".concat(n),...i,children:a===n&&(0,l.jsx)(t.A,{sx:{p:3},children:s})})}function N(){let[e,s]=(0,n.useState)(0),[a,N]=(0,n.useState)(!1),{theme:_,toggleTheme:G}=(0,L.D)(),[O,U]=(0,n.useState)({name:"EvoSpace Cafe",address:"123 Innovation Street, Tech City",phone:"(*************",email:"<EMAIL>",taxRate:"7.5",currency:"USD"}),[H,q]=(0,n.useState)({showLogo:!0,showTaxDetails:!0,addFooterMessage:!0,footerMessage:"Thank you for your business!",printAutomatically:!1,emailReceipt:!0}),[J,K]=(0,n.useState)({name:"Administrator",email:"<EMAIL>",role:"Administrator",language:"English",theme:"dark"===_?"Dark":"Light",notifications:!0}),Q=e=>{let{name:s,value:a}=e.target;U(e=>({...e,[s]:a}))},V=e=>{let{name:s,value:a,checked:l}=e.target,n="checkbox"===e.target.type?l:a;q(e=>({...e,[s]:n}))},X=e=>{let{name:s,value:a}=e.target,l=e.target instanceof HTMLInputElement&&"checkbox"===e.target.type?e.target.checked:a;K(e=>({...e,[s]:l})),"theme"===s&&("Dark"===a&&"light"===_||"Light"===a&&"dark"===_)&&G()};return(0,l.jsxs)(t.A,{sx:{flexGrow:1},children:[(0,l.jsxs)(t.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,l.jsx)(i.A,{variant:"h4",children:"Settings"}),(0,l.jsx)(r.A,{variant:"contained",startIcon:(0,l.jsx)(S.A,{}),onClick:()=>{N(!0)},children:"Save Settings"})]}),(0,l.jsxs)(o.A,{sx:{width:"100%"},children:[(0,l.jsxs)(x.A,{value:e,onChange:(e,a)=>{s(a)},"aria-label":"settings tabs",sx:{borderBottom:1,borderColor:"divider"},children:[(0,l.jsx)(c.A,{icon:(0,l.jsx)(W.A,{}),label:"Store"}),(0,l.jsx)(c.A,{icon:(0,l.jsx)(R.A,{}),label:"Receipt"}),(0,l.jsx)(c.A,{icon:(0,l.jsx)(M.A,{}),label:"User"}),(0,l.jsx)(c.A,{icon:(0,l.jsx)(E.A,{}),label:"Backup & Restore"})]}),(0,l.jsx)(z,{value:e,index:0,children:(0,l.jsxs)(t.A,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:[(0,l.jsx)(t.A,{sx:{flexBasis:{xs:"100%",md:"48%"}},children:(0,l.jsx)(d.A,{children:(0,l.jsxs)(h.A,{children:[(0,l.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Store Information"}),(0,l.jsx)(A.A,{sx:{mb:2}}),(0,l.jsxs)(t.A,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,l.jsx)(j.A,{label:"Store Name",name:"name",value:O.name,onChange:Q,fullWidth:!0}),(0,l.jsx)(j.A,{label:"Address",name:"address",value:O.address,onChange:Q,fullWidth:!0,multiline:!0,rows:2}),(0,l.jsx)(j.A,{label:"Phone",name:"phone",value:O.phone,onChange:Q,fullWidth:!0}),(0,l.jsx)(j.A,{label:"Email",name:"email",type:"email",value:O.email,onChange:Q,fullWidth:!0})]})]})})}),(0,l.jsx)(t.A,{sx:{flexBasis:{xs:"100%",md:"48%"}},children:(0,l.jsx)(d.A,{children:(0,l.jsxs)(h.A,{children:[(0,l.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Financial Settings"}),(0,l.jsx)(A.A,{sx:{mb:2}}),(0,l.jsxs)(t.A,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,l.jsx)(j.A,{label:"Tax Rate (%)",name:"taxRate",type:"number",value:O.taxRate,onChange:Q,fullWidth:!0}),(0,l.jsx)(j.A,{label:"Currency",name:"currency",value:O.currency,onChange:Q,fullWidth:!0})]})]})})})]})}),(0,l.jsx)(z,{value:e,index:1,children:(0,l.jsxs)(t.A,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:[(0,l.jsx)(t.A,{sx:{flexBasis:{xs:"100%",sm:"48%"}},children:(0,l.jsx)(d.A,{children:(0,l.jsxs)(h.A,{children:[(0,l.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Receipt Content"}),(0,l.jsx)(A.A,{sx:{mb:2}}),(0,l.jsxs)(t.A,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,l.jsx)(m.A,{control:(0,l.jsx)(u.A,{checked:H.showLogo,onChange:V,name:"showLogo"}),label:"Show Store Logo"}),(0,l.jsx)(m.A,{control:(0,l.jsx)(u.A,{checked:H.showTaxDetails,onChange:V,name:"showTaxDetails"}),label:"Show Tax Details"}),(0,l.jsx)(m.A,{control:(0,l.jsx)(u.A,{checked:H.addFooterMessage,onChange:V,name:"addFooterMessage"}),label:"Add Footer Message"}),H.addFooterMessage&&(0,l.jsx)(j.A,{label:"Footer Message",name:"footerMessage",value:H.footerMessage,onChange:V,fullWidth:!0,multiline:!0,rows:2})]})]})})}),(0,l.jsx)(t.A,{sx:{flexBasis:{xs:"100%",sm:"48%"}},children:(0,l.jsx)(d.A,{children:(0,l.jsxs)(h.A,{children:[(0,l.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Receipt Delivery"}),(0,l.jsx)(A.A,{sx:{mb:2}}),(0,l.jsxs)(t.A,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,l.jsx)(m.A,{control:(0,l.jsx)(u.A,{checked:H.printAutomatically,onChange:V,name:"printAutomatically"}),label:"Print Automatically"}),(0,l.jsx)(m.A,{control:(0,l.jsx)(u.A,{checked:H.emailReceipt,onChange:V,name:"emailReceipt"}),label:"Email Receipt to Member"})]})]})})})]})}),(0,l.jsx)(z,{value:e,index:2,children:(0,l.jsx)(d.A,{children:(0,l.jsxs)(h.A,{children:[(0,l.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"User Profile"}),(0,l.jsx)(A.A,{sx:{mb:2}}),(0,l.jsxs)(t.A,{sx:{display:"flex",flexDirection:{xs:"column",sm:"row"},gap:3,mb:4},children:[(0,l.jsxs)(t.A,{sx:{display:"flex",flexDirection:"column",alignItems:"center",gap:1},children:[(0,l.jsx)(g.A,{sx:{width:100,height:100,bgcolor:"primary.main",fontSize:"2rem"},children:J.name.charAt(0)}),(0,l.jsx)(r.A,{size:"small",children:"Change Photo"})]}),(0,l.jsx)(t.A,{sx:{flexGrow:1},children:(0,l.jsxs)(p.A,{spacing:2,children:[(0,l.jsx)(j.A,{label:"Name",name:"name",value:J.name,onChange:X,fullWidth:!0,InputProps:{startAdornment:(0,l.jsx)(f.A,{position:"start",children:(0,l.jsx)(M.A,{})})}}),(0,l.jsx)(j.A,{label:"Email",name:"email",type:"email",value:J.email,onChange:X,fullWidth:!0,InputProps:{startAdornment:(0,l.jsx)(f.A,{position:"start",children:(0,l.jsx)(F.A,{})})}})]})})]}),(0,l.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Preferences"}),(0,l.jsx)(A.A,{sx:{mb:2}}),(0,l.jsxs)(t.A,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,l.jsx)(d.A,{variant:"outlined",sx:{p:2},children:(0,l.jsxs)(t.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,l.jsxs)(t.A,{children:[(0,l.jsx)(i.A,{variant:"subtitle1",children:"Theme"}),(0,l.jsx)(i.A,{variant:"body2",color:"text.secondary",children:"Choose between light and dark theme"})]}),(0,l.jsxs)(t.A,{sx:{display:"flex",alignItems:"center",gap:1},children:[(0,l.jsx)(I.A,{color:"light"===_?"primary":"disabled"}),(0,l.jsx)(u.A,{checked:"dark"===_,onChange:()=>{G(),K(e=>({...e,theme:"light"===_?"Dark":"Light"}))},inputProps:{"aria-label":"theme toggle"}}),(0,l.jsx)(P.A,{color:"dark"===_?"primary":"disabled"})]})]})}),(0,l.jsx)(b.A,{fullWidth:!0,children:(0,l.jsxs)(j.A,{select:!0,label:"Language",name:"language",value:J.language,onChange:X,fullWidth:!0,children:[(0,l.jsx)(v.A,{value:"English",children:"English"}),(0,l.jsx)(v.A,{value:"Spanish",children:"Spanish"}),(0,l.jsx)(v.A,{value:"French",children:"French"}),(0,l.jsx)(v.A,{value:"German",children:"German"}),(0,l.jsx)(v.A,{value:"Chinese",children:"Chinese"})]})}),(0,l.jsx)(m.A,{control:(0,l.jsx)(u.A,{checked:!0===J.notifications,onChange:e=>X({target:{name:"notifications",type:"checkbox",checked:e.target.checked}})}),label:"Enable Notifications"})]})]})})}),(0,l.jsx)(z,{value:e,index:3,children:(0,l.jsx)(d.A,{children:(0,l.jsxs)(h.A,{children:[(0,l.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Backup & Restore"}),(0,l.jsx)(A.A,{sx:{mb:2}}),(0,l.jsx)(y.A,{severity:"info",sx:{mb:3},children:"Regular backups help protect your data. We recommend backing up your data at least once a week."}),(0,l.jsxs)(t.A,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:[(0,l.jsx)(t.A,{sx:{flexBasis:{xs:"100%",md:"48%"}},children:(0,l.jsx)(d.A,{variant:"outlined",children:(0,l.jsxs)(h.A,{children:[(0,l.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Backup Data"}),(0,l.jsx)(i.A,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Create a backup of all your POS data including products, members, and transactions."}),(0,l.jsx)(r.A,{variant:"contained",startIcon:(0,l.jsx)(E.A,{}),fullWidth:!0,children:"Create Backup"})]})})}),(0,l.jsx)(t.A,{sx:{flexBasis:{xs:"100%",md:"48%"}},children:(0,l.jsx)(d.A,{variant:"outlined",children:(0,l.jsxs)(h.A,{children:[(0,l.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Restore Data"}),(0,l.jsx)(i.A,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Restore your POS data from a previous backup file."}),(0,l.jsx)(r.A,{variant:"outlined",startIcon:(0,l.jsx)(T.A,{}),fullWidth:!0,children:"Upload Backup File"})]})})}),(0,l.jsxs)(t.A,{sx:{flexBasis:{xs:"100%"}},children:[(0,l.jsx)(i.A,{variant:"h6",gutterBottom:!0,children:"Backup History"}),(0,l.jsx)(o.A,{variant:"outlined",children:(0,l.jsxs)(k.A,{children:[(0,l.jsxs)(C.Ay,{children:[(0,l.jsx)(w.A,{children:(0,l.jsx)(E.A,{})}),(0,l.jsx)(B.A,{primary:"Full Backup",secondary:"April 28, 2025 - 10:30 AM"}),(0,l.jsx)(r.A,{size:"small",children:"Download"}),(0,l.jsx)(r.A,{size:"small",color:"error",children:"Delete"})]}),(0,l.jsx)(A.A,{}),(0,l.jsxs)(C.Ay,{children:[(0,l.jsx)(w.A,{children:(0,l.jsx)(E.A,{})}),(0,l.jsx)(B.A,{primary:"Full Backup",secondary:"April 21, 2025 - 09:15 AM"}),(0,l.jsx)(r.A,{size:"small",children:"Download"}),(0,l.jsx)(r.A,{size:"small",color:"error",children:"Delete"})]})]})})]})]})]})})})]}),(0,l.jsx)(D.A,{open:a,autoHideDuration:6e3,onClose:()=>{N(!1)},message:"Settings saved successfully"})]})}},77610:(e,s,a)=>{Promise.resolve().then(a.bind(a,72608))}},e=>{var s=s=>e(e.s=s);e.O(0,[319,692,317,687,221,13,159,726,372,441,684,358],()=>s(77610)),_N_E=e.O()}]);