(()=>{var e={};e.id=763,e.ids=[763],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3297:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(23428),i=r(60687);let n=(0,s.A)((0,i.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"Add")},8693:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>R});var s=r(60687),i=r(43210),n=r(88931),a=r(87088),o=r(16184),l=r(51067),c=r(16393),d=r(51711),p=r(23789),u=r(52260),x=r(41629),h=r(47651),v=r(80986),A=r(86862),m=r(76533),j=r(91176),f=r(82681),y=r(12362),g=r(24296),b=r(90764),C=r(11830),S=r(27674),w=r(3297),k=r(41896),M=r(23428);let P=(0,M.A)([(0,s.jsx)("path",{d:"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8"},"0"),(0,s.jsx)("path",{d:"M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"},"1")],"AccessTime"),z=(0,M.A)([(0,s.jsx)("path",{d:"m12 2-5.5 9h11z"},"0"),(0,s.jsx)("circle",{cx:"17.5",cy:"17.5",r:"4.5"},"1"),(0,s.jsx)("path",{d:"M3 13.5h8v8H3z"},"2")],"Category");var I=r(46380),W=r(43323);let _=(0,M.A)((0,s.jsx)("path",{d:"M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8zm2 16H8v-2h8zm0-4H8v-2h8zm-3-5V3.5L18.5 9z"}),"Description");var D=r(28840),L=r(54534);function R(){let{services:e,fetchServices:t,addService:r,updateService:M,deleteService:R,authUser:T}=(0,D.A)(),[q,E]=(0,i.useState)(""),[H,B]=(0,i.useState)(!1),[N,G]=(0,i.useState)(!1),[F,$]=(0,i.useState)({name:"",price:"",duration:"",unit:"",description:"",category:""}),[V,O]=(0,i.useState)(!1),[U,K]=(0,i.useState)(null),[X,Z]=(0,i.useState)("all"),[J,Q]=(0,i.useState)("all"),Y=(0,i.useMemo)(()=>Array.from(new Set(e.map(e=>e.category).filter(e=>!!e))).sort(),[e]),ee=e.filter(e=>{let t=e.name.toLowerCase().includes(q.toLowerCase())||e.description&&e.description.toLowerCase().includes(q.toLowerCase()),r=!0;"hourly"===X?r=!!e.duration:"per-unit"===X?r=!!e.unit:"fixed"===X&&(r=!e.duration&&!e.unit);let s="all"===J||e.category===J;return t&&r&&s}),et=(e=!1,t)=>{e&&t?($({id:t.id,name:t.name,price:t.price.toString(),duration:t.duration?.toString()||"",unit:t.unit||"",description:t.description||"",category:t.category||""}),G(!0)):($({name:"",price:"",duration:"",unit:"",description:"",category:""}),G(!1)),B(!0)},er=()=>{B(!1)},es=e=>{let{name:t,value:r}=e.target;$(e=>({...e,[t]:r}))},ei=async()=>{let e={name:F.name,price:parseFloat(F.price),duration:F.duration?parseInt(F.duration):void 0,unit:F.unit||void 0,description:F.description||void 0,category:F.category||void 0};try{if(N&&void 0!==F.id)await M(F.id,e);else{let{id:e,...t}=F;await r({name:t.name,price:parseFloat(t.price),duration:t.duration?parseInt(t.duration):void 0,unit:t.unit||void 0,description:t.description||"",category:t.category||void 0})}er()}catch(e){console.error("Failed to save service:",e)}},en=e=>{K(e),O(!0)},ea=async()=>{if(null!==U)try{await R(U),O(!1),K(null)}catch(e){console.error("Failed to delete service:",e)}};return(0,s.jsxs)(n.A,{sx:{flexGrow:1},children:[(0,s.jsxs)(n.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,s.jsx)(a.A,{variant:"h4",children:"Services"}),(0,s.jsx)(o.A,{variant:"contained",startIcon:(0,s.jsx)(w.A,{}),onClick:()=>et(),children:"Add Service"})]}),(0,s.jsx)(l.A,{sx:{p:2,mb:3},children:(0,s.jsxs)(n.A,{sx:{display:"flex",flexWrap:"wrap",gap:2},children:[(0,s.jsx)(n.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:(0,s.jsx)(c.A,{fullWidth:!0,placeholder:"Search services...",value:q,onChange:e=>E(e.target.value),InputProps:{startAdornment:(0,s.jsx)(d.A,{position:"start",children:(0,s.jsx)(k.A,{})})}})}),(0,s.jsx)(n.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:(0,s.jsxs)(p.A,{fullWidth:!0,children:[(0,s.jsx)(u.A,{id:"service-type-label",children:"Service Type"}),(0,s.jsxs)(x.A,{labelId:"service-type-label",value:X,label:"Service Type",onChange:e=>{Z(e.target.value)},children:[(0,s.jsx)(h.A,{value:"all",children:"All Services"}),(0,s.jsx)(h.A,{value:"hourly",children:"Hourly"}),(0,s.jsx)(h.A,{value:"fixed",children:"Fixed Price"}),(0,s.jsx)(h.A,{value:"per-unit",children:"Per Unit"})]})]})}),(0,s.jsx)(n.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:(0,s.jsxs)(p.A,{fullWidth:!0,children:[(0,s.jsx)(u.A,{id:"service-category-label",children:"Category"}),(0,s.jsxs)(x.A,{labelId:"service-category-label",value:J,label:"Category",onChange:e=>Q(e.target.value),children:[(0,s.jsx)(h.A,{value:"all",children:"All Categories"}),Y.map(e=>(0,s.jsx)(h.A,{value:e,children:e},e))]})]})})]})}),(0,s.jsx)(n.A,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:ee.length>0?ee.map(e=>(0,s.jsx)(n.A,{sx:{flexBasis:{xs:"100%",sm:"45%",md:"30%"}},children:(0,s.jsxs)(v.A,{elevation:2,children:[(0,s.jsxs)(A.A,{children:[(0,s.jsx)(a.A,{variant:"h6",gutterBottom:!0,children:e.name}),(0,s.jsxs)(n.A,{sx:{display:"flex",flexWrap:"wrap",gap:1,mb:2},children:[e.category&&(0,s.jsx)(m.A,{label:e.category,size:"small",color:"primary",variant:"outlined"}),e.duration&&(0,s.jsx)(m.A,{icon:(0,s.jsx)(P,{fontSize:"small"}),label:`${e.duration} min`,size:"small"}),e.unit&&(0,s.jsx)(m.A,{icon:(0,s.jsx)(z,{fontSize:"small"}),label:`Per ${e.unit}`,size:"small"})]}),(0,s.jsx)(a.A,{variant:"body2",color:"text.secondary",sx:{mb:2},children:e.description}),(0,s.jsx)(j.A,{sx:{mb:2}}),(0,s.jsx)(n.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:(0,s.jsxs)(a.A,{variant:"h6",color:"primary",children:["$",Number(e.price).toFixed(2)]})})]}),(0,s.jsxs)(f.A,{children:[(0,s.jsx)(o.A,{size:"small",startIcon:(0,s.jsx)(I.A,{}),onClick:()=>et(!0,e),children:"Edit"}),(0,s.jsx)(o.A,{size:"small",color:"error",startIcon:(0,s.jsx)(W.A,{}),onClick:()=>en(e.id),children:"Delete"})]})]})},e.id)):(0,s.jsx)(n.A,{sx:{width:"100%",p:4,textAlign:"center"},children:(0,s.jsxs)(l.A,{sx:{p:4,borderRadius:2},children:[(0,s.jsx)(a.A,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"No services found"}),(0,s.jsx)(a.A,{variant:"body1",color:"text.secondary",children:"Try adjusting your search or category filter, or add a new service."}),(0,s.jsx)(o.A,{variant:"contained",startIcon:(0,s.jsx)(w.A,{}),sx:{mt:2},onClick:()=>et(),children:"Add Service"})]})})}),(0,s.jsxs)(y.A,{open:H,onClose:er,maxWidth:"sm",fullWidth:!0,children:[(0,s.jsx)(g.A,{children:N?"Edit Service":"Add New Service"}),(0,s.jsx)(b.A,{children:(0,s.jsxs)(C.A,{spacing:2,sx:{mt:1},children:[(0,s.jsx)(c.A,{label:"Service Name",name:"name",value:F.name,onChange:es,fullWidth:!0,required:!0}),(0,s.jsx)(c.A,{label:"Price",name:"price",type:"number",value:F.price,onChange:es,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,s.jsx)(d.A,{position:"start",children:"$"})}}),(0,s.jsx)(c.A,{label:"Duration (minutes)",name:"duration",type:"number",value:F.duration,onChange:es,fullWidth:!0,helperText:"Leave empty for non-time-based services",InputProps:{startAdornment:(0,s.jsx)(d.A,{position:"start",children:(0,s.jsx)(P,{})})}}),(0,s.jsx)(c.A,{label:"Unit (e.g., 'per page', 'per person')",name:"unit",value:F.unit,onChange:es,fullWidth:!0,helperText:"Leave empty for fixed-price services",InputProps:{startAdornment:(0,s.jsx)(d.A,{position:"start",children:(0,s.jsx)(z,{})})}}),(0,s.jsx)(c.A,{label:"Description",name:"description",value:F.description,onChange:es,fullWidth:!0,multiline:!0,rows:3,InputProps:{startAdornment:(0,s.jsx)(d.A,{position:"start",children:(0,s.jsx)(_,{})})}}),(0,s.jsxs)(p.A,{fullWidth:!0,children:[(0,s.jsx)(u.A,{id:"service-category-label",children:"Category"}),(0,s.jsxs)(x.A,{labelId:"service-category-label",value:F.category,label:"Category",onChange:e=>$(t=>({...t,category:e.target.value})),children:[(0,s.jsx)(h.A,{value:"all",children:"All Categories"}),Y.map(e=>(0,s.jsx)(h.A,{value:e,children:e},e))]})]})]})}),(0,s.jsxs)(S.A,{children:[(0,s.jsx)(o.A,{onClick:er,children:"Cancel"}),(0,s.jsx)(o.A,{onClick:ei,variant:"contained",disabled:!F.name||!F.price,children:N?"Update":"Add"})]})]}),(0,s.jsx)(L.A,{open:V,title:"Confirm Delete",message:"Are you sure you want to delete this service? This action cannot be undone.",onConfirm:ea,onCancel:()=>{O(!1),K(null)},confirmText:"Delete",confirmButtonColor:"error"})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11830:(e,t,r)=>{"use strict";r.d(t,{A:()=>b});var s=r(43210),i=r(49384),n=r(43648),a=r(82816),o=r(99282);let l=(0,r(88316).Ay)();var c=r(32856),d=r(44018),p=r(30437),u=r(98896),x=r(27887),h=r(60687);let v=(0,p.A)(),A=l("div",{name:"MuiStack",slot:"Root"});function m(e){return(0,c.A)({props:e,name:"MuiStack",defaultTheme:v})}let j=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],f=({ownerState:e,theme:t})=>{let r={display:"flex",flexDirection:"column",...(0,u.NI)({theme:t},(0,u.kW)({values:e.direction,breakpoints:t.breakpoints.values}),e=>({flexDirection:e}))};if(e.spacing){let s=(0,x.LX)(t),i=Object.keys(t.breakpoints.values).reduce((t,r)=>(("object"==typeof e.spacing&&null!=e.spacing[r]||"object"==typeof e.direction&&null!=e.direction[r])&&(t[r]=!0),t),{}),a=(0,u.kW)({values:e.direction,base:i}),o=(0,u.kW)({values:e.spacing,base:i});"object"==typeof a&&Object.keys(a).forEach((e,t,r)=>{if(!a[e]){let s=t>0?a[r[t-1]]:"column";a[e]=s}}),r=(0,n.A)(r,(0,u.NI)({theme:t},o,(t,r)=>e.useFlexGap?{gap:(0,x._W)(s,t)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${j(r?a[r]:e.direction)}`]:(0,x._W)(s,t)}}))}return(0,u.iZ)(t.breakpoints,r)};var y=r(13555),g=r(84754);let b=function(e={}){let{createStyledComponent:t=A,useThemeProps:r=m,componentName:n="MuiStack"}=e,l=()=>(0,o.A)({root:["root"]},e=>(0,a.Ay)(n,e),{}),c=t(f);return s.forwardRef(function(e,t){let n=r(e),{component:a="div",direction:o="column",spacing:p=0,divider:u,children:x,className:v,useFlexGap:A=!1,...m}=(0,d.A)(n),j=l();return(0,h.jsx)(c,{as:a,ownerState:{direction:o,spacing:p,useFlexGap:A},ref:t,className:(0,i.A)(j.root,v),...m,children:u?function(e,t){let r=s.Children.toArray(e).filter(Boolean);return r.reduce((e,i,n)=>(e.push(i),n<r.length-1&&e.push(s.cloneElement(t,{key:`separator-${n}`})),e),[])}(x,u):x})})}({createStyledComponent:(0,y.Ay)("div",{name:"MuiStack",slot:"Root"}),useThemeProps:e=>(0,g.b)({props:e,name:"MuiStack"})})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24296:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(43210),i=r(49384),n=r(99282),a=r(87088),o=r(13555),l=r(84754),c=r(79222),d=r(44791),p=r(60687);let u=e=>{let{classes:t}=e;return(0,n.A)({root:["root"]},c.t,t)},x=(0,o.Ay)(a.A,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),h=s.forwardRef(function(e,t){let r=(0,l.b)({props:e,name:"MuiDialogTitle"}),{className:n,id:a,...o}=r,c=u(r),{titleId:h=a}=s.useContext(d.A);return(0,p.jsx)(x,{component:"h2",className:(0,i.A)(c.root,n),ownerState:r,ref:t,variant:"h6",id:a??h,...o})})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31331:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\yunsell\\\\evospace\\\\evospace-pos\\\\src\\\\app\\\\services\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\services\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},41896:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(23428),i=r(60687);let n=(0,s.A)((0,i.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14"}),"Search")},43323:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(23428),i=r(60687);let n=(0,s.A)((0,i.jsx)("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"}),"Delete")},46380:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(23428),i=r(60687);let n=(0,s.A)((0,i.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit")},51916:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),i=r(48088),n=r(88170),a=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,31331)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\services\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\yunsell\\evospace\\evospace-pos\\src\\app\\services\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/services/page",pathname:"/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},54534:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(60687);r(43210);var i=r(12362),n=r(24296),a=r(90764),o=r(87088),l=r(27674),c=r(16184);let d=({open:e,title:t,message:r,onConfirm:d,onCancel:p,infoMode:u=!1,confirmText:x,confirmButtonColor:h="primary"})=>(0,s.jsxs)(i.A,{open:e,onClose:p,children:[(0,s.jsx)(n.A,{children:t}),(0,s.jsx)(a.A,{children:(0,s.jsx)(o.A,{children:r})}),(0,s.jsxs)(l.A,{children:[!u&&(0,s.jsx)(c.A,{onClick:p,children:"Cancel"}),(0,s.jsx)(c.A,{onClick:d,variant:"contained",color:u?"primary":h,children:u?x||"OK":x||"Confirm"})]})]})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64985:(e,t,r)=>{Promise.resolve().then(r.bind(r,31331))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78137:(e,t,r)=>{Promise.resolve().then(r.bind(r,8693))},79551:e=>{"use strict";e.exports=require("url")},82681:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var s=r(43210),i=r(49384),n=r(99282),a=r(13555),o=r(84754),l=r(4144),c=r(82816);function d(e){return(0,c.Ay)("MuiCardActions",e)}(0,l.A)("MuiCardActions",["root","spacing"]);var p=r(60687);let u=e=>{let{classes:t,disableSpacing:r}=e;return(0,n.A)({root:["root",!r&&"spacing"]},d,t)},x=(0,a.Ay)("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,variants:[{props:{disableSpacing:!1},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),h=s.forwardRef(function(e,t){let r=(0,o.b)({props:e,name:"MuiCardActions"}),{disableSpacing:s=!1,className:n,...a}=r,l={...r,disableSpacing:s},c=u(l);return(0,p.jsx)(x,{className:(0,i.A)(c.root,n),ownerState:l,ref:t,...a})})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,991,619,111,117,575,154,79],()=>r(51916));module.exports=s})();