(()=>{var e={};e.id=662,e.ids=[662],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7982:(e,t,r)=>{Promise.resolve().then(r.bind(r,74063))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11830:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var a=r(43210),s=r(49384),o=r(43648),l=r(82816),n=r(99282);let i=(0,r(88316).Ay)();var c=r(32856),d=r(44018),p=r(30437),h=r(98896),u=r(27887),m=r(60687);let x=(0,p.A)(),A=i("div",{name:"MuiStack",slot:"Root"});function g(e){return(0,c.A)({props:e,name:"MuiStack",defaultTheme:x})}let v=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],f=({ownerState:e,theme:t})=>{let r={display:"flex",flexDirection:"column",...(0,h.NI)({theme:t},(0,h.kW)({values:e.direction,breakpoints:t.breakpoints.values}),e=>({flexDirection:e}))};if(e.spacing){let a=(0,u.LX)(t),s=Object.keys(t.breakpoints.values).reduce((t,r)=>(("object"==typeof e.spacing&&null!=e.spacing[r]||"object"==typeof e.direction&&null!=e.direction[r])&&(t[r]=!0),t),{}),l=(0,h.kW)({values:e.direction,base:s}),n=(0,h.kW)({values:e.spacing,base:s});"object"==typeof l&&Object.keys(l).forEach((e,t,r)=>{if(!l[e]){let a=t>0?l[r[t-1]]:"column";l[e]=a}}),r=(0,o.A)(r,(0,h.NI)({theme:t},n,(t,r)=>e.useFlexGap?{gap:(0,u._W)(a,t)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${v(r?l[r]:e.direction)}`]:(0,u._W)(a,t)}}))}return(0,h.iZ)(t.breakpoints,r)};var j=r(13555),b=r(84754);let y=function(e={}){let{createStyledComponent:t=A,useThemeProps:r=g,componentName:o="MuiStack"}=e,i=()=>(0,n.A)({root:["root"]},e=>(0,l.Ay)(o,e),{}),c=t(f);return a.forwardRef(function(e,t){let o=r(e),{component:l="div",direction:n="column",spacing:p=0,divider:h,children:u,className:x,useFlexGap:A=!1,...g}=(0,d.A)(o),v=i();return(0,m.jsx)(c,{as:l,ownerState:{direction:n,spacing:p,useFlexGap:A},ref:t,className:(0,s.A)(v.root,x),...g,children:h?function(e,t){let r=a.Children.toArray(e).filter(Boolean);return r.reduce((e,s,o)=>(e.push(s),o<r.length-1&&e.push(a.cloneElement(t,{key:`separator-${o}`})),e),[])}(u,h):u})})}({createStyledComponent:(0,j.Ay)("div",{name:"MuiStack",slot:"Root"}),useThemeProps:e=>(0,b.b)({props:e,name:"MuiStack"})})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19511:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(23428),s=r(60687);let o=(0,a.A)((0,s.jsx)("path",{d:"M20 4H4v2h16zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6zm-9 4H6v-4h6z"}),"Store")},29041:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(23428),s=r(60687);let o=(0,a.A)((0,s.jsx)("path",{d:"M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3m3-10H5V5h10z"}),"Save")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37860:(e,t,r)=>{"use strict";r.d(t,{D:()=>o}),r(60687);var a=r(43210);let s=(0,a.createContext)({mode:"light",toggleMode:()=>{},theme:"light",toggleTheme:()=>{}}),o=()=>(0,a.useContext)(s)},41434:(e,t,r)=>{"use strict";r.d(t,{A:()=>R});var a=r(43210),s=r(49384),o=r(99282),l=r(2899),n=r(13555),i=r(45258),c=r(84754),d=r(34414),p=r(61543),h=r(48285),u=r(51067),m=r(4144),x=r(82816);function A(e){return(0,x.Ay)("MuiAlert",e)}let g=(0,m.A)("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);var v=r(83685),f=r(23428),j=r(60687);let b=(0,f.A)((0,j.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),y=(0,f.A)((0,j.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),k=(0,f.A)((0,j.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),C=(0,f.A)((0,j.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),w=(0,f.A)((0,j.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close"),M=e=>{let{variant:t,color:r,severity:a,classes:s}=e,l={root:["root",`color${(0,p.A)(r||a)}`,`${t}${(0,p.A)(r||a)}`,`${t}`],icon:["icon"],message:["message"],action:["action"]};return(0,o.A)(l,A,s)},S=(0,n.Ay)(u.A,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],t[`${r.variant}${(0,p.A)(r.color||r.severity)}`]]}})((0,i.A)(({theme:e})=>{let t="light"===e.palette.mode?l.e$:l.a,r="light"===e.palette.mode?l.a:l.e$;return{...e.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(e.palette).filter((0,h.A)(["light"])).map(([a])=>({props:{colorSeverity:a,variant:"standard"},style:{color:e.vars?e.vars.palette.Alert[`${a}Color`]:t(e.palette[a].light,.6),backgroundColor:e.vars?e.vars.palette.Alert[`${a}StandardBg`]:r(e.palette[a].light,.9),[`& .${g.icon}`]:e.vars?{color:e.vars.palette.Alert[`${a}IconColor`]}:{color:e.palette[a].main}}})),...Object.entries(e.palette).filter((0,h.A)(["light"])).map(([r])=>({props:{colorSeverity:r,variant:"outlined"},style:{color:e.vars?e.vars.palette.Alert[`${r}Color`]:t(e.palette[r].light,.6),border:`1px solid ${(e.vars||e).palette[r].light}`,[`& .${g.icon}`]:e.vars?{color:e.vars.palette.Alert[`${r}IconColor`]}:{color:e.palette[r].main}}})),...Object.entries(e.palette).filter((0,h.A)(["dark"])).map(([t])=>({props:{colorSeverity:t,variant:"filled"},style:{fontWeight:e.typography.fontWeightMedium,...e.vars?{color:e.vars.palette.Alert[`${t}FilledColor`],backgroundColor:e.vars.palette.Alert[`${t}FilledBg`]}:{backgroundColor:"dark"===e.palette.mode?e.palette[t].dark:e.palette[t].main,color:e.palette.getContrastText(e.palette[t].main)}}}))]}})),$=(0,n.Ay)("div",{name:"MuiAlert",slot:"Icon"})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),B=(0,n.Ay)("div",{name:"MuiAlert",slot:"Message"})({padding:"8px 0",minWidth:0,overflow:"auto"}),P=(0,n.Ay)("div",{name:"MuiAlert",slot:"Action"})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),z={success:(0,j.jsx)(b,{fontSize:"inherit"}),warning:(0,j.jsx)(y,{fontSize:"inherit"}),error:(0,j.jsx)(k,{fontSize:"inherit"}),info:(0,j.jsx)(C,{fontSize:"inherit"})},R=a.forwardRef(function(e,t){let r=(0,c.b)({props:e,name:"MuiAlert"}),{action:a,children:o,className:l,closeText:n="Close",color:i,components:p={},componentsProps:h={},icon:u,iconMapping:m=z,onClose:x,role:A="alert",severity:g="success",slotProps:f={},slots:b={},variant:y="standard",...k}=r,C={...r,color:i,severity:g,variant:y,colorSeverity:i||g},R=M(C),L={slots:{closeButton:p.CloseButton,closeIcon:p.CloseIcon,...b},slotProps:{...h,...f}},[D,I]=(0,d.A)("root",{ref:t,shouldForwardComponentProp:!0,className:(0,s.A)(R.root,l),elementType:S,externalForwardedProps:{...L,...k},ownerState:C,additionalProps:{role:A,elevation:0}}),[T,W]=(0,d.A)("icon",{className:R.icon,elementType:$,externalForwardedProps:L,ownerState:C}),[F,E]=(0,d.A)("message",{className:R.message,elementType:B,externalForwardedProps:L,ownerState:C}),[H,N]=(0,d.A)("action",{className:R.action,elementType:P,externalForwardedProps:L,ownerState:C}),[O,_]=(0,d.A)("closeButton",{elementType:v.A,externalForwardedProps:L,ownerState:C}),[q,G]=(0,d.A)("closeIcon",{elementType:w,externalForwardedProps:L,ownerState:C});return(0,j.jsxs)(D,{...I,children:[!1!==u?(0,j.jsx)(T,{...W,children:u||m[g]||z[g]}):null,(0,j.jsx)(F,{...E,children:o}),null!=a?(0,j.jsx)(H,{...N,children:a}):null,null==a&&x?(0,j.jsx)(H,{...N,children:(0,j.jsx)(O,{size:"small","aria-label":n,title:n,color:"inherit",onClick:x,..._,children:(0,j.jsx)(q,{fontSize:"small",...G})})}):null]})})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69998:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=r(65239),s=r(48088),o=r(88170),l=r.n(o),n=r(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(t,i);let c={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74198)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\settings\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\yunsell\\evospace\\evospace-pos\\src\\app\\settings\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74063:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ef});var a=r(60687),s=r(43210),o=r(88931),l=r(87088),n=r(16184),i=r(51067),c=r(43291),d=r(62014),p=r(80986),h=r(86862),u=r(91176),m=r(16393),x=r(49384),A=r(99282),g=r(92829),v=r(13555),f=r(45258),j=r(84754),b=r(61543),y=r(4144),k=r(82816);function C(e){return(0,k.Ay)("MuiFormControlLabel",e)}let w=(0,y.A)("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]);var M=r(93487),S=r(34414);let $=e=>{let{classes:t,disabled:r,labelPlacement:a,error:s,required:o}=e,l={root:["root",r&&"disabled",`labelPlacement${(0,b.A)(a)}`,s&&"error",o&&"required"],label:["label",r&&"disabled"],asterisk:["asterisk",s&&"error"]};return(0,A.A)(l,C,t)},B=(0,v.Ay)("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{[`& .${w.label}`]:t.label},t.root,t[`labelPlacement${(0,b.A)(r.labelPlacement)}`]]}})((0,f.A)(({theme:e})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${w.disabled}`]:{cursor:"default"},[`& .${w.label}`]:{[`&.${w.disabled}`]:{color:(e.vars||e).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:e})=>"start"===e||"top"===e||"bottom"===e,style:{marginLeft:16}}]}))),P=(0,v.Ay)("span",{name:"MuiFormControlLabel",slot:"Asterisk"})((0,f.A)(({theme:e})=>({[`&.${w.error}`]:{color:(e.vars||e).palette.error.main}}))),z=s.forwardRef(function(e,t){let r=(0,j.b)({props:e,name:"MuiFormControlLabel"}),{checked:o,className:n,componentsProps:i={},control:c,disabled:d,disableTypography:p,inputRef:h,label:u,labelPlacement:m="end",name:A,onChange:v,required:f,slots:b={},slotProps:y={},value:k,...C}=r,w=(0,g.A)(),z=d??c.props.disabled??w?.disabled,R=f??c.props.required,L={disabled:z,required:R};["checked","name","onChange","value","inputRef"].forEach(e=>{void 0===c.props[e]&&void 0!==r[e]&&(L[e]=r[e])});let D=(0,M.A)({props:r,muiFormControl:w,states:["error"]}),I={...r,disabled:z,labelPlacement:m,required:R,error:D.error},T=$(I),W={slots:b,slotProps:{...i,...y}},[F,E]=(0,S.A)("typography",{elementType:l.A,externalForwardedProps:W,ownerState:I}),H=u;return null==H||H.type===l.A||p||(H=(0,a.jsx)(F,{component:"span",...E,className:(0,x.A)(T.label,E?.className),children:H})),(0,a.jsxs)(B,{className:(0,x.A)(T.root,n),ownerState:I,ref:t,...C,children:[s.cloneElement(c,L),R?(0,a.jsxs)("div",{children:[H,(0,a.jsxs)(P,{ownerState:I,"aria-hidden":!0,className:T.asterisk,children:[" ","*"]})]}):H]})});var R=r(2899),L=r(48285),D=r(5591),I=r(24924),T=r(30748);function W(e){return(0,k.Ay)("PrivateSwitchBase",e)}(0,y.A)("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);let F=e=>{let{classes:t,checked:r,disabled:a,edge:s}=e,o={root:["root",r&&"checked",a&&"disabled",s&&`edge${(0,b.A)(s)}`],input:["input"]};return(0,A.A)(o,W,t)},E=(0,v.Ay)(T.A)({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:e,ownerState:t})=>"start"===e&&"small"!==t.size,style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:e,ownerState:t})=>"end"===e&&"small"!==t.size,style:{marginRight:-12}}]}),H=(0,v.Ay)("input",{shouldForwardProp:D.A})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),N=s.forwardRef(function(e,t){let{autoFocus:r,checked:s,checkedIcon:o,defaultChecked:l,disabled:n,disableFocusRipple:i=!1,edge:c=!1,icon:d,id:p,inputProps:h,inputRef:u,name:m,onBlur:x,onChange:A,onFocus:v,readOnly:f,required:j=!1,tabIndex:b,type:y,value:k,slots:C={},slotProps:w={},...M}=e,[$,B]=(0,I.A)({controlled:s,default:!!l,name:"SwitchBase",state:"checked"}),P=(0,g.A)(),z=e=>{v&&v(e),P&&P.onFocus&&P.onFocus(e)},R=e=>{x&&x(e),P&&P.onBlur&&P.onBlur(e)},L=e=>{if(e.nativeEvent.defaultPrevented)return;let t=e.target.checked;B(t),A&&A(e,t)},D=n;P&&void 0===D&&(D=P.disabled);let T="checkbox"===y||"radio"===y,W={...e,checked:$,disabled:D,disableFocusRipple:i,edge:c},N=F(W),O={slots:C,slotProps:{input:h,...w}},[_,q]=(0,S.A)("root",{ref:t,elementType:E,className:N.root,shouldForwardComponentProp:!0,externalForwardedProps:{...O,component:"span",...M},getSlotProps:e=>({...e,onFocus:t=>{e.onFocus?.(t),z(t)},onBlur:t=>{e.onBlur?.(t),R(t)}}),ownerState:W,additionalProps:{centerRipple:!0,focusRipple:!i,disabled:D,role:void 0,tabIndex:null}}),[G,V]=(0,S.A)("input",{ref:u,elementType:H,className:N.input,externalForwardedProps:O,getSlotProps:e=>({...e,onChange:t=>{e.onChange?.(t),L(t)}}),ownerState:W,additionalProps:{autoFocus:r,checked:s,defaultChecked:l,disabled:D,id:T?p:void 0,name:m,readOnly:f,required:j,tabIndex:b,type:y,..."checkbox"===y&&void 0===k?{}:{value:k}}});return(0,a.jsxs)(_,{...q,children:[(0,a.jsx)(G,{...V}),$?o:d]})});function O(e){return(0,k.Ay)("MuiSwitch",e)}let _=(0,y.A)("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]),q=e=>{let{classes:t,edge:r,size:a,color:s,checked:o,disabled:l}=e,n={root:["root",r&&`edge${(0,b.A)(r)}`,`size${(0,b.A)(a)}`],switchBase:["switchBase",`color${(0,b.A)(s)}`,o&&"checked",l&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},i=(0,A.A)(n,O,t);return{...t,...i}},G=(0,v.Ay)("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.edge&&t[`edge${(0,b.A)(r.edge)}`],t[`size${(0,b.A)(r.size)}`]]}})({display:"inline-flex",width:58,height:38,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${_.thumb}`]:{width:16,height:16},[`& .${_.switchBase}`]:{padding:4,[`&.${_.checked}`]:{transform:"translateX(16px)"}}}}]}),V=(0,v.Ay)(N,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.switchBase,{[`& .${_.input}`]:t.input},"default"!==r.color&&t[`color${(0,b.A)(r.color)}`]]}})((0,f.A)(({theme:e})=>({position:"absolute",top:0,left:0,zIndex:1,color:e.vars?e.vars.palette.Switch.defaultColor:`${"light"===e.palette.mode?e.palette.common.white:e.palette.grey[300]}`,transition:e.transitions.create(["left","transform"],{duration:e.transitions.duration.shortest}),[`&.${_.checked}`]:{transform:"translateX(20px)"},[`&.${_.disabled}`]:{color:e.vars?e.vars.palette.Switch.defaultDisabledColor:`${"light"===e.palette.mode?e.palette.grey[100]:e.palette.grey[600]}`},[`&.${_.checked} + .${_.track}`]:{opacity:.5},[`&.${_.disabled} + .${_.track}`]:{opacity:e.vars?e.vars.opacity.switchTrackDisabled:`${"light"===e.palette.mode?.12:.2}`},[`& .${_.input}`]:{left:"-100%",width:"300%"}})),(0,f.A)(({theme:e})=>({"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,R.X4)(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(e.palette).filter((0,L.A)(["light"])).map(([t])=>({props:{color:t},style:{[`&.${_.checked}`]:{color:(e.vars||e).palette[t].main,"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,R.X4)(e.palette[t].main,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${_.disabled}`]:{color:e.vars?e.vars.palette.Switch[`${t}DisabledColor`]:`${"light"===e.palette.mode?(0,R.a)(e.palette[t].main,.62):(0,R.e$)(e.palette[t].main,.55)}`}},[`&.${_.checked} + .${_.track}`]:{backgroundColor:(e.vars||e).palette[t].main}}}))]}))),X=(0,v.Ay)("span",{name:"MuiSwitch",slot:"Track"})((0,f.A)(({theme:e})=>({height:"100%",width:"100%",borderRadius:7,zIndex:-1,transition:e.transitions.create(["opacity","background-color"],{duration:e.transitions.duration.shortest}),backgroundColor:e.vars?e.vars.palette.common.onBackground:`${"light"===e.palette.mode?e.palette.common.black:e.palette.common.white}`,opacity:e.vars?e.vars.opacity.switchTrack:`${"light"===e.palette.mode?.38:.3}`}))),U=(0,v.Ay)("span",{name:"MuiSwitch",slot:"Thumb"})((0,f.A)(({theme:e})=>({boxShadow:(e.vars||e).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}))),Z=s.forwardRef(function(e,t){let r=(0,j.b)({props:e,name:"MuiSwitch"}),{className:s,color:o="primary",edge:l=!1,size:n="medium",sx:i,slots:c={},slotProps:d={},...p}=r,h={...r,color:o,edge:l,size:n},u=q(h),m={slots:c,slotProps:d},[A,g]=(0,S.A)("root",{className:(0,x.A)(u.root,s),elementType:G,externalForwardedProps:m,ownerState:h,additionalProps:{sx:i}}),[v,f]=(0,S.A)("thumb",{className:u.thumb,elementType:U,externalForwardedProps:m,ownerState:h}),b=(0,a.jsx)(v,{...f}),[y,k]=(0,S.A)("track",{className:u.track,elementType:X,externalForwardedProps:m,ownerState:h});return(0,a.jsxs)(A,{...g,children:[(0,a.jsx)(V,{type:"checkbox",icon:b,checkedIcon:b,ref:t,ownerState:h,...p,classes:{...u,root:u.switchBase},slots:{...c.switchBase&&{root:c.switchBase},...c.input&&{input:c.input}},slotProps:{...d.switchBase&&{root:"function"==typeof d.switchBase?d.switchBase(h):d.switchBase},...d.input&&{input:"function"==typeof d.input?d.input(h):d.input}}}),(0,a.jsx)(y,{...k})]})});var K=r(12879),J=r(11830),Q=r(51711),Y=r(23789),ee=r(47651),et=r(41434),er=r(45525),ea=r(17181),es=r(53006),eo=r(93010),el=r(59985),en=r(29041),ei=r(19511),ec=r(23428);let ed=(0,ec.A)((0,a.jsx)("path",{d:"M18 17H6v-2h12zm0-4H6v-2h12zm0-4H6V7h12zM3 22l1.5-1.5L6 22l1.5-1.5L9 22l1.5-1.5L12 22l1.5-1.5L15 22l1.5-1.5L18 22l1.5-1.5L21 22V2l-1.5 1.5L18 2l-1.5 1.5L15 2l-1.5 1.5L12 2l-1.5 1.5L9 2 7.5 3.5 6 2 4.5 3.5 3 2z"}),"Receipt");var ep=r(19257);let eh=(0,ec.A)((0,a.jsx)("path",{d:"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M14 13v4h-4v-4H7l5-5 5 5z"}),"Backup");var eu=r(91270);let em=(0,ec.A)((0,a.jsx)("path",{d:"M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5M2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1m18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1M11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1m0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1M5.99 4.58c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0s.39-1.03 0-1.41zm12.37 12.37c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41zm1.06-10.96c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0zM7.05 18.36c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0z"}),"LightMode"),ex=(0,ec.A)((0,a.jsx)("path",{d:"M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9c0-.46-.04-.92-.1-1.36-.98 1.37-2.58 2.26-4.4 2.26-2.98 0-5.4-2.42-5.4-5.4 0-1.81.89-3.42 2.26-4.4-.44-.06-.9-.1-1.36-.1"}),"DarkMode"),eA=(0,ec.A)((0,a.jsx)("path",{d:"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M14 13v4h-4v-4H7l5-5 5 5z"}),"CloudUpload");var eg=r(37860);function ev(e){let{children:t,value:r,index:s,...l}=e;return(0,a.jsx)("div",{role:"tabpanel",hidden:r!==s,id:`settings-tabpanel-${s}`,"aria-labelledby":`settings-tab-${s}`,...l,children:r===s&&(0,a.jsx)(o.A,{sx:{p:3},children:t})})}function ef(){let[e,t]=(0,s.useState)(0),[r,x]=(0,s.useState)(!1),{theme:A,toggleTheme:g}=(0,eg.D)(),[v,f]=(0,s.useState)({name:"EvoSpace Cafe",address:"123 Innovation Street, Tech City",phone:"(*************",email:"<EMAIL>",taxRate:"7.5",currency:"USD"}),[j,b]=(0,s.useState)({showLogo:!0,showTaxDetails:!0,addFooterMessage:!0,footerMessage:"Thank you for your business!",printAutomatically:!1,emailReceipt:!0}),[y,k]=(0,s.useState)({name:"Administrator",email:"<EMAIL>",role:"Administrator",language:"English",theme:"dark"===A?"Dark":"Light",notifications:!0}),C=e=>{let{name:t,value:r}=e.target;f(e=>({...e,[t]:r}))},w=e=>{let{name:t,value:r,checked:a}=e.target,s="checkbox"===e.target.type?a:r;b(e=>({...e,[t]:s}))},M=e=>{let{name:t,value:r}=e.target,a=e.target instanceof HTMLInputElement&&"checkbox"===e.target.type?e.target.checked:r;k(e=>({...e,[t]:a})),"theme"===t&&("Dark"===r&&"light"===A||"Light"===r&&"dark"===A)&&g()};return(0,a.jsxs)(o.A,{sx:{flexGrow:1},children:[(0,a.jsxs)(o.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,a.jsx)(l.A,{variant:"h4",children:"Settings"}),(0,a.jsx)(n.A,{variant:"contained",startIcon:(0,a.jsx)(en.A,{}),onClick:()=>{x(!0)},children:"Save Settings"})]}),(0,a.jsxs)(i.A,{sx:{width:"100%"},children:[(0,a.jsxs)(c.A,{value:e,onChange:(e,r)=>{t(r)},"aria-label":"settings tabs",sx:{borderBottom:1,borderColor:"divider"},children:[(0,a.jsx)(d.A,{icon:(0,a.jsx)(ei.A,{}),label:"Store"}),(0,a.jsx)(d.A,{icon:(0,a.jsx)(ed,{}),label:"Receipt"}),(0,a.jsx)(d.A,{icon:(0,a.jsx)(ep.A,{}),label:"User"}),(0,a.jsx)(d.A,{icon:(0,a.jsx)(eh,{}),label:"Backup & Restore"})]}),(0,a.jsx)(ev,{value:e,index:0,children:(0,a.jsxs)(o.A,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:[(0,a.jsx)(o.A,{sx:{flexBasis:{xs:"100%",md:"48%"}},children:(0,a.jsx)(p.A,{children:(0,a.jsxs)(h.A,{children:[(0,a.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Store Information"}),(0,a.jsx)(u.A,{sx:{mb:2}}),(0,a.jsxs)(o.A,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,a.jsx)(m.A,{label:"Store Name",name:"name",value:v.name,onChange:C,fullWidth:!0}),(0,a.jsx)(m.A,{label:"Address",name:"address",value:v.address,onChange:C,fullWidth:!0,multiline:!0,rows:2}),(0,a.jsx)(m.A,{label:"Phone",name:"phone",value:v.phone,onChange:C,fullWidth:!0}),(0,a.jsx)(m.A,{label:"Email",name:"email",type:"email",value:v.email,onChange:C,fullWidth:!0})]})]})})}),(0,a.jsx)(o.A,{sx:{flexBasis:{xs:"100%",md:"48%"}},children:(0,a.jsx)(p.A,{children:(0,a.jsxs)(h.A,{children:[(0,a.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Financial Settings"}),(0,a.jsx)(u.A,{sx:{mb:2}}),(0,a.jsxs)(o.A,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,a.jsx)(m.A,{label:"Tax Rate (%)",name:"taxRate",type:"number",value:v.taxRate,onChange:C,fullWidth:!0}),(0,a.jsx)(m.A,{label:"Currency",name:"currency",value:v.currency,onChange:C,fullWidth:!0})]})]})})})]})}),(0,a.jsx)(ev,{value:e,index:1,children:(0,a.jsxs)(o.A,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:[(0,a.jsx)(o.A,{sx:{flexBasis:{xs:"100%",sm:"48%"}},children:(0,a.jsx)(p.A,{children:(0,a.jsxs)(h.A,{children:[(0,a.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Receipt Content"}),(0,a.jsx)(u.A,{sx:{mb:2}}),(0,a.jsxs)(o.A,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,a.jsx)(z,{control:(0,a.jsx)(Z,{checked:j.showLogo,onChange:w,name:"showLogo"}),label:"Show Store Logo"}),(0,a.jsx)(z,{control:(0,a.jsx)(Z,{checked:j.showTaxDetails,onChange:w,name:"showTaxDetails"}),label:"Show Tax Details"}),(0,a.jsx)(z,{control:(0,a.jsx)(Z,{checked:j.addFooterMessage,onChange:w,name:"addFooterMessage"}),label:"Add Footer Message"}),j.addFooterMessage&&(0,a.jsx)(m.A,{label:"Footer Message",name:"footerMessage",value:j.footerMessage,onChange:w,fullWidth:!0,multiline:!0,rows:2})]})]})})}),(0,a.jsx)(o.A,{sx:{flexBasis:{xs:"100%",sm:"48%"}},children:(0,a.jsx)(p.A,{children:(0,a.jsxs)(h.A,{children:[(0,a.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Receipt Delivery"}),(0,a.jsx)(u.A,{sx:{mb:2}}),(0,a.jsxs)(o.A,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,a.jsx)(z,{control:(0,a.jsx)(Z,{checked:j.printAutomatically,onChange:w,name:"printAutomatically"}),label:"Print Automatically"}),(0,a.jsx)(z,{control:(0,a.jsx)(Z,{checked:j.emailReceipt,onChange:w,name:"emailReceipt"}),label:"Email Receipt to Member"})]})]})})})]})}),(0,a.jsx)(ev,{value:e,index:2,children:(0,a.jsx)(p.A,{children:(0,a.jsxs)(h.A,{children:[(0,a.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"User Profile"}),(0,a.jsx)(u.A,{sx:{mb:2}}),(0,a.jsxs)(o.A,{sx:{display:"flex",flexDirection:{xs:"column",sm:"row"},gap:3,mb:4},children:[(0,a.jsxs)(o.A,{sx:{display:"flex",flexDirection:"column",alignItems:"center",gap:1},children:[(0,a.jsx)(K.A,{sx:{width:100,height:100,bgcolor:"primary.main",fontSize:"2rem"},children:y.name.charAt(0)}),(0,a.jsx)(n.A,{size:"small",children:"Change Photo"})]}),(0,a.jsx)(o.A,{sx:{flexGrow:1},children:(0,a.jsxs)(J.A,{spacing:2,children:[(0,a.jsx)(m.A,{label:"Name",name:"name",value:y.name,onChange:M,fullWidth:!0,InputProps:{startAdornment:(0,a.jsx)(Q.A,{position:"start",children:(0,a.jsx)(ep.A,{})})}}),(0,a.jsx)(m.A,{label:"Email",name:"email",type:"email",value:y.email,onChange:M,fullWidth:!0,InputProps:{startAdornment:(0,a.jsx)(Q.A,{position:"start",children:(0,a.jsx)(eu.A,{})})}})]})})]}),(0,a.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Preferences"}),(0,a.jsx)(u.A,{sx:{mb:2}}),(0,a.jsxs)(o.A,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,a.jsx)(p.A,{variant:"outlined",sx:{p:2},children:(0,a.jsxs)(o.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,a.jsxs)(o.A,{children:[(0,a.jsx)(l.A,{variant:"subtitle1",children:"Theme"}),(0,a.jsx)(l.A,{variant:"body2",color:"text.secondary",children:"Choose between light and dark theme"})]}),(0,a.jsxs)(o.A,{sx:{display:"flex",alignItems:"center",gap:1},children:[(0,a.jsx)(em,{color:"light"===A?"primary":"disabled"}),(0,a.jsx)(Z,{checked:"dark"===A,onChange:()=>{g(),k(e=>({...e,theme:"light"===A?"Dark":"Light"}))},inputProps:{"aria-label":"theme toggle"}}),(0,a.jsx)(ex,{color:"dark"===A?"primary":"disabled"})]})]})}),(0,a.jsx)(Y.A,{fullWidth:!0,children:(0,a.jsxs)(m.A,{select:!0,label:"Language",name:"language",value:y.language,onChange:M,fullWidth:!0,children:[(0,a.jsx)(ee.A,{value:"English",children:"English"}),(0,a.jsx)(ee.A,{value:"Spanish",children:"Spanish"}),(0,a.jsx)(ee.A,{value:"French",children:"French"}),(0,a.jsx)(ee.A,{value:"German",children:"German"}),(0,a.jsx)(ee.A,{value:"Chinese",children:"Chinese"})]})}),(0,a.jsx)(z,{control:(0,a.jsx)(Z,{checked:!0===y.notifications,onChange:e=>M({target:{name:"notifications",type:"checkbox",checked:e.target.checked}})}),label:"Enable Notifications"})]})]})})}),(0,a.jsx)(ev,{value:e,index:3,children:(0,a.jsx)(p.A,{children:(0,a.jsxs)(h.A,{children:[(0,a.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Backup & Restore"}),(0,a.jsx)(u.A,{sx:{mb:2}}),(0,a.jsx)(et.A,{severity:"info",sx:{mb:3},children:"Regular backups help protect your data. We recommend backing up your data at least once a week."}),(0,a.jsxs)(o.A,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:[(0,a.jsx)(o.A,{sx:{flexBasis:{xs:"100%",md:"48%"}},children:(0,a.jsx)(p.A,{variant:"outlined",children:(0,a.jsxs)(h.A,{children:[(0,a.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Backup Data"}),(0,a.jsx)(l.A,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Create a backup of all your POS data including products, members, and transactions."}),(0,a.jsx)(n.A,{variant:"contained",startIcon:(0,a.jsx)(eh,{}),fullWidth:!0,children:"Create Backup"})]})})}),(0,a.jsx)(o.A,{sx:{flexBasis:{xs:"100%",md:"48%"}},children:(0,a.jsx)(p.A,{variant:"outlined",children:(0,a.jsxs)(h.A,{children:[(0,a.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Restore Data"}),(0,a.jsx)(l.A,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Restore your POS data from a previous backup file."}),(0,a.jsx)(n.A,{variant:"outlined",startIcon:(0,a.jsx)(eA,{}),fullWidth:!0,children:"Upload Backup File"})]})})}),(0,a.jsxs)(o.A,{sx:{flexBasis:{xs:"100%"}},children:[(0,a.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Backup History"}),(0,a.jsx)(i.A,{variant:"outlined",children:(0,a.jsxs)(er.A,{children:[(0,a.jsxs)(ea.Ay,{children:[(0,a.jsx)(es.A,{children:(0,a.jsx)(eh,{})}),(0,a.jsx)(eo.A,{primary:"Full Backup",secondary:"April 28, 2025 - 10:30 AM"}),(0,a.jsx)(n.A,{size:"small",children:"Download"}),(0,a.jsx)(n.A,{size:"small",color:"error",children:"Delete"})]}),(0,a.jsx)(u.A,{}),(0,a.jsxs)(ea.Ay,{children:[(0,a.jsx)(es.A,{children:(0,a.jsx)(eh,{})}),(0,a.jsx)(eo.A,{primary:"Full Backup",secondary:"April 21, 2025 - 09:15 AM"}),(0,a.jsx)(n.A,{size:"small",children:"Download"}),(0,a.jsx)(n.A,{size:"small",color:"error",children:"Delete"})]})]})})]})]})]})})})]}),(0,a.jsx)(el.A,{open:r,autoHideDuration:6e3,onClose:()=>{x(!1)},message:"Settings saved successfully"})]})}},74198:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\yunsell\\\\evospace\\\\evospace-pos\\\\src\\\\app\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\settings\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},80986:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var a=r(43210),s=r(49384),o=r(99282),l=r(13555),n=r(84754),i=r(51067),c=r(4144),d=r(82816);function p(e){return(0,d.Ay)("MuiCard",e)}(0,c.A)("MuiCard",["root"]);var h=r(60687);let u=e=>{let{classes:t}=e;return(0,o.A)({root:["root"]},p,t)},m=(0,l.Ay)(i.A,{name:"MuiCard",slot:"Root"})({overflow:"hidden"}),x=a.forwardRef(function(e,t){let r=(0,n.b)({props:e,name:"MuiCard"}),{className:a,raised:o=!1,...l}=r,i={...r,raised:o},c=u(i);return(0,h.jsx)(m,{className:(0,s.A)(c.root,a),elevation:o?8:void 0,ref:t,ownerState:i,...l})})},86862:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var a=r(43210),s=r(49384),o=r(99282),l=r(13555),n=r(84754),i=r(4144),c=r(82816);function d(e){return(0,c.Ay)("MuiCardContent",e)}(0,i.A)("MuiCardContent",["root"]);var p=r(60687);let h=e=>{let{classes:t}=e;return(0,o.A)({root:["root"]},d,t)},u=(0,l.Ay)("div",{name:"MuiCardContent",slot:"Root"})({padding:16,"&:last-child":{paddingBottom:24}}),m=a.forwardRef(function(e,t){let r=(0,n.b)({props:e,name:"MuiCardContent"}),{className:a,component:o="div",...l}=r,i={...r,component:o},c=h(i);return(0,p.jsx)(u,{as:o,className:(0,s.A)(c.root,a),ownerState:i,ref:t,...l})})},89830:(e,t,r)=>{Promise.resolve().then(r.bind(r,74198))},91270:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(23428),s=r(60687);let o=(0,a.A)((0,s.jsx)("path",{d:"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"}),"Email")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,991,619,111,117,790,985,79],()=>r(69998));module.exports=a})();