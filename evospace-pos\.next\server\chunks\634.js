"use strict";exports.id=634,exports.ids=[634],exports.modules={41434:(e,t,n)=>{n.d(t,{A:()=>D});var r=n(43210),o=n(49384),i=n(99282),a=n(2899),s=n(13555),l=n(45258),f=n(84754),c=n(34414),p=n(61543),d=n(48285),u=n(51067),m=n(4144),h=n(82816);function v(e){return(0,h.Ay)("MuiAlert",e)}let g=(0,m.A)("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);var y=n(83685),b=n(23428),x=n(60687);let w=(0,b.A)((0,x.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),A=(0,b.A)((0,x.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),O=(0,b.A)((0,x.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),j=(0,b.A)((0,x.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),M=(0,b.A)((0,x.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close"),E=e=>{let{variant:t,color:n,severity:r,classes:o}=e,a={root:["root",`color${(0,p.A)(n||r)}`,`${t}${(0,p.A)(n||r)}`,`${t}`],icon:["icon"],message:["message"],action:["action"]};return(0,i.A)(a,v,o)},P=(0,s.Ay)(u.A,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t[n.variant],t[`${n.variant}${(0,p.A)(n.color||n.severity)}`]]}})((0,l.A)(({theme:e})=>{let t="light"===e.palette.mode?a.e$:a.a,n="light"===e.palette.mode?a.a:a.e$;return{...e.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(e.palette).filter((0,d.A)(["light"])).map(([r])=>({props:{colorSeverity:r,variant:"standard"},style:{color:e.vars?e.vars.palette.Alert[`${r}Color`]:t(e.palette[r].light,.6),backgroundColor:e.vars?e.vars.palette.Alert[`${r}StandardBg`]:n(e.palette[r].light,.9),[`& .${g.icon}`]:e.vars?{color:e.vars.palette.Alert[`${r}IconColor`]}:{color:e.palette[r].main}}})),...Object.entries(e.palette).filter((0,d.A)(["light"])).map(([n])=>({props:{colorSeverity:n,variant:"outlined"},style:{color:e.vars?e.vars.palette.Alert[`${n}Color`]:t(e.palette[n].light,.6),border:`1px solid ${(e.vars||e).palette[n].light}`,[`& .${g.icon}`]:e.vars?{color:e.vars.palette.Alert[`${n}IconColor`]}:{color:e.palette[n].main}}})),...Object.entries(e.palette).filter((0,d.A)(["dark"])).map(([t])=>({props:{colorSeverity:t,variant:"filled"},style:{fontWeight:e.typography.fontWeightMedium,...e.vars?{color:e.vars.palette.Alert[`${t}FilledColor`],backgroundColor:e.vars.palette.Alert[`${t}FilledBg`]}:{backgroundColor:"dark"===e.palette.mode?e.palette[t].dark:e.palette[t].main,color:e.palette.getContrastText(e.palette[t].main)}}}))]}})),S=(0,s.Ay)("div",{name:"MuiAlert",slot:"Icon"})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),C=(0,s.Ay)("div",{name:"MuiAlert",slot:"Message"})({padding:"8px 0",minWidth:0,overflow:"auto"}),L=(0,s.Ay)("div",{name:"MuiAlert",slot:"Action"})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),R={success:(0,x.jsx)(w,{fontSize:"inherit"}),warning:(0,x.jsx)(A,{fontSize:"inherit"}),error:(0,x.jsx)(O,{fontSize:"inherit"}),info:(0,x.jsx)(j,{fontSize:"inherit"})},D=r.forwardRef(function(e,t){let n=(0,f.b)({props:e,name:"MuiAlert"}),{action:r,children:i,className:a,closeText:s="Close",color:l,components:p={},componentsProps:d={},icon:u,iconMapping:m=R,onClose:h,role:v="alert",severity:g="success",slotProps:b={},slots:w={},variant:A="standard",...O}=n,j={...n,color:l,severity:g,variant:A,colorSeverity:l||g},D=E(j),k={slots:{closeButton:p.CloseButton,closeIcon:p.CloseIcon,...w},slotProps:{...d,...b}},[W,T]=(0,c.A)("root",{ref:t,shouldForwardComponentProp:!0,className:(0,o.A)(D.root,a),elementType:P,externalForwardedProps:{...k,...O},ownerState:j,additionalProps:{role:v,elevation:0}}),[B,H]=(0,c.A)("icon",{className:D.icon,elementType:S,externalForwardedProps:k,ownerState:j}),[I,z]=(0,c.A)("message",{className:D.message,elementType:C,externalForwardedProps:k,ownerState:j}),[$,V]=(0,c.A)("action",{className:D.action,elementType:L,externalForwardedProps:k,ownerState:j}),[N,q]=(0,c.A)("closeButton",{elementType:y.A,externalForwardedProps:k,ownerState:j}),[F,U]=(0,c.A)("closeIcon",{elementType:M,externalForwardedProps:k,ownerState:j});return(0,x.jsxs)(W,{...T,children:[!1!==u?(0,x.jsx)(B,{...H,children:u||m[g]||R[g]}):null,(0,x.jsx)(I,{...z,children:i}),null!=r?(0,x.jsx)($,{...V,children:r}):null,null==r&&h?(0,x.jsx)($,{...V,children:(0,x.jsx)(N,{size:"small","aria-label":s,title:s,color:"inherit",onClick:h,...q,children:(0,x.jsx)(F,{fontSize:"small",...U})})}):null]})})},89551:(e,t,n)=>{n.d(t,{A:()=>eM});var r=n(71779),o=n(43210),i=n(6352),a=n(70380),s=n(83662);function l(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function f(e){var t=l(e).Element;return e instanceof t||e instanceof Element}function c(e){var t=l(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function p(e){if("undefined"==typeof ShadowRoot)return!1;var t=l(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var d=Math.max,u=Math.min,m=Math.round;function h(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function v(){return!/^((?!chrome|android).)*safari/i.test(h())}function g(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,i=1;t&&c(e)&&(o=e.offsetWidth>0&&m(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&m(r.height)/e.offsetHeight||1);var a=(f(e)?l(e):window).visualViewport,s=!v()&&n,p=(r.left+(s&&a?a.offsetLeft:0))/o,d=(r.top+(s&&a?a.offsetTop:0))/i,u=r.width/o,h=r.height/i;return{width:u,height:h,top:d,right:p+u,bottom:d+h,left:p,x:p,y:d}}function y(e){var t=l(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function b(e){return e?(e.nodeName||"").toLowerCase():null}function x(e){return((f(e)?e.ownerDocument:e.document)||window.document).documentElement}function w(e){return g(x(e)).left+y(e).scrollLeft}function A(e){return l(e).getComputedStyle(e)}function O(e){var t=A(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function j(e){var t=g(e),n=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-n)&&(n=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function M(e){return"html"===b(e)?e:e.assignedSlot||e.parentNode||(p(e)?e.host:null)||x(e)}function E(e,t){void 0===t&&(t=[]);var n,r=function e(t){return["html","body","#document"].indexOf(b(t))>=0?t.ownerDocument.body:c(t)&&O(t)?t:e(M(t))}(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),i=l(r),a=o?[i].concat(i.visualViewport||[],O(r)?r:[]):r,s=t.concat(a);return o?s:s.concat(E(M(a)))}function P(e){return c(e)&&"fixed"!==A(e).position?e.offsetParent:null}function S(e){for(var t=l(e),n=P(e);n&&["table","td","th"].indexOf(b(n))>=0&&"static"===A(n).position;)n=P(n);return n&&("html"===b(n)||"body"===b(n)&&"static"===A(n).position)?t:n||function(e){var t=/firefox/i.test(h());if(/Trident/i.test(h())&&c(e)&&"fixed"===A(e).position)return null;var n=M(e);for(p(n)&&(n=n.host);c(n)&&0>["html","body"].indexOf(b(n));){var r=A(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var C="bottom",L="right",R="left",D="auto",k=["top",C,L,R],W="start",T="viewport",B="popper",H=k.reduce(function(e,t){return e.concat([t+"-"+W,t+"-end"])},[]),I=[].concat(k,[D]).reduce(function(e,t){return e.concat([t,t+"-"+W,t+"-end"])},[]),z=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"],$={placement:"bottom",modifiers:[],strategy:"absolute"};function V(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var N={passive:!0};function q(e){return e.split("-")[0]}function F(e){return e.split("-")[1]}function U(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function _(e){var t,n=e.reference,r=e.element,o=e.placement,i=o?q(o):null,a=o?F(o):null,s=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(i){case"top":t={x:s,y:n.y-r.height};break;case C:t={x:s,y:n.y+n.height};break;case L:t={x:n.x+n.width,y:l};break;case R:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var f=i?U(i):null;if(null!=f){var c="y"===f?"height":"width";switch(a){case W:t[f]=t[f]-(n[c]/2-r[c]/2);break;case"end":t[f]=t[f]+(n[c]/2-r[c]/2)}}return t}var X={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Y(e){var t,n,r,o,i,a,s,f=e.popper,c=e.popperRect,p=e.placement,d=e.variation,u=e.offsets,h=e.position,v=e.gpuAcceleration,g=e.adaptive,y=e.roundOffsets,b=e.isFixed,w=u.x,O=void 0===w?0:w,j=u.y,M=void 0===j?0:j,E="function"==typeof y?y({x:O,y:M}):{x:O,y:M};O=E.x,M=E.y;var P=u.hasOwnProperty("x"),D=u.hasOwnProperty("y"),k=R,W="top",T=window;if(g){var B=S(f),H="clientHeight",I="clientWidth";B===l(f)&&"static"!==A(B=x(f)).position&&"absolute"===h&&(H="scrollHeight",I="scrollWidth"),("top"===p||(p===R||p===L)&&"end"===d)&&(W=C,M-=(b&&B===T&&T.visualViewport?T.visualViewport.height:B[H])-c.height,M*=v?1:-1),(p===R||("top"===p||p===C)&&"end"===d)&&(k=L,O-=(b&&B===T&&T.visualViewport?T.visualViewport.width:B[I])-c.width,O*=v?1:-1)}var z=Object.assign({position:h},g&&X),$=!0===y?(t={x:O,y:M},n=l(f),r=t.x,o=t.y,{x:m(r*(i=n.devicePixelRatio||1))/i||0,y:m(o*i)/i||0}):{x:O,y:M};return(O=$.x,M=$.y,v)?Object.assign({},z,((s={})[W]=D?"0":"",s[k]=P?"0":"",s.transform=1>=(T.devicePixelRatio||1)?"translate("+O+"px, "+M+"px)":"translate3d("+O+"px, "+M+"px, 0)",s)):Object.assign({},z,((a={})[W]=D?M+"px":"",a[k]=P?O+"px":"",a.transform="",a))}var Z={left:"right",right:"left",bottom:"top",top:"bottom"};function G(e){return e.replace(/left|right|bottom|top/g,function(e){return Z[e]})}var J={start:"end",end:"start"};function K(e){return e.replace(/start|end/g,function(e){return J[e]})}function Q(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&p(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function ee(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function et(e,t,n){var r,o,i,a,s,c,p,u,m,h;return t===T?ee(function(e,t){var n=l(e),r=x(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,s=0,f=0;if(o){i=o.width,a=o.height;var c=v();(c||!c&&"fixed"===t)&&(s=o.offsetLeft,f=o.offsetTop)}return{width:i,height:a,x:s+w(e),y:f}}(e,n)):f(t)?((r=g(t,!1,"fixed"===n)).top=r.top+t.clientTop,r.left=r.left+t.clientLeft,r.bottom=r.top+t.clientHeight,r.right=r.left+t.clientWidth,r.width=t.clientWidth,r.height=t.clientHeight,r.x=r.left,r.y=r.top,r):ee((o=x(e),a=x(o),s=y(o),c=null==(i=o.ownerDocument)?void 0:i.body,p=d(a.scrollWidth,a.clientWidth,c?c.scrollWidth:0,c?c.clientWidth:0),u=d(a.scrollHeight,a.clientHeight,c?c.scrollHeight:0,c?c.clientHeight:0),m=-s.scrollLeft+w(o),h=-s.scrollTop,"rtl"===A(c||a).direction&&(m+=d(a.clientWidth,c?c.clientWidth:0)-p),{width:p,height:u,x:m,y:h}))}function en(){return{top:0,right:0,bottom:0,left:0}}function er(e){return Object.assign({},en(),e)}function eo(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}function ei(e,t){void 0===t&&(t={});var n,r,o,i,a,s,l,p,m=t,h=m.placement,v=void 0===h?e.placement:h,y=m.strategy,w=void 0===y?e.strategy:y,O=m.boundary,j=m.rootBoundary,P=m.elementContext,R=void 0===P?B:P,D=m.altBoundary,W=m.padding,H=void 0===W?0:W,I=er("number"!=typeof H?H:eo(H,k)),z=e.rects.popper,$=e.elements[void 0!==D&&D?R===B?"reference":B:R],V=(n=f($)?$:$.contextElement||x(e.elements.popper),r=void 0===O?"clippingParents":O,o=void 0===j?T:j,l=(s=[].concat("clippingParents"===r?(i=E(M(n)),!f(a=["absolute","fixed"].indexOf(A(n).position)>=0&&c(n)?S(n):n)?[]:i.filter(function(e){return f(e)&&Q(e,a)&&"body"!==b(e)})):[].concat(r),[o]))[0],(p=s.reduce(function(e,t){var r=et(n,t,w);return e.top=d(r.top,e.top),e.right=u(r.right,e.right),e.bottom=u(r.bottom,e.bottom),e.left=d(r.left,e.left),e},et(n,l,w))).width=p.right-p.left,p.height=p.bottom-p.top,p.x=p.left,p.y=p.top,p),N=g(e.elements.reference),q=_({reference:N,element:z,strategy:"absolute",placement:v}),F=ee(Object.assign({},z,q)),U=R===B?F:N,X={top:V.top-U.top+I.top,bottom:U.bottom-V.bottom+I.bottom,left:V.left-U.left+I.left,right:U.right-V.right+I.right},Y=e.modifiersData.offset;if(R===B&&Y){var Z=Y[v];Object.keys(X).forEach(function(e){var t=[L,C].indexOf(e)>=0?1:-1,n=["top",C].indexOf(e)>=0?"y":"x";X[e]+=Z[n]*t})}return X}function ea(e,t,n){return d(e,u(t,n))}function es(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function el(e){return["top",L,C,R].some(function(t){return e[t]>=0})}var ef=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,i=void 0===o?$:o;return function(e,t,n){void 0===n&&(n=i);var o,a,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},$,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},p=[],d=!1,u={state:s,setOptions:function(n){var o,a,l,c,d,m,v="function"==typeof n?n(s.options):n;h(),s.options=Object.assign({},i,s.options,v),s.scrollParents={reference:f(e)?E(e):e.contextElement?E(e.contextElement):[],popper:E(t)};var g=(a=Object.keys(o=[].concat(r,s.options.modifiers).reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{})).map(function(e){return o[e]}),l=new Map,c=new Set,d=[],a.forEach(function(e){l.set(e.name,e)}),a.forEach(function(e){c.has(e.name)||function e(t){c.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!c.has(t)){var n=l.get(t);n&&e(n)}}),d.push(t)}(e)}),m=d,z.reduce(function(e,t){return e.concat(m.filter(function(e){return e.phase===t}))},[]));return s.orderedModifiers=g.filter(function(e){return e.enabled}),s.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=e.effect;if("function"==typeof r){var o=r({state:s,name:t,instance:u,options:void 0===n?{}:n});p.push(o||function(){})}}),u.update()},forceUpdate:function(){if(!d){var e=s.elements,t=e.reference,n=e.popper;if(V(t,n)){s.rects={reference:(r=S(n),o="fixed"===s.options.strategy,i=c(r),h=c(r)&&(f=m((a=r.getBoundingClientRect()).width)/r.offsetWidth||1,p=m(a.height)/r.offsetHeight||1,1!==f||1!==p),v=x(r),A=g(t,h,o),M={scrollLeft:0,scrollTop:0},E={x:0,y:0},(i||!i&&!o)&&(("body"!==b(r)||O(v))&&(M=function(e){return e!==l(e)&&c(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:y(e)}(r)),c(r)?(E=g(r,!0),E.x+=r.clientLeft,E.y+=r.clientTop):v&&(E.x=w(v))),{x:A.left+M.scrollLeft-E.x,y:A.top+M.scrollTop-E.y,width:A.width,height:A.height}),popper:j(n)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach(function(e){return s.modifiersData[e.name]=Object.assign({},e.data)});for(var r,o,i,a,f,p,h,v,A,M,E,P=0;P<s.orderedModifiers.length;P++){if(!0===s.reset){s.reset=!1,P=-1;continue}var C=s.orderedModifiers[P],L=C.fn,R=C.options,D=void 0===R?{}:R,k=C.name;"function"==typeof L&&(s=L({state:s,options:D,name:k,instance:u})||s)}}}},update:(o=function(){return new Promise(function(e){u.forceUpdate(),e(s)})},function(){return a||(a=new Promise(function(e){Promise.resolve().then(function(){a=void 0,e(o())})})),a}),destroy:function(){h(),d=!0}};if(!V(e,t))return u;function h(){p.forEach(function(e){return e()}),p=[]}return u.setOptions(n).then(function(e){!d&&n.onFirstUpdate&&n.onFirstUpdate(e)}),u}}({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,i=void 0===o||o,a=r.resize,s=void 0===a||a,f=l(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&c.forEach(function(e){e.addEventListener("scroll",n.update,N)}),s&&f.addEventListener("resize",n.update,N),function(){i&&c.forEach(function(e){e.removeEventListener("scroll",n.update,N)}),s&&f.removeEventListener("resize",n.update,N)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=_({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=n.adaptive,i=n.roundOffsets,a=void 0===i||i,s={placement:q(t.placement),variation:F(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Y(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===o||o,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Y(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];c(o)&&b(o)&&(Object.assign(o.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});c(r)&&b(r)&&(Object.assign(r.style,i),Object.keys(o).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=void 0===o?[0,0]:o,a=I.reduce(function(e,n){var r,o,a,s,l,f;return e[n]=(r=t.rects,a=[R,"top"].indexOf(o=q(n))>=0?-1:1,l=(s="function"==typeof i?i(Object.assign({},r,{placement:n})):i)[0],f=s[1],l=l||0,f=(f||0)*a,[R,L].indexOf(o)>=0?{x:f,y:l}:{x:l,y:f}),e},{}),s=a[t.placement],l=s.x,f=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=f),t.modifiersData[r]=a}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0===a||a,l=n.fallbackPlacements,f=n.padding,c=n.boundary,p=n.rootBoundary,d=n.altBoundary,u=n.flipVariations,m=void 0===u||u,h=n.allowedAutoPlacements,v=t.options.placement,g=q(v)===v,y=l||(g||!m?[G(v)]:function(e){if(q(e)===D)return[];var t=G(e);return[K(e),t,K(t)]}(v)),b=[v].concat(y).reduce(function(e,n){var r,o,i,a,s,l,d,u,v,g,y,b;return e.concat(q(n)===D?(o=(r={placement:n,boundary:c,rootBoundary:p,padding:f,flipVariations:m,allowedAutoPlacements:h}).placement,i=r.boundary,a=r.rootBoundary,s=r.padding,l=r.flipVariations,u=void 0===(d=r.allowedAutoPlacements)?I:d,0===(y=(g=(v=F(o))?l?H:H.filter(function(e){return F(e)===v}):k).filter(function(e){return u.indexOf(e)>=0})).length&&(y=g),Object.keys(b=y.reduce(function(e,n){return e[n]=ei(t,{placement:n,boundary:i,rootBoundary:a,padding:s})[q(n)],e},{})).sort(function(e,t){return b[e]-b[t]})):n)},[]),x=t.rects.reference,w=t.rects.popper,A=new Map,O=!0,j=b[0],M=0;M<b.length;M++){var E=b[M],P=q(E),S=F(E)===W,T=["top",C].indexOf(P)>=0,B=T?"width":"height",z=ei(t,{placement:E,boundary:c,rootBoundary:p,altBoundary:d,padding:f}),$=T?S?L:R:S?C:"top";x[B]>w[B]&&($=G($));var V=G($),N=[];if(i&&N.push(z[P]<=0),s&&N.push(z[$]<=0,z[V]<=0),N.every(function(e){return e})){j=E,O=!1;break}A.set(E,N)}if(O)for(var U=m?3:1,_=function(e){var t=b.find(function(t){var n=A.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return j=t,"break"},X=U;X>0&&"break"!==_(X);X--);t.placement!==j&&(t.modifiersData[r]._skip=!0,t.placement=j,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,i=n.altAxis,a=n.boundary,s=n.rootBoundary,l=n.altBoundary,f=n.padding,c=n.tether,p=void 0===c||c,m=n.tetherOffset,h=void 0===m?0:m,v=ei(t,{boundary:a,rootBoundary:s,padding:f,altBoundary:l}),g=q(t.placement),y=F(t.placement),b=!y,x=U(g),w="x"===x?"y":"x",A=t.modifiersData.popperOffsets,O=t.rects.reference,M=t.rects.popper,E="function"==typeof h?h(Object.assign({},t.rects,{placement:t.placement})):h,P="number"==typeof E?{mainAxis:E,altAxis:E}:Object.assign({mainAxis:0,altAxis:0},E),D=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,k={x:0,y:0};if(A){if(void 0===o||o){var T,B="y"===x?"top":R,H="y"===x?C:L,I="y"===x?"height":"width",z=A[x],$=z+v[B],V=z-v[H],N=p?-M[I]/2:0,_=y===W?O[I]:M[I],X=y===W?-M[I]:-O[I],Y=t.elements.arrow,Z=p&&Y?j(Y):{width:0,height:0},G=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:en(),J=G[B],K=G[H],Q=ea(0,O[I],Z[I]),ee=b?O[I]/2-N-Q-J-P.mainAxis:_-Q-J-P.mainAxis,et=b?-O[I]/2+N+Q+K+P.mainAxis:X+Q+K+P.mainAxis,er=t.elements.arrow&&S(t.elements.arrow),eo=er?"y"===x?er.clientTop||0:er.clientLeft||0:0,es=null!=(T=null==D?void 0:D[x])?T:0,el=ea(p?u($,z+ee-es-eo):$,z,p?d(V,z+et-es):V);A[x]=el,k[x]=el-z}if(void 0!==i&&i){var ef,ec,ep="x"===x?"top":R,ed="x"===x?C:L,eu=A[w],em="y"===w?"height":"width",eh=eu+v[ep],ev=eu-v[ed],eg=-1!==["top",R].indexOf(g),ey=null!=(ec=null==D?void 0:D[w])?ec:0,eb=eg?eh:eu-O[em]-M[em]-ey+P.altAxis,ex=eg?eu+O[em]+M[em]-ey-P.altAxis:ev,ew=p&&eg?(ef=ea(eb,eu,ex))>ex?ex:ef:ea(p?eb:eh,eu,p?ex:ev);A[w]=ew,k[w]=ew-eu}t.modifiersData[r]=k}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=q(n.placement),l=U(s),f=[R,L].indexOf(s)>=0?"height":"width";if(i&&a){var c,p=(c=o.padding,er("number"!=typeof(c="function"==typeof c?c(Object.assign({},n.rects,{placement:n.placement})):c)?c:eo(c,k))),d=j(i),u="y"===l?"top":R,m="y"===l?C:L,h=n.rects.reference[f]+n.rects.reference[l]-a[l]-n.rects.popper[f],v=a[l]-n.rects.reference[l],g=S(i),y=g?"y"===l?g.clientHeight||0:g.clientWidth||0:0,b=p[u],x=y-d[f]-p[m],w=y/2-d[f]/2+(h/2-v/2),A=ea(b,w,x);n.modifiersData[r]=((t={})[l]=A,t.centerOffset=A-w,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;if(null!=r)("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&Q(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=ei(t,{elementContext:"reference"}),s=ei(t,{altBoundary:!0}),l=es(a,r),f=es(s,o,i),c=el(l),p=el(f);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:f,isReferenceHidden:c,hasPopperEscaped:p},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":p})}}]}),ec=n(99282),ep=n(83992),ed=n(88598),eu=n(4144),em=n(82816);function eh(e){return(0,em.Ay)("MuiPopper",e)}(0,eu.A)("MuiPopper",["root"]);var ev=n(60687);function eg(e){return"function"==typeof e?e():e}let ey=e=>{let{classes:t}=e;return(0,ec.A)({root:["root"]},eh,t)},eb={},ex=o.forwardRef(function(e,t){let{anchorEl:n,children:r,direction:i,disablePortal:l,modifiers:f,open:c,placement:p,popperOptions:d,popperRef:u,slotProps:m={},slots:h={},TransitionProps:v,ownerState:g,...y}=e,b=o.useRef(null),x=(0,s.A)(b,t),w=o.useRef(null),A=(0,s.A)(w,u),O=o.useRef(A);(0,a.A)(()=>{O.current=A},[A]),o.useImperativeHandle(u,()=>w.current,[]);let j=function(e,t){if("ltr"===t)return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}(p,i),[M,E]=o.useState(j),[P,S]=o.useState(eg(n));o.useEffect(()=>{w.current&&w.current.forceUpdate()}),o.useEffect(()=>{n&&S(eg(n))},[n]),(0,a.A)(()=>{if(!P||!c)return;let e=e=>{E(e.placement)},t=[{name:"preventOverflow",options:{altBoundary:l}},{name:"flip",options:{altBoundary:l}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:t})=>{e(t)}}];null!=f&&(t=t.concat(f)),d&&null!=d.modifiers&&(t=t.concat(d.modifiers));let n=ef(P,b.current,{placement:j,...d,modifiers:t});return O.current(n),()=>{n.destroy(),O.current(null)}},[P,l,f,c,d,j]);let C={placement:M};null!==v&&(C.TransitionProps=v);let L=ey(e),R=h.root??"div",D=(0,ep.A)({elementType:R,externalSlotProps:m.root,externalForwardedProps:y,additionalProps:{role:"tooltip",ref:x},ownerState:e,className:L.root});return(0,ev.jsx)(R,{...D,children:"function"==typeof r?r(C):r})}),ew=o.forwardRef(function(e,t){let n,{anchorEl:r,children:a,container:s,direction:l="ltr",disablePortal:f=!1,keepMounted:c=!1,modifiers:p,open:d,placement:u="bottom",popperOptions:m=eb,popperRef:h,style:v,transition:g=!1,slotProps:y={},slots:b={},...x}=e,[w,A]=o.useState(!0);if(!c&&!d&&(!g||w))return null;if(s)n=s;else if(r){let e=eg(r);n=e&&void 0!==e.nodeType?(0,i.A)(e).body:(0,i.A)(null).body}let O=!d&&c&&(!g||w)?"none":void 0,j=g?{in:d,onEnter:()=>{A(!1)},onExited:()=>{A(!0)}}:void 0;return(0,ev.jsx)(ed.A,{disablePortal:f,container:n,children:(0,ev.jsx)(ex,{anchorEl:r,direction:l,disablePortal:f,modifiers:p,ref:t,open:g?!w:d,placement:u,popperOptions:m,popperRef:h,slotProps:y,slots:b,...x,style:{position:"fixed",top:0,left:0,display:O,...v},TransitionProps:j,children:a})})});var eA=n(13555),eO=n(84754);let ej=(0,eA.Ay)(ew,{name:"MuiPopper",slot:"Root"})({}),eM=o.forwardRef(function(e,t){let n=(0,r.I)(),{anchorEl:o,component:i,components:a,componentsProps:s,container:l,disablePortal:f,keepMounted:c,modifiers:p,open:d,placement:u,popperOptions:m,popperRef:h,transition:v,slots:g,slotProps:y,...b}=(0,eO.b)({props:e,name:"MuiPopper"}),x=g?.root??a?.Root,w={anchorEl:o,container:l,disablePortal:f,keepMounted:c,modifiers:p,open:d,placement:u,popperOptions:m,popperRef:h,transition:v,...b};return(0,ev.jsx)(ej,{as:i,direction:n?"rtl":"ltr",slots:{root:x},slotProps:y??s,...w,ref:t})})}};