exports.id=79,exports.ids=[79],exports.modules={6311:(e,t,s)=>{Promise.resolve().then(s.bind(s,24892)),Promise.resolve().then(s.bind(s,40518))},22868:(e,t,s)=>{"use strict";s.d(t,{W:()=>p,default:()=>S});var r=s(60687),o=s(43210),a=s.n(o),n=s(6198),i=s(16189),c=s(55764),d=s(51052),l=s(73806),u=s(32288);let m=a().createContext({mode:"light",toggleTheme:()=>{}}),p=()=>a().useContext(m),h=e=>(0,d.A)({palette:{mode:e,primary:{main:"#1976d2"},secondary:{main:"#dc004e"},background:{default:"light"===e?"#f5f5f5":"#121212",paper:"light"===e?"#ffffff":"#1e1e1e"}},typography:{fontFamily:"var(--font-geist-sans)"},components:{MuiCssBaseline:{styleOverrides:{body:{scrollbarColor:"dark"===e?"#6b6b6b #2b2b2b":"#959595 #f5f5f5","&::-webkit-scrollbar, & *::-webkit-scrollbar":{width:"8px",height:"8px"},"&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb":{borderRadius:8,backgroundColor:"dark"===e?"#6b6b6b":"#959595"},"&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track":{borderRadius:8,backgroundColor:"dark"===e?"#2b2b2b":"#f5f5f5"}}}}}});function S({children:e}){let[t,s]=a().useState("light"),o=a().useMemo(()=>h(t),[t]),d=a().useCallback(()=>{s(e=>{let t="light"===e?"dark":"light";return localStorage?.setItem?.("themeMode",t),t})},[]),p=a().useMemo(()=>({mode:t,toggleTheme:d}),[t,d]),[{cache:S,flush:y}]=a().useState(()=>{let e=(0,n.A)({key:"mui"});e.compat=!0;let t=e.insert,s=[];return e.insert=(...r)=>{let o=r[1];return void 0===e.inserted[o.name]&&s.push(o.name),t(...r)},{cache:e,flush:()=>{let e=s;return s=[],e}}});return(0,i.useServerInsertedHTML)(()=>{let e=y();if(0===e.length)return null;let t="";for(let s of e)t+=S.inserted[s];return(0,r.jsx)("style",{"data-emotion":`${S.key} ${e.join(" ")}`,dangerouslySetInnerHTML:{__html:t}},"emotion-style")}),(0,r.jsx)(c.C,{value:S,children:(0,r.jsx)(m.Provider,{value:p,children:(0,r.jsxs)(l.A,{theme:o,children:[(0,r.jsx)(u.Ay,{}),e]})})})}},24892:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\yunsell\\\\evospace\\\\evospace-pos\\\\src\\\\components\\\\layout\\\\MainLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\yunsell\\evospace\\evospace-pos\\src\\components\\layout\\MainLayout.tsx","default")},27233:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},28840:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(26787),o=s(52707),a=s(58188);let n=async(e,t,s)=>{try{let r=await e();if(r.success&&r.data.items&&r.data.items.length>0)return r.data.items;return console.warn(`API returned no ${s}, using mock data`),t}catch(e){return console.error(`Error fetching ${s}:`,e),t}},i=async(e,t,s,r,o,a)=>{try{let r=await t();if(r.success&&r.data.transaction)return a(t=>({transactions:t.transactions.map(t=>t.id===e?r.data.transaction:t)})),r.data.transaction;return a(t=>({transactions:t.transactions.map(t=>t.id===e?{...t,status:s}:t)})),o.transactions.find(t=>t.id===e)}catch(t){return console.error(`Error ${r} transaction ${e}:`,t),a(t=>({transactions:t.transactions.map(t=>t.id===e?{...t,status:s}:t)})),o.transactions.find(t=>t.id===e)}},c=async(e,t,s,r,o,a)=>{let n=r(e);if(!n)return void console.error(`No active session found for resource ${e}`);let i="product"===s?{product_id:t.id,quantity:1,price:t.price}:{service_id:t.id,unit:"hour",quantity:1,price:t.price};try{await o(n.id,i),a(e=>({activeSessions:e.activeSessions.map(e=>{if(e.id===n.id){let r,o="product"===s?e.products:e.services;return r=o.find(e=>e.id===t.id)?o.map(e=>e.id===t.id?{...e,quantity:e.quantity+1}:e):[...o,{id:t.id,type:s,name:t.name,price:t.price,quantity:1,[`${s}_id`]:t.id}],"product"===s?{...e,products:r}:{...e,services:r}}return e})}))}catch(e){console.error(`Error adding ${s} ${t.id} to session ${n.id}:`,e),a(e=>({activeSessions:e.activeSessions.map(e=>{if(e.id===n.id){let r,o="product"===s?e.products:e.services;return r=o.find(e=>e.id===t.id)?o.map(e=>e.id===t.id?{...e,quantity:e.quantity+1}:e):[...o,{id:t.id,type:s,name:t.name,price:t.price,quantity:1,[`${s}_id`]:t.id}],"product"===s?{...e,products:r}:{...e,services:r}}return e})}))}},d=(0,r.v)((e,t)=>({authUser:{username:null,token:null,store_id:null,store_name:null},stores:[],fetchStores:async()=>{try{let t=await a.Kp.auth.getStores();t.success&&t.data&&t.data.items&&e({stores:t.data.items})}catch(t){console.error("Error fetching stores:",t),e({stores:[{id:1,name:"Main Store",address:"123 Main St",phone:"555-0100"},{id:2,name:"Branch Store",address:"456 Oak Ave",phone:"555-0200"}]})}},setUser:t=>e({authUser:t}),loadAuthToken:()=>{},currentUser:o.VV[0],isAuthenticated:!1,login:async(t,s,r)=>{try{let o=await a.Kp.auth.login({username:t,password:s,store_id:r});if(o.success&&o.data){let{user:t,token:s}=o.data;return e({currentUser:t,isAuthenticated:!0,authUser:{username:t.username,token:s,store_id:t.store_id||r,store_name:t.store_name||null}}),!0}return!1}catch(e){return console.error("Login error:",e),!1}},logout:()=>{e({currentUser:o.VV[0],isAuthenticated:!1,authUser:{username:null,token:null,store_id:null,store_name:null}})},products:[],getProduct:e=>t().products.find(t=>t.id===e),fetchProducts:async()=>{e({products:await n(()=>a.Kp.products.getProducts(),o.ZE,"products")})},addProduct:async t=>{try{let s=await a.Kp.products.createProduct(t);s.success&&s.data.product&&e(e=>({products:[...e.products,s.data.product]}))}catch(e){throw console.error("Error adding product:",e),e}},updateProduct:async(t,s)=>{try{let r=await a.Kp.products.updateProduct(t,s);r.success&&r.data.product&&e(e=>({products:e.products.map(e=>e.id===t?r.data.product:e)}))}catch(e){throw console.error(`Error updating product ${t}:`,e),e}},deleteProduct:async t=>{try{(await a.Kp.products.deleteProduct(t)).success&&e(e=>({products:e.products.filter(e=>e.id!==t)}))}catch(e){throw console.error(`Error deleting product ${t}:`,e),e}},categories:o.LZ,getCategory:e=>t().categories.find(t=>t.id===e),services:[],getService:e=>t().services.find(t=>t.id===e),fetchServices:async()=>{e({services:await n(()=>a.Kp.services.getServices(),o.$p,"services")})},addService:async t=>{try{let s=await a.Kp.services.createService(t);s.success&&s.data.service&&e(e=>({services:[...e.services,s.data.service]}))}catch(e){throw console.error("Error adding service:",e),e}},updateService:async(t,s)=>{try{let r=await a.Kp.services.updateService(t,s);r.success&&r.data.service&&e(e=>({services:e.services.map(e=>e.id===t?r.data.service:e)}))}catch(e){throw console.error(`Error updating service ${t}:`,e),e}},deleteService:async t=>{try{(await a.Kp.services.deleteService(t)).success&&e(e=>({services:e.services.filter(e=>e.id!==t)}))}catch(e){throw console.error(`Error deleting service ${t}:`,e),e}},resources:o.ES,getResource:e=>t().resources.find(t=>t.id===e),fetchResources:async()=>{e({resources:await n(()=>a.Kp.resources.getResources(),o.ES,"resources")})},addResource:async t=>{try{let s=await a.Kp.resources.createResource(t);s.success&&s.data.resource&&e(e=>({resources:[...e.resources,s.data.resource]}))}catch(e){throw console.error("Error adding resource:",e),e}},updateResource:async(t,s)=>{try{let r=await a.Kp.resources.updateResource(t,s);r.success&&r.data.resource&&e(e=>({resources:e.resources.map(e=>e.id===t?r.data.resource:e)}))}catch(e){throw console.error(`Error updating resource ${t}:`,e),e}},updateResourceStatus:async(t,s)=>{try{let r=await a.Kp.resources.updateResource(t,{status:s});r.success&&r.data.resource&&e(e=>({resources:e.resources.map(e=>e.id===t?r.data.resource:e)}))}catch(e){throw console.error(`Error updating resource status ${t}:`,e),e}},setResources:t=>e({resources:t}),deleteResource:async t=>{try{(await a.Kp.resources.deleteResource(t)).success&&e(e=>({resources:e.resources.filter(e=>e.id!==t)}))}catch(e){throw console.error(`Error deleting resource ${t}:`,e),e}},members:[],getMember:e=>t().members.find(t=>t.id===e),addMember:async t=>{try{let s=await a.Kp.members.createMember(t);s.success&&s.data.member&&e(e=>({members:[...e.members,s.data.member]}))}catch(e){throw console.error("Error adding member:",e),e}},updateMember:async(t,s)=>{try{let r=await a.Kp.members.updateMember(t,s);r.success&&r.data.member&&e(e=>({members:e.members.map(e=>e.id===t?r.data.member:e)}))}catch(e){throw console.error(`Error updating member ${t}:`,e),e}},deleteMember:async t=>{try{(await a.Kp.members.deleteMember(t)).success&&e(e=>({members:e.members.filter(e=>e.id!==t)}))}catch(e){throw console.error(`Error deleting member ${t}:`,e),e}},fetchMembers:async()=>{let{members:t}=await Promise.resolve().then(s.bind(s,52707));e({members:await n(()=>a.Kp.members.getMembers(),t,"members")})},transactions:[],fetchTransactions:async()=>{try{let t=await a.Kp.transactions.getTransactions();t.success&&t.data.items?e({transactions:t.data.items}):console.warn("API returned no transactions")}catch(e){console.error("Error fetching transactions:",e)}},addTransaction:async s=>{try{let r={member_id:s.memberId||null,subtotal:s.subtotal||s.totalAmount,tax_amount:s.tax_amount||0,tax_rate:s.tax_rate||0,discount_amount:s.discount_amount||0,discount_rate:s.discount_rate||0,total_amount:s.totalAmount,actual_amount:s.actual_amount||s.totalAmount,payment_method:s.paymentMethod,payment_reference:s.payment_reference||"",notes:s.notes||"",sessions:s.sessions||[]},o=await a.Kp.transactions.createTransaction(r);if(o.success&&o.data.transaction)return e(e=>({transactions:[...e.transactions,o.data.transaction]})),t().clearCart(),o.data.transaction;{let r={id:Math.max(0,...t().transactions.map(e=>e.id))+1,...s,createdAt:new Date().toISOString(),status:"pending"};return e(e=>({transactions:[...e.transactions,r]})),t().clearCart(),r}}catch(o){console.error("Error adding transaction:",o);let r={id:Math.max(0,...t().transactions.map(e=>e.id))+1,...s,createdAt:new Date().toISOString(),status:"pending"};return e(e=>({transactions:[...e.transactions,r]})),t().clearCart(),r}},completeTransaction:async s=>i(s,()=>a.Kp.transactions.completeTransaction(s),"completed","completing",t(),e),cancelTransaction:async(s,r="Cancelled by user")=>i(s,()=>a.Kp.transactions.cancelTransaction(s,r),"cancelled","cancelling",t(),e),cart:[],addToCart:s=>{let{cart:r}=t();r.find(e=>e.id===s.id)||e(e=>({cart:[...e.cart,{...s}]}))},removeFromCart:t=>{e(e=>({cart:e.cart.filter(e=>e.id!==t)}))},clearCart:()=>e({cart:[]}),cartTotal:()=>{let{cart:e,getActiveSession:s}=t();return e.reduce((e,t)=>{let r=s(t.id);if(r){let s=10*Math.ceil((Date.now()-new Date(r.start_time).getTime())/6e4/10)/60*t.price;return e+s+r.products.reduce((e,t)=>e+t.price*t.quantity,0)+r.services.reduce((e,t)=>e+t.price*t.quantity,0)}return e},0)},selectedMember:null,setSelectedMember:t=>e({selectedMember:t}),activeSessions:[],fetchActiveSessions:async()=>{try{let t=await a.Kp.sessions.getSessions();if(t.success&&t.data.items){let s=t.data.items.filter(e=>"active"===e.status||"open"===e.status).map(e=>{let t=Array.isArray(e.products)?e.products.map(e=>({id:e.id,type:"product",name:e.name,price:e.price,quantity:e.quantity})):[],s=Array.isArray(e.services)?e.services.map(e=>({id:e.id,type:"service",name:e.name,price:e.price,quantity:e.quantity})):[];return{id:e.id,resource_id:e.resource_id||0,user_id:e.user_id,member_id:e.member_id,start_time:e.start_time||new Date().toISOString(),end_time:void 0,status:e.status,products:t,services:s,notes:e.notes}});e({activeSessions:s})}else console.warn("API returned no active sessions")}catch(e){console.error("Error fetching active sessions:",e)}},startSession:async(s,r,o)=>{try{let n={resource_id:s,member_id:r,notes:o||`Session for resource ${s}`},i=await a.Kp.sessions.createSession(n);if(i.success&&i.data.session){let a=i.data.session.id,n={id:a,resource_id:s,user_id:i.data.session.user_id,member_id:r,start_time:i.data.session.start_time||new Date().toISOString(),status:"open",products:[],services:[],notes:o};return await t().updateResourceStatus(s,"in-use"),e(e=>({activeSessions:[...e.activeSessions,n]})),a}{console.warn("API session creation failed, using local session");let a=Date.now(),n={id:a,resource_id:s,member_id:r,start_time:new Date().toISOString(),status:"active",products:[],services:[],notes:o};return await t().updateResourceStatus(s,"in-use"),e(e=>({activeSessions:[...e.activeSessions,n]})),a}}catch(i){console.error("Error starting session:",i);let a=Date.now(),n={id:a,resource_id:s,member_id:r,start_time:new Date().toISOString(),status:"active",products:[],services:[],notes:o};try{await t().updateResourceStatus(s,"in-use")}catch(e){console.error("Error updating resource status:",e)}return e(e=>({activeSessions:[...e.activeSessions,n]})),a}},endSession:async(s,r)=>{try{let o=t().activeSessions.find(e=>e.id===s);if(!o)return void console.error(`Session with ID ${s} not found`);let n=await a.Kp.sessions.closeSession(s,{status:"closed",notes:r||`Session for resource ${o.resource_id} ended`});n.success&&n.data.session?e(e=>({activeSessions:e.activeSessions.filter(e=>e.id!==s)})):e(e=>({activeSessions:e.activeSessions.filter(e=>e.id!==s)})),await t().updateResourceStatus(o.resource_id,"available")}catch(r){console.error(`Error ending session ${s}:`,r),e(e=>({activeSessions:e.activeSessions.filter(e=>e.id!==s)}));try{let e=t().activeSessions.find(e=>e.id===s);e&&await t().updateResourceStatus(e.resource_id,"available")}catch(e){console.error("Error updating resource status:",e)}}},getActiveSession:e=>t().activeSessions.find(t=>t.resource_id===e&&("active"===t.status||"open"===t.status)),addServiceToActiveSession:async(s,r)=>c(s,r,"service",t().getActiveSession,(e,t)=>a.Kp.sessions.addServiceToSession(e,{service_id:t.service_id,unit:t.unit,quantity:t.quantity,price:t.price}),e),addProductToActiveSession:async(s,r)=>c(s,r,"product",t().getActiveSession,(e,t)=>a.Kp.sessions.addProductToSession(e,{product_id:t.product_id,quantity:t.quantity,price:t.price}),e)}))},36575:(e,t,s)=>{Promise.resolve().then(s.bind(s,89306)),Promise.resolve().then(s.bind(s,22868))},40385:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},40518:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call ThemeContext() from the server but ThemeContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\yunsell\\evospace\\evospace-pos\\src\\components\\ThemeRegistry.tsx","ThemeContext"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\yunsell\\evospace\\evospace-pos\\src\\components\\ThemeRegistry.tsx","useTheme");let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\yunsell\\\\evospace\\\\evospace-pos\\\\src\\\\components\\\\ThemeRegistry.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\yunsell\\evospace\\evospace-pos\\src\\components\\ThemeRegistry.tsx","default")},52707:(e,t,s)=>{"use strict";s.d(t,{$p:()=>n,ES:()=>i,LZ:()=>a,VV:()=>r,ZE:()=>o,members:()=>c});let r=[{id:1,username:"admin",name:"Administrator",email:"<EMAIL>",role:"admin"},{id:2,username:"staff",name:"Staff User",email:"<EMAIL>",role:"staff"}],o=[{id:1,name:"Coffee",category:"Beverages",price:2.5,stock:100,image:""},{id:2,name:"Tea",category:"Beverages",price:1.8,stock:120,image:""},{id:3,name:"Sandwich",category:"Food",price:4.5,stock:30,image:""},{id:4,name:"Croissant",category:"Bakery",price:2.2,stock:45,image:""},{id:5,name:"Salad",category:"Food",price:5.5,stock:25,image:""},{id:6,name:"Cake Slice",category:"Desserts",price:3.8,stock:20,image:""},{id:7,name:"Water Bottle",category:"Beverages",price:1,stock:150,image:""},{id:8,name:"Muffin",category:"Bakery",price:2,stock:40,image:""}],a=[{id:1,name:"Beverages"},{id:2,name:"Food"},{id:3,name:"Bakery"},{id:4,name:"Desserts"}],n=[{id:1,name:"Room Rental",price:25,duration:60,description:"Rent a meeting room for 1 hour",category:"Workspace"},{id:2,name:"Workspace",price:15,duration:60,description:"Access to workspace for 1 hour",category:"Workspace"},{id:3,name:"Printing",price:.1,unit:"per page",description:"Black and white printing service",category:"Office Services"},{id:4,name:"Color Printing",price:.25,unit:"per page",description:"Color printing service",category:"Office Services"},{id:5,name:"Coffee Service",price:10,description:"Coffee service for meetings (serves 5)",category:"Catering"}],i=[{id:1,name:"Conference Room A",type:"room",capacity:12,hourly_rate:50,status:"available",x:0,y:0,width:3,height:2,floor:1,zone:"North Wing"},{id:2,name:"Office Desk 1",type:"desk",capacity:1,hourly_rate:10,status:"booked",x:3,y:0,width:1,height:1,floor:1,zone:"North Wing"},{id:3,name:"Meeting Room B",type:"room",capacity:8,hourly_rate:35,status:"maintenance",x:0,y:3,width:2,height:2,floor:1,zone:"South Wing"},{id:4,name:"Office Desk 2",type:"desk",capacity:1,hourly_rate:10,status:"available",x:3,y:1,width:1,height:1,floor:1,zone:"North Wing"},{id:5,name:"Projector",type:"equipment",capacity:0,hourly_rate:15,status:"available",x:5,y:5,width:1,height:1,floor:1,zone:"Storage"},{id:6,name:"Conference Room C",type:"room",capacity:20,hourly_rate:75,status:"available",x:6,y:0,width:3,height:3,floor:2,zone:"Executive Wing"},{id:7,name:"Standing Desk 1",type:"desk",capacity:1,hourly_rate:12,status:"available",x:1,y:0,width:1,height:1,floor:2,zone:"Open Space"},{id:8,name:"Meeting Pod",type:"room",capacity:4,hourly_rate:25,status:"booked",x:2,y:0,width:2,height:2,floor:2,zone:"Open Space"},{id:9,name:"Laptop",type:"equipment",capacity:0,hourly_rate:20,status:"available",x:5,y:6,width:1,height:1,floor:1,zone:"Storage"},{id:10,name:"Focus Room",type:"room",capacity:1,hourly_rate:15,status:"available",x:9,y:0,width:1,height:1,floor:1,zone:"Quiet Zone"}],c=[{id:1,name:"John Doe",email:"<EMAIL>",phone:"555-1234",visits:10,totalSpent:250.5},{id:2,name:"Jane Smith",email:"<EMAIL>",phone:"555-5678",visits:5,totalSpent:120.75},{id:3,name:"Bob Johnson",email:"<EMAIL>",phone:"555-9012",visits:8,totalSpent:180.25},{id:4,name:"Alice Brown",email:"<EMAIL>",phone:"555-3456",visits:12,totalSpent:320},{id:5,name:"Charlie Wilson",email:"<EMAIL>",phone:"555-7890",visits:3,totalSpent:75.5}];new Date(Date.now()-36e5).toISOString(),new Date().toISOString(),new Date().toISOString(),new Date(Date.now()-72e5).toISOString(),new Date(Date.now()-36e5).toISOString(),new Date().toISOString()},58188:(e,t,s)=>{"use strict";s.d(t,{Kp:()=>c,Ay:()=>d,SL:()=>i});let r=process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:5000/api";class o extends Error{constructor(e,t,s){super(e),this.name="ApiError",this.status=t,this.errors=s}}async function a(e,t={}){let{method:s="GET",token:n=null,body:i=null,params:c={}}=t,d={Accept:"application/json"};i&&(d["Content-Type"]="application/json"),n&&(d.Authorization=`Bearer ${n}`);let l=new URLSearchParams;Object.entries(c).forEach(([e,t])=>{void 0!==t&&l.append(e,String(t))});let u=l.toString(),m=`${r}${e}${u?`?${u}`:""}`,p=await fetch(m,{method:s,headers:d,body:i?JSON.stringify(i):void 0});if(!p.ok){let e;p.status;let t=`API Error (${p.status}): `;try{let s=await p.json();t+=s.message||p.statusText||"Unknown error",e=s.errors}catch{t+=p.statusText||"Unknown error (failed to parse error response)"}throw console.error(`${s} ${m} failed:`,t),new o(t,p.status,e)}if(204===p.status)return;let h=await p.json();if("success"in h&&!h.success){let e=h.message||"API request failed";throw console.error(`${s} ${m} failed:`,e),new o(e,p.status,h.errors)}return h}let n={AUTH:{LOGIN:"/auth/login",STORES:"/auth/stores"},MEMBERS:{BASE:"/members",DETAIL:e=>`/members/${e}`},PRODUCTS:{BASE:"/products",DETAIL:e=>`/products/${e}`},SERVICES:{BASE:"/services",DETAIL:e=>`/services/${e}`},RESOURCES:{BASE:"/resources",DETAIL:e=>`/resources/${e}`},TRANSACTIONS:{BASE:"/transactions",DETAIL:e=>`/transactions/${e}`,ITEMS:e=>`/transactions/${e}/items`,ITEM_DETAIL:(e,t)=>`/transactions/${e}/items/${t}`,COMPLETE:e=>`/transactions/${e}/complete`,REFUND:e=>`/transactions/${e}/refund`,CANCEL:e=>`/transactions/${e}/cancel`},SESSIONS:{BASE:"/sessions",DETAIL:e=>`/sessions/${e}`,CLOSE:e=>`/sessions/${e}/close`,SERVICES:e=>`/sessions/${e}/services`,PRODUCTS:e=>`/sessions/${e}/products`},REPORTS:{SALES_SUMMARY:"/reports/sales_summary",SALES_BY_CATEGORY:"/reports/sales_by_category",PRODUCT_PERFORMANCE:"/reports/product_performance",RESOURCE_UTILIZATION:"/reports/resource_utilization",DASHBOARD_SUMMARY:"/reports/dashboard_summary",SALES_BY_PAYMENT_METHOD:"/reports/sales_by_payment_method",MEMBER_ACTIVITY:"/reports/member_activity"}},i={getSalesSummary:async e=>a(n.REPORTS.SALES_SUMMARY,{params:e}),getSalesByCategory:async e=>a(n.REPORTS.SALES_BY_CATEGORY,{params:e}),getProductPerformance:async e=>a(n.REPORTS.PRODUCT_PERFORMANCE,{params:e}),getResourceUtilization:async e=>a(n.REPORTS.RESOURCE_UTILIZATION,{params:e}),getDashboardSummary:async e=>a(n.REPORTS.DASHBOARD_SUMMARY,{params:e}),getSalesByPaymentMethod:async e=>a(n.REPORTS.SALES_BY_PAYMENT_METHOD,{params:e}),getMemberActivity:async e=>a(n.REPORTS.MEMBER_ACTIVITY,{params:e})},c={auth:{login:async e=>a(n.AUTH.LOGIN,{method:"POST",body:e}),getStores:async()=>a(n.AUTH.STORES,{method:"GET"}),getUserStores:async e=>a(n.AUTH.STORES,{method:"POST",body:e})},members:{getMembers:async e=>a(n.MEMBERS.BASE,{params:e}),getMember:async e=>a(n.MEMBERS.DETAIL(e)),createMember:async e=>a(n.MEMBERS.BASE,{method:"POST",body:e}),updateMember:async(e,t)=>a(n.MEMBERS.DETAIL(e),{method:"PUT",body:t}),deleteMember:async e=>a(n.MEMBERS.DETAIL(e),{method:"DELETE"})},products:{getProducts:async e=>a(n.PRODUCTS.BASE,{params:e}),getProduct:async e=>a(n.PRODUCTS.DETAIL(e)),createProduct:async e=>a(n.PRODUCTS.BASE,{method:"POST",body:e}),updateProduct:async(e,t)=>a(n.PRODUCTS.DETAIL(e),{method:"PUT",body:t}),deleteProduct:async e=>a(n.PRODUCTS.DETAIL(e),{method:"DELETE"})},services:{getServices:async e=>a(n.SERVICES.BASE,{params:e}),getService:async e=>a(n.SERVICES.DETAIL(e)),createService:async e=>a(n.SERVICES.BASE,{method:"POST",body:e}),updateService:async(e,t)=>a(n.SERVICES.DETAIL(e),{method:"PUT",body:t}),deleteService:async e=>a(n.SERVICES.DETAIL(e),{method:"DELETE"})},resources:{getResources:async e=>a(n.RESOURCES.BASE,{params:e}),getResource:async e=>a(n.RESOURCES.DETAIL(e)),createResource:async e=>a(n.RESOURCES.BASE,{method:"POST",body:e}),updateResource:async(e,t)=>a(n.RESOURCES.DETAIL(e),{method:"PUT",body:t}),deleteResource:async e=>a(n.RESOURCES.DETAIL(e),{method:"DELETE"})},transactions:{getTransactions:async e=>a(n.TRANSACTIONS.BASE,{params:e}),getTransaction:async e=>a(n.TRANSACTIONS.DETAIL(e)),createTransaction:async e=>a(n.TRANSACTIONS.BASE,{method:"POST",body:e}),getTransactionItems:async e=>a(n.TRANSACTIONS.ITEMS(e)),addTransactionItem:async(e,t)=>a(n.TRANSACTIONS.ITEMS(e),{method:"POST",body:t}),removeTransactionItem:async(e,t)=>a(n.TRANSACTIONS.ITEM_DETAIL(e,t),{method:"DELETE"}),completeTransaction:async e=>a(n.TRANSACTIONS.COMPLETE(e),{method:"PUT"}),refundTransaction:async(e,t)=>a(n.TRANSACTIONS.REFUND(e),{method:"PUT",body:t}),cancelTransaction:async(e,t)=>a(n.TRANSACTIONS.CANCEL(e),{method:"PUT",body:{reason:t}})},sessions:{getSessions:async e=>a(n.SESSIONS.BASE,{params:e}),getSession:async e=>a(n.SESSIONS.DETAIL(e)),createSession:async e=>a(n.SESSIONS.BASE,{method:"POST",body:e}),updateSession:async(e,t)=>a(n.SESSIONS.DETAIL(e),{method:"PUT",body:t}),closeSession:async(e,t)=>a(n.SESSIONS.CLOSE(e),{method:"PUT",body:t}),addServiceToSession:async(e,t)=>a(n.SESSIONS.SERVICES(e),{method:"POST",body:t}),addProductToSession:async(e,t)=>a(n.SESSIONS.PRODUCTS(e),{method:"POST",body:t})},reports:i},d=c},61135:()=>{},89306:(e,t,s)=>{"use strict";s.d(t,{default:()=>q});var r=s(60687),o=s(43210),a=s(66932),n=s(87088),i=s(91176),c=s(45525),d=s(17181),l=s(64216),u=s(53006),m=s(93010),p=s(88931),h=s(32288),S=s(24955),y=s(83685),v=s(12879),E=s(73766),g=s(47651),f=s(82948),b=s(51730),A=s(81889),T=s(82647),x=s(84122),R=s(98224),w=s(35683),C=s(27710),P=s(6441),_=s(73855),I=s(42149),O=s(90765),D=s(73234),M=s(52543),k=s(19257),j=s(83706),L=s(85814),U=s.n(L),$=s(16189),B=s(28840),N=s(22868);let K=[{text:"Dashboard",icon:(0,r.jsx)(b.A,{}),path:"/"},{text:"Serve",icon:(0,r.jsx)(A.A,{}),path:"/pos"},{text:"Resources",icon:(0,r.jsx)(T.A,{}),path:"/resources"},{text:"Products",icon:(0,r.jsx)(x.A,{}),path:"/products"},{text:"Services",icon:(0,r.jsx)(R.A,{}),path:"/services"},{text:"Members",icon:(0,r.jsx)(w.A,{}),path:"/members"},{text:"Reports",icon:(0,r.jsx)(C.A,{}),path:"/reports"},{text:"Settings",icon:(0,r.jsx)(P.A,{}),path:"/settings"}];function q({children:e}){let[t,s]=(0,o.useState)(!1),[b,A]=(0,o.useState)(null),[T,x]=(0,o.useState)(!1),R=(0,$.usePathname)(),{currentUser:w,logout:C,authUser:P,loadAuthToken:L}=(0,B.A)(),{mode:q,toggleTheme:z}=(0,N.W)(),V=!P.token,F=()=>{s(!t)},W=()=>{A(null)},Y=(0,r.jsxs)("div",{children:[(0,r.jsx)(a.A,{sx:{justifyContent:"center"},children:(0,r.jsx)(n.A,{variant:"h6",component:"div",sx:{fontWeight:"bold"},children:"EvoSpace"})}),(0,r.jsx)(i.A,{}),(0,r.jsx)(c.A,{children:K.map(e=>(0,r.jsx)(d.Ay,{disablePadding:!0,children:(0,r.jsx)(U(),{href:e.path,style:{textDecoration:"none",width:"100%",color:"inherit"},children:(0,r.jsxs)(l.A,{selected:R===e.path,children:[(0,r.jsx)(u.A,{children:e.icon}),(0,r.jsx)(m.A,{primary:e.text})]})})},e.text))})]});return(0,r.jsxs)(p.A,{sx:{display:"flex"},children:[(0,r.jsx)(h.Ay,{}),!V&&(0,r.jsx)(S.A,{position:"fixed",sx:{width:{sm:"calc(100% - 240px)"},ml:{sm:"240px"}},children:(0,r.jsxs)(a.A,{children:[(0,r.jsx)(y.A,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:F,sx:{mr:2,display:{sm:"none"}},children:(0,r.jsx)(_.A,{})}),(0,r.jsx)(n.A,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:K.find(e=>e.path===R)?.text||"EvoSpace"}),w&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.A,{color:"inherit",onClick:()=>{document.fullscreenElement?document.exitFullscreen&&document.exitFullscreen():document.documentElement.requestFullscreen().catch(e=>{console.error(`Error attempting to enable fullscreen: ${e.message}`)})},sx:{ml:1},children:T?(0,r.jsx)(I.A,{}):(0,r.jsx)(O.A,{})}),(0,r.jsx)(y.A,{color:"inherit",onClick:()=>{z()},sx:{ml:1},children:"dark"===q?(0,r.jsx)(D.A,{}):(0,r.jsx)(M.A,{})}),(0,r.jsx)(y.A,{onClick:e=>{A(e.currentTarget)},size:"small",sx:{ml:2},"aria-controls":"menu-appbar","aria-haspopup":"true",children:(0,r.jsx)(v.A,{sx:{width:32,height:32},children:w.name.charAt(0)})}),(0,r.jsxs)(E.A,{id:"menu-appbar",anchorEl:b,anchorOrigin:{vertical:"bottom",horizontal:"right"},keepMounted:!0,transformOrigin:{vertical:"top",horizontal:"right"},open:!!b,onClose:W,children:[(0,r.jsxs)(g.A,{onClick:W,children:[(0,r.jsx)(u.A,{children:(0,r.jsx)(k.A,{fontSize:"small"})}),(0,r.jsx)(m.A,{children:"Profile"})]}),(0,r.jsxs)(g.A,{onClick:()=>{C(),W()},children:[(0,r.jsx)(u.A,{children:(0,r.jsx)(j.A,{fontSize:"small"})}),(0,r.jsx)(m.A,{children:"Logout"})]})]})]})]})}),!V&&(0,r.jsxs)(p.A,{component:"nav",sx:{width:{sm:240},flexShrink:{sm:0}},"aria-label":"mailbox folders",children:[(0,r.jsx)(f.Ay,{variant:"temporary",open:t,onClose:F,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",sm:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:240}},children:Y}),(0,r.jsx)(f.Ay,{variant:"permanent",sx:{display:{xs:"none",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:240}},open:!0,children:Y})]}),(0,r.jsxs)(p.A,{component:"main",sx:{flexGrow:1,p:3,width:V?"100%":{sm:"calc(100% - 240px)"}},children:[!V&&(0,r.jsx)(a.A,{})," ",e]})]})}},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>l});var r=s(37413),o=s(22376),a=s.n(o),n=s(68726),i=s.n(n),c=s(40518),d=s(24892);s(61135);let l={title:"EvoSpace",description:"Point of Sale system for EvoSpace"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().variable} ${i().variable} antialiased`,children:(0,r.jsx)(c.default,{children:(0,r.jsx)(d.default,{children:e})})})})}}};