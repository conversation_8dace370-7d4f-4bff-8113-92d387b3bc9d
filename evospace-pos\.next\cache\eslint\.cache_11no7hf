[{"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\auth\\login\\page.tsx": "1", "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\layout.tsx": "2", "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\members\\page.tsx": "3", "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\page.tsx": "4", "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\pos\\page.tsx": "5", "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\products\\page.tsx": "6", "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\reports\\page.tsx": "7", "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\resources\\page.tsx": "8", "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\services\\page.tsx": "9", "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\settings\\page.tsx": "10", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\ConfirmDialog.tsx": "11", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\layout\\MainLayout.tsx": "12", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\CartTotal.tsx": "13", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\MemberSelectionDialog.tsx": "14", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\PaymentDialog.tsx": "15", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\ProductSelectionDialog.tsx": "16", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\ResourceFloorPlanView.tsx": "17", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\ServiceSelectionDialog.tsx": "18", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\SessionDuration.tsx": "19", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\ProtectedPage.tsx": "20", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\TabPanel.tsx": "21", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\ThemeRegistry.tsx": "22", "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\apiClient.ts": "23", "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\mockData.ts": "24", "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\store.ts": "25", "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\theme.tsx": "26", "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\types.ts": "27", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\resources\\index.ts": "28", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\resources\\ResourceControls.tsx": "29", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\resources\\ResourceDialog.tsx": "30", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\resources\\ResourceFilters.tsx": "31", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\resources\\ResourceViews.tsx": "32", "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\apiService.ts": "33", "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\StoreContext.tsx": "34"}, {"size": 10034, "mtime": 1748497187290, "results": "35", "hashOfConfig": "36"}, {"size": 900, "mtime": 1747025003991, "results": "37", "hashOfConfig": "36"}, {"size": 13976, "mtime": 1747164779880, "results": "38", "hashOfConfig": "36"}, {"size": 25116, "mtime": 1749317605030, "results": "39", "hashOfConfig": "36"}, {"size": 31588, "mtime": 1747983161828, "results": "40", "hashOfConfig": "36"}, {"size": 16022, "mtime": 1747075390431, "results": "41", "hashOfConfig": "36"}, {"size": 34688, "mtime": 1749317988121, "results": "42", "hashOfConfig": "36"}, {"size": 18989, "mtime": 1748540038463, "results": "43", "hashOfConfig": "36"}, {"size": 15793, "mtime": 1747161027423, "results": "44", "hashOfConfig": "36"}, {"size": 20182, "mtime": 1746375882633, "results": "45", "hashOfConfig": "36"}, {"size": 1146, "mtime": 1746815750494, "results": "46", "hashOfConfig": "36"}, {"size": 8515, "mtime": 1747984862033, "results": "47", "hashOfConfig": "36"}, {"size": 517, "mtime": 1746771686119, "results": "48", "hashOfConfig": "36"}, {"size": 2800, "mtime": 1746778526099, "results": "49", "hashOfConfig": "36"}, {"size": 7982, "mtime": 1747238340069, "results": "50", "hashOfConfig": "36"}, {"size": 7115, "mtime": 1747075432109, "results": "51", "hashOfConfig": "36"}, {"size": 13718, "mtime": 1747189170657, "results": "52", "hashOfConfig": "36"}, {"size": 6385, "mtime": 1747075438709, "results": "53", "hashOfConfig": "36"}, {"size": 1636, "mtime": 1747190255805, "results": "54", "hashOfConfig": "36"}, {"size": 2249, "mtime": 1747035763628, "results": "55", "hashOfConfig": "36"}, {"size": 655, "mtime": 1746771679648, "results": "56", "hashOfConfig": "36"}, {"size": 4102, "mtime": 1745861698863, "results": "57", "hashOfConfig": "36"}, {"size": 3532, "mtime": 1747984892721, "results": "58", "hashOfConfig": "36"}, {"size": 7578, "mtime": 1747246665272, "results": "59", "hashOfConfig": "36"}, {"size": 33182, "mtime": 1748453043107, "results": "60", "hashOfConfig": "36"}, {"size": 2176, "mtime": 1745862703961, "results": "61", "hashOfConfig": "36"}, {"size": 3082, "mtime": 1749316113396, "results": "62", "hashOfConfig": "36"}, {"size": 572, "mtime": 1747059197868, "results": "63", "hashOfConfig": "36"}, {"size": 4046, "mtime": 1747178564399, "results": "64", "hashOfConfig": "36"}, {"size": 6944, "mtime": 1747166381625, "results": "65", "hashOfConfig": "36"}, {"size": 2706, "mtime": 1747158142441, "results": "66", "hashOfConfig": "36"}, {"size": 27654, "mtime": 1747184727078, "results": "67", "hashOfConfig": "36"}, {"size": 22073, "mtime": 1749316353352, "results": "68", "hashOfConfig": "36"}, {"size": 1942, "mtime": 1748406758368, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t9oth4", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\auth\\login\\page.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\layout.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\members\\page.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\page.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\pos\\page.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\products\\page.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\reports\\page.tsx", ["172", "173", "174", "175"], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\resources\\page.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\services\\page.tsx", [], ["176"], "D:\\yunsell\\evospace\\evospace-pos\\src\\app\\settings\\page.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\ConfirmDialog.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\layout\\MainLayout.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\CartTotal.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\MemberSelectionDialog.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\PaymentDialog.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\ProductSelectionDialog.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\ResourceFloorPlanView.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\ServiceSelectionDialog.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\pos\\SessionDuration.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\ProtectedPage.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\TabPanel.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\ThemeRegistry.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\apiClient.ts", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\mockData.ts", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\store.ts", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\theme.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\types.ts", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\resources\\index.ts", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\resources\\ResourceControls.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\resources\\ResourceDialog.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\resources\\ResourceFilters.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\resources\\ResourceViews.tsx", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\lib\\apiService.ts", [], [], "D:\\yunsell\\evospace\\evospace-pos\\src\\components\\StoreContext.tsx", [], [], {"ruleId": "177", "severity": 1, "message": "178", "line": 170, "column": 9, "nodeType": "179", "endLine": 214, "endColumn": 4, "suggestions": "180"}, {"ruleId": "181", "severity": 2, "message": "182", "line": 255, "column": 11, "nodeType": null, "messageId": "183", "endLine": 255, "endColumn": 27}, {"ruleId": "181", "severity": 2, "message": "184", "line": 260, "column": 35, "nodeType": null, "messageId": "183", "endLine": 260, "endColumn": 36}, {"ruleId": "181", "severity": 2, "message": "185", "line": 260, "column": 38, "nodeType": null, "messageId": "183", "endLine": 260, "endColumn": 43}, {"ruleId": "181", "severity": 2, "message": "186", "line": 158, "column": 17, "nodeType": null, "messageId": "183", "endLine": 158, "endColumn": 19, "suppressions": "187"}, "react-hooks/exhaustive-deps", "The 'fetchReportsData' function makes the dependencies of useEffect Hook (at line 219) change on every render. To fix this, wrap the definition of 'fetchReportsData' in its own useCallback() Hook.", "VariableDeclarator", ["188"], "@typescript-eslint/no-unused-vars", "'transactionCount' is assigned a value but never used.", "unusedVar", "'_' is defined but never used.", "'index' is defined but never used.", "'id' is assigned a value but never used.", ["189"], {"desc": "190", "fix": "191"}, {"kind": "192", "justification": "193"}, "Wrap the definition of 'fetchReportsData' in its own useCallback() Hook.", {"range": "194", "text": "195"}, "directive", "", [3847, 5445], "useCallback(async () => {\r\n    if (!startDate || !endDate) return;\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const startDateStr = format(startDate, 'yyyy-MM-dd');\r\n      const endDateStr = format(endDate, 'yyyy-MM-dd');\r\n\r\n      const [salesSummaryResponse, salesByCategoryResponse, salesByPaymentMethodResponse, productPerformanceResponse] = await Promise.all([\r\n        reportService.getSalesSummary({\r\n          start_date: startDateStr,\r\n          end_date: endDateStr\r\n        }),\r\n        reportService.getSalesByCategory({\r\n          start_date: startDateStr,\r\n          end_date: endDateStr\r\n        }),\r\n        reportService.getSalesByPaymentMethod({\r\n          start_date: startDateStr,\r\n          end_date: endDateStr\r\n        }),\r\n        reportService.getProductPerformance({\r\n          start_date: startDateStr,\r\n          end_date: endDateStr,\r\n          limit: 10\r\n        })\r\n      ]);\r\n\r\n      setReportsData({\r\n        salesSummary: salesSummaryResponse.success ? salesSummaryResponse.data : null,\r\n        salesByCategory: salesByCategoryResponse.success ? salesByCategoryResponse.data : null,\r\n        salesByPaymentMethod: salesByPaymentMethodResponse.success ? salesByPaymentMethodResponse.data : null,\r\n        productPerformance: productPerformanceResponse.success ? productPerformanceResponse.data : null,\r\n      });\r\n\r\n      setLastUpdated(new Date());\r\n    } catch (err) {\r\n      console.error('Error fetching reports data:', err);\r\n      setError('Failed to load reports data. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  })"]