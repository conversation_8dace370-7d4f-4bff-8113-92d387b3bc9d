(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{28890:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(57515),s=r(95155);let n=(0,a.A)((0,s.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit")},32922:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(57515),s=r(95155);let n=(0,a.A)((0,s.jsx)("path",{d:"M20 2H4c-1 0-2 .9-2 2v3.01c0 .72.43 1.34 1 1.69V20c0 1.1 1.1 2 2 2h14c.9 0 2-.9 2-2V8.7c.57-.35 1-.97 1-1.69V4c0-1.1-1-2-2-2m-5 12H9v-2h6zm5-7H4V4l16-.02z"}),"Inventory")},35884:(e,t,r)=>{"use strict";r.d(t,{D:()=>n}),r(95155);var a=r(12115);let s=(0,a.createContext)({mode:"light",toggleMode:()=>{},theme:"light",toggleTheme:()=>{}}),n=()=>(0,a.useContext)(s)},40857:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(57515),s=r(95155);let n=(0,a.A)((0,s.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"Add")},51935:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(57515),s=r(95155);let n=(0,a.A)((0,s.jsx)("path",{d:"M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3m3-10H5V5h10z"}),"Save")},62480:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var a=r(95155),s=r(12115),n=r(54581),i=r(700),o=r(68534),l=r(18407),c=r(78449),d=r(33989),x=r(27088),h=r(17348),p=r(68104),m=r(76380),A=r(41218),u=r(60785),g=r(16632),j=r(72705),v=r(41101),y=r(96490),f=r(36114),C=r(54492),b=r(99927),w=r(64329),k=r(71977),S=r(40857),I=r(63954),P=r(28890),z=r(19505),W=r(98028),M=r(32922);let L=(0,r(57515).A)((0,a.jsx)("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2M8.5 13.5l2.5 3.01L14.5 12l4.5 6H5z"}),"Image");var H=r(51935),V=r(2730),D=r(35884),E=r(98648);function N(){let{products:e,categories:t,addProduct:r,updateProduct:N,deleteProduct:R,fetchProducts:F,authUser:_}=(0,V.A)(),[T,q]=(0,s.useState)(""),[B,G]=(0,s.useState)("all"),[O,U]=(0,s.useState)(!1),[$,K]=(0,s.useState)(!1),[Y,J]=(0,s.useState)({name:"",category:"",price:"",stock:"",image:""}),[Q,X]=(0,s.useState)(!1),[Z,ee]=(0,s.useState)(null),{theme:et}=(0,D.D)();(0,s.useEffect)(()=>{_.token&&F()},[_.token,F]);let er=e.filter(e=>(e.name.toLowerCase().includes(T.toLowerCase())||e.category.toLowerCase().includes(T.toLowerCase()))&&("all"===B||e.category===B)),ea=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],r=arguments.length>1?arguments[1]:void 0;if(e&&r)J({id:r.id,name:r.name,category:r.category,price:r.price.toString(),stock:r.stock.toString(),image:r.image||""}),K(!0);else{var a;J({name:"",category:(null==(a=t[0])?void 0:a.name)||"",price:"",stock:"",image:""}),K(!1)}U(!0)},es=()=>{U(!1)},en=e=>{let{name:t,value:r}=e.target;J(e=>({...e,[t]:r}))},ei=async()=>{let e={name:Y.name,category:Y.category,price:parseFloat(Y.price),stock:parseInt(Y.stock),image:Y.image};try{$&&void 0!==Y.id?await N(Y.id,e):await r(e),es()}catch(e){console.error("Failed to save product:",e)}},eo=e=>{ee(e),X(!0)},el=async()=>{if(null!==Z)try{await R(Z),X(!1),ee(null)}catch(e){console.error("Failed to delete product:",e)}};return(0,a.jsxs)(n.A,{sx:{flexGrow:1},children:[(0,a.jsxs)(n.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,a.jsx)(i.A,{variant:"h4",children:"Products"}),(0,a.jsx)(o.A,{variant:"contained",startIcon:(0,a.jsx)(S.A,{}),onClick:()=>ea(),children:"Add Product"})]}),(0,a.jsx)(l.A,{sx:{p:2,mb:3},children:(0,a.jsx)(n.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:2},children:(0,a.jsxs)(n.A,{sx:{display:"flex",gap:2,flexGrow:1,maxWidth:{xs:"100%",sm:"70%"}},children:[(0,a.jsx)(c.A,{placeholder:"Search products...",value:T,onChange:e=>q(e.target.value),InputProps:{startAdornment:(0,a.jsx)(d.A,{position:"start",children:(0,a.jsx)(I.A,{})})},fullWidth:!0}),(0,a.jsxs)(x.A,{sx:{minWidth:"200px"},children:[(0,a.jsx)(h.A,{children:"Category"}),(0,a.jsxs)(p.A,{value:B,label:"Category",onChange:e=>G(e.target.value),children:[(0,a.jsx)(m.A,{value:"all",children:"All Categories"}),Array.from(new Set(e.map(e=>e.category))).sort().map(e=>(0,a.jsx)(m.A,{value:e,children:e},e))]})]})]})})}),(0,a.jsx)(n.A,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:er.length>0?er.map(e=>(0,a.jsx)(n.A,{sx:{flexBasis:{xs:"100%",sm:"45%",md:"30%",lg:"22%"}},children:(0,a.jsxs)(A.A,{elevation:2,sx:{height:"100%",display:"flex",flexDirection:"column",transition:"transform 0.2s, box-shadow 0.2s","&:hover":{transform:"translateY(-4px)",boxShadow:6}},children:[(0,a.jsx)(u.A,{sx:{height:160,backgroundColor:e.image?"grey.200":"dark"===et?"primary.dark":"primary.main",color:"white",display:"flex",alignItems:"center",justifyContent:"center",position:"relative"},children:e.image?(0,a.jsx)(n.A,{component:"img",sx:{height:140,width:"100%",objectFit:"cover",borderRadius:1},src:e.image||"/placeholder.jpg",alt:e.name}):(0,a.jsx)(n.A,{sx:{textAlign:"center",p:2,width:"100%",height:"100%",display:"flex",flexDirection:"column",justifyContent:"center"},children:(0,a.jsx)(i.A,{variant:"h5",component:"div",sx:{fontWeight:"bold",textShadow:"1px 1px 3px rgba(0,0,0,0.5)",mb:1},children:e.name})})}),(0,a.jsxs)(g.A,{sx:{flexGrow:1,display:"flex",flexDirection:"column",justifyContent:"space-between"},children:[(0,a.jsxs)(n.A,{children:[(0,a.jsx)(i.A,{gutterBottom:!0,variant:"h6",component:"div",children:e.name}),(0,a.jsxs)(j.A,{direction:"row",spacing:1,sx:{mb:2,flexWrap:"wrap",gap:1},children:[(0,a.jsx)(v.A,{label:e.category,size:"small"}),(0,a.jsx)(v.A,{label:"Stock: ".concat(e.stock),size:"small",color:e.stock>10?"success":e.stock>0?"warning":"error"})]})]}),(0,a.jsxs)(i.A,{variant:"h6",color:"primary",children:["$",Number(e.price).toFixed(2)]})]}),(0,a.jsxs)(y.A,{children:[(0,a.jsx)(o.A,{size:"small",startIcon:(0,a.jsx)(P.A,{}),onClick:()=>ea(!0,e),children:"Edit"}),(0,a.jsx)(o.A,{size:"small",color:"error",startIcon:(0,a.jsx)(z.A,{}),onClick:()=>eo(e.id),children:"Delete"})]})]})},e.id)):(0,a.jsx)(n.A,{sx:{width:"100%",p:4,textAlign:"center"},children:(0,a.jsxs)(l.A,{sx:{p:4,borderRadius:2},children:[(0,a.jsx)(i.A,{variant:"h6",color:"text.secondary",gutterBottom:!0,children:"No products found"}),(0,a.jsx)(i.A,{variant:"body1",color:"text.secondary",children:"Try adjusting your search or category filter, or add a new product."}),(0,a.jsx)(o.A,{variant:"contained",startIcon:(0,a.jsx)(S.A,{}),sx:{mt:2},onClick:()=>ea(),children:"Add Product"})]})})}),(0,a.jsxs)(f.A,{open:O,onClose:es,maxWidth:"sm",fullWidth:!0,children:[(0,a.jsx)(C.A,{children:(0,a.jsxs)(n.A,{sx:{display:"flex",alignItems:"center",gap:1},children:[$?(0,a.jsx)(P.A,{color:"primary"}):(0,a.jsx)(S.A,{color:"primary"}),(0,a.jsx)(i.A,{variant:"h6",children:$?"Edit Product":"Add New Product"})]})}),(0,a.jsx)(b.A,{dividers:!0,children:(0,a.jsxs)(j.A,{spacing:3,sx:{mt:1},children:[(0,a.jsx)(c.A,{label:"Product Name",name:"name",value:Y.name,onChange:en,fullWidth:!0,required:!0,autoFocus:!0,InputProps:{startAdornment:(0,a.jsx)(d.A,{position:"start",children:(0,a.jsx)(W.A,{fontSize:"small"})})}}),(0,a.jsxs)(x.A,{fullWidth:!0,required:!0,children:[(0,a.jsx)(h.A,{children:"Category"}),(0,a.jsx)(p.A,{name:"category",value:Y.category,label:"Category",onChange:e=>en(e),children:t.map(e=>(0,a.jsx)(m.A,{value:e.name,children:e.name},e.id))}),(0,a.jsx)(w.A,{children:"Select a product category"})]}),(0,a.jsx)(c.A,{label:"Price",name:"price",type:"number",value:Y.price,onChange:en,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,a.jsx)(d.A,{position:"start",children:"$"})}}),(0,a.jsx)(c.A,{label:"Stock",name:"stock",type:"number",value:Y.stock,onChange:en,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,a.jsx)(d.A,{position:"start",children:(0,a.jsx)(M.A,{fontSize:"small"})})}}),(0,a.jsx)(c.A,{label:"Image URL",name:"image",value:Y.image,onChange:en,fullWidth:!0,placeholder:"https://example.com/image.jpg",helperText:"Leave empty to use product name as display",InputProps:{startAdornment:(0,a.jsx)(d.A,{position:"start",children:(0,a.jsx)(L,{fontSize:"small","aria-label":"Product image URL"})})}}),Y.image&&(0,a.jsxs)(n.A,{sx:{width:"100%",textAlign:"center"},children:[(0,a.jsx)(n.A,{component:"img",src:Y.image,alt:"Preview of ".concat(Y.name||"product"),sx:{maxWidth:"100%",maxHeight:"150px",objectFit:"contain",borderRadius:1,border:"1px solid",borderColor:"divider"},onError:e=>{e.target.src="/placeholder.jpg"}}),(0,a.jsx)(i.A,{variant:"caption",color:"text.secondary",display:"block",children:"Image Preview"})]})]})}),(0,a.jsxs)(k.A,{sx:{px:3,py:2},children:[(0,a.jsx)(o.A,{onClick:es,color:"inherit",children:"Cancel"}),(0,a.jsx)(o.A,{onClick:ei,variant:"contained",disabled:!Y.name||!Y.category||!Y.price||!Y.stock,startIcon:$?(0,a.jsx)(H.A,{}):(0,a.jsx)(S.A,{}),children:$?"Update":"Add"})]})]}),(0,a.jsx)(E.A,{open:Q,title:"Confirm Delete",message:"Are you sure you want to delete this product? This action cannot be undone.",onConfirm:el,onCancel:()=>{X(!1),ee(null)},confirmText:"Delete",confirmButtonColor:"error"})]})}},92135:(e,t,r)=>{Promise.resolve().then(r.bind(r,62480))},96490:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var a=r(12115),s=r(52596),n=r(17472),i=r(75955),o=r(10186),l=r(55170),c=r(90870);function d(e){return(0,c.Ay)("MuiCardActions",e)}(0,l.A)("MuiCardActions",["root","spacing"]);var x=r(95155);let h=e=>{let{classes:t,disableSpacing:r}=e;return(0,n.A)({root:["root",!r&&"spacing"]},d,t)},p=(0,i.Ay)("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,variants:[{props:{disableSpacing:!1},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),m=a.forwardRef(function(e,t){let r=(0,o.b)({props:e,name:"MuiCardActions"}),{disableSpacing:a=!1,className:n,...i}=r,l={...r,disableSpacing:a},c=h(l);return(0,x.jsx)(p,{className:(0,s.A)(c.root,n),ownerState:l,ref:t,...i})})},98648:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(95155);r(12115);var s=r(36114),n=r(54492),i=r(99927),o=r(700),l=r(71977),c=r(68534);let d=e=>{let{open:t,title:r,message:d,onConfirm:x,onCancel:h,infoMode:p=!1,confirmText:m,confirmButtonColor:A="primary"}=e;return(0,a.jsxs)(s.A,{open:t,onClose:h,children:[(0,a.jsx)(n.A,{children:r}),(0,a.jsx)(i.A,{children:(0,a.jsx)(o.A,{children:d})}),(0,a.jsxs)(l.A,{children:[!p&&(0,a.jsx)(c.A,{onClick:h,children:"Cancel"}),(0,a.jsx)(c.A,{onClick:x,variant:"contained",color:p?"primary":A,children:p?m||"OK":m||"Confirm"})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[319,692,317,687,257,700,922,730,441,684,358],()=>t(92135)),_N_E=e.O()}]);