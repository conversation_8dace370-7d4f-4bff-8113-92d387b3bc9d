(()=>{var e={};e.id=807,e.ids=[807],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7937:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\yunsell\\\\evospace\\\\evospace-pos\\\\src\\\\app\\\\pos\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\pos\\page.tsx","default")},10078:(e,t,r)=>{"use strict";r.d(t,{A:()=>o}),r(43210);var a=r(23428),s=r(60687);let o=(0,a.A)((0,s.jsx)("path",{d:"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"}),"FirstPage")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33074:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eM});var a=r(60687),s=r(43210),o=r.n(s),i=r(23428);let n=(0,i.A)((0,a.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close");var l=r(41896),c=r(19257),d=r(24330),p=r(43323);let x=(0,i.A)((0,a.jsx)("path",{d:"M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2m0 14H4v-6h16zm0-10H4V6h16z"}),"Payment"),u=(0,i.A)((0,a.jsx)("path",{d:"M8 5v14l11-7z"}),"PlayArrow");var h=r(80986),m=r(86862),g=r(88931),b=r(87088),v=r(83685),y=r(16184),A=r(43291),f=r(62014),j=r(16393),C=r(51711),w=r(23789),S=r(52260),k=r(41629),$=r(47651),M=r(51067),P=r(11830),z=r(28840);let T=()=>{let{cartTotal:e,cart:t}=(0,z.A)(),[r,o]=(0,s.useState)(e());return(0,s.useEffect)(()=>{o(e())},[t,e]),(0,a.jsxs)(b.A,{variant:"h6",children:["$",r.toFixed(2)]})},R=({resource_id:e})=>{let[t,r]=(0,s.useState)(0),{getActiveSession:o}=(0,z.A)();(0,s.useEffect)(()=>{let t=o(e);if(!t)return void r(0);let a=()=>{r(Math.round((Date.now()-new Date(t.start_time).getTime())/6e4))};a();let s=setInterval(a,1e3);return()=>clearInterval(s)},[e,o]);let i=10*Math.ceil(t/10);return t<=0&&!o(e)?null:(0,a.jsxs)(a.Fragment,{children:[t," min",i>t&&(0,a.jsxs)(b.A,{component:"span",variant:"caption",sx:{ml:.5,opacity:.7},children:["(Billed: ",i," min)"]})]})},N=e=>{let t=Math.max(...e.resources.map(e=>(e.x||0)+(e.width||1)),0),r=Math.max(...e.resources.map(e=>(e.y||0)+(e.height||1)),0),o=50*r+(r-1)*10,[i,n]=(0,s.useState)(!1),[l,c]=(0,s.useState)({x:0,y:0}),[d,p]=(0,s.useState)({x:0,y:0}),x=(0,s.useRef)(null),u=t=>{let r="all"===e.currentResourceStatusFilter||t.status===e.currentResourceStatusFilter,a=""===e.currentSearchQuery||t.name.toLowerCase().includes(e.currentSearchQuery.toLowerCase()),s="all"===e.currentSelectedZone||null===e.currentSelectedZone||t.zone===e.currentSelectedZone;return r&&a&&s},h=e=>{let t=e.width||1,r=e.height||1;return{width:50*t+(t-1)*10,height:50*r+(r-1)*10}},m=e=>{let t=e.x||0;return{top:60*(e.y||0),left:60*t}},v=(e,t,r)=>{if(!r)return{bg:"rgba(189, 189, 189, 0.4)",border:"grey.400",glow:"none",textColor:"grey.700"};if(t)return{bg:"rgba(33, 150, 243, 0.9)",border:"primary.main",glow:"0 0 15px rgba(33, 150, 243, 0.5)",textColor:"white"};switch(e){case"available":return{bg:"rgba(76, 175, 80, 0.7)",border:"success.main",glow:"none",textColor:"white"};case"booked":return{bg:"rgba(244, 67, 54, 0.7)",border:"error.main",glow:"none",textColor:"white"};case"maintenance":return{bg:"rgba(255, 152, 0, 0.7)",border:"warning.main",glow:"none",textColor:"white"};case"in-use":return{bg:"rgba(33, 150, 243, 0.7)",border:"primary.main",glow:"none",textColor:"white"};default:return{bg:"rgba(158, 158, 158, 0.7)",border:"text.secondary",glow:"none",textColor:"white"}}};return(0,s.useEffect)(()=>{let e=()=>{i&&n(!1)};return window.addEventListener("mouseup",e),()=>{window.removeEventListener("mouseup",e)}},[i]),(0,a.jsxs)(g.A,{className:"floor-plan-container",sx:{position:"relative",width:"100%",minHeight:`${Math.max(200,o+100)}px`,border:"1px solid #ccc",borderRadius:1,overflow:"hidden",backgroundColor:"#f5f5f5",mb:3,p:2,cursor:i?"grabbing":"grab"},onMouseDown:t=>{let r=t.target;(r.classList.contains("floor-plan-container")||r.classList.contains("grid-background"))&&(e.onBackgroundClick&&e.onBackgroundClick(t),n(!0),c({x:t.clientX-d.x,y:t.clientY-d.y}))},onMouseMove:e=>{i&&p({x:e.clientX-l.x,y:e.clientY-l.y})},onMouseUp:()=>{n(!1)},ref:x,children:[(0,a.jsx)(g.A,{sx:{position:"absolute",top:10,right:10,backgroundColor:"rgba(255, 255, 255, 0.8)",padding:1,borderRadius:1,zIndex:10,fontSize:"0.75rem",boxShadow:1},children:(0,a.jsx)(b.A,{variant:"caption",display:"block",children:"• Click and drag to move the canvas"})}),(0,a.jsxs)(g.A,{sx:{position:"relative",width:`${50*t+(t-1)*10}px`,height:`${o}px`,margin:"0 auto",transform:`translate(${d.x}px, ${d.y}px)`,transformOrigin:"0 0",transition:i?"none":"transform 0.1s ease-out"},children:[(0,a.jsx)(g.A,{className:"grid-background",sx:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backgroundImage:"linear-gradient(#ddd 1px, transparent 1px), linear-gradient(90deg, #ddd 1px, transparent 1px)",backgroundSize:"50px 50px",opacity:.5}}),e.resources.filter(t=>"all"===e.currentSelectedFloor||t.floor?.toString()===e.currentSelectedFloor.toString()).map(t=>{let r=h(t),s=m(t),o=void 0!==e.getActiveSession(t.id),i=u(t),n=v(t.status,o,i),l=`T-${t.id.toString().padStart(2,"0")}`,c=e.getActiveSession(t.id);return(0,a.jsxs)(g.A,{className:`resource-item ${e.selectedResource===t.id?"selected":""}`,onClick:()=>{i&&(e.getActiveSession(t.id)||"available"===t.status?e.onResourceSelect(t.id):e.onShowInfoAlert("Resource Unavailable",`Resource ${t.name} is currently ${t.status} and cannot be selected.`))},sx:{position:"absolute",top:s.top,left:s.left,width:r.width,height:r.height,backgroundColor:n.bg,border:"2px solid",borderColor:e.selectedResource===t.id&&i?"primary.dark":n.border,borderRadius:2,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",cursor:i?"pointer":"default",boxShadow:e.selectedResource===t.id&&i?n.glow:i?"2px 2px 5px rgba(0,0,0,0.1)":"none",transition:"all 0.2s ease",opacity:i?1:.6,"&:hover":i?{transform:"translateY(-2px)",boxShadow:`0 4px 12px ${"text.secondary"===n.border?"rgba(0,0,0,0.2)":n.border}`}:{},p:1,textAlign:"center"},children:[(0,a.jsx)(b.A,{variant:"body2",sx:{color:n.textColor,fontWeight:"bold",fontSize:"0.8rem",mb:.5},children:l}),(0,a.jsxs)(g.A,{sx:{display:"flex",flexDirection:"column",alignItems:"center"},children:[r.width>=100&&r.height>=40&&(0,a.jsx)(b.A,{variant:"body2",sx:{color:n.textColor,textAlign:"center",fontSize:"0.7rem",mt:.25},children:t.name}),c&&i&&(0,a.jsx)(b.A,{component:"div",variant:"body2",sx:{color:n.textColor,textAlign:"center",fontSize:"0.7rem",mt:.5,fontWeight:"bold"},children:(0,a.jsx)(R,{resource_id:t.id})})]}),("available"!==t.status||o)&&i&&(0,a.jsx)(g.A,{sx:{position:"absolute",top:5,right:5,width:10,height:10,borderRadius:"50%",backgroundColor:o||"in-use"===t.status?"primary.main":"booked"===t.status?"error.main":"warning.main",boxShadow:o?"0 0 10px rgba(33, 150, 243, 0.7)":"0 0 5px rgba(0,0,0,0.3)",animation:o?"pulse 2s infinite":"none","@keyframes pulse":{"0%":{boxShadow:"0 0 0 0 rgba(33, 150, 243, 0.7)"},"70%":{boxShadow:"0 0 0 10px rgba(33, 150, 243, 0)"},"100%":{boxShadow:"0 0 0 0 rgba(33, 150, 243, 0)"}}}})]},t.id)})]})]})};var I=r(54534),W=r(12362),F=r(24296),D=r(90764),L=r(27674);let O=({open:e,onClose:t,cart:r,cartTotal:s,getActiveSession:o,paymentMethod:i,setPaymentMethod:n,paymentAmount:l,setPaymentAmount:c,onCompleteTransaction:d})=>{let p=s();return(0,a.jsxs)(W.A,{open:e,onClose:t,maxWidth:"xs",fullWidth:!0,children:[(0,a.jsx)(F.A,{children:"Complete Payment"}),(0,a.jsxs)(D.A,{children:[(0,a.jsxs)(g.A,{sx:{mb:3},children:[(0,a.jsx)(b.A,{variant:"body1",gutterBottom:!0,children:"Total Amount:"}),(0,a.jsxs)(g.A,{children:[(0,a.jsxs)(b.A,{variant:"h4",color:"primary",children:["$",p.toFixed(2)]}),(0,a.jsxs)(g.A,{sx:{mt:2,maxHeight:"200px",overflowY:"auto"},children:[" ",(0,a.jsx)(b.A,{variant:"subtitle2",color:"text.secondary",sx:{mb:1},children:"Session Details:"}),r.map(e=>{let t=o(e.id);if(t){let r=Math.round((Date.now()-new Date(t.start_time).getTime())/6e4),s=10*Math.ceil(r/10),o=s/60,i=o*e.price;return(0,a.jsxs)(g.A,{sx:{mb:2,fontSize:"0.875rem"},children:[" ",(0,a.jsx)(b.A,{variant:"body2",color:"text.secondary",sx:{fontWeight:"bold"},children:e.name}),(0,a.jsxs)(b.A,{variant:"caption",display:"block",color:"text.secondary",sx:{pl:2},children:["• Duration: ",r," min"]}),(0,a.jsxs)(b.A,{variant:"caption",display:"block",color:"text.secondary",sx:{pl:2},children:["• Billed: ",s," min (",o.toFixed(2)," hrs)"]}),(0,a.jsxs)(b.A,{variant:"caption",display:"block",color:"primary",sx:{pl:2,mb:.5},children:["Resource Total: $",i.toFixed(2)]}),t.services.length>0&&(0,a.jsxs)(g.A,{sx:{mt:.5},children:[(0,a.jsxs)(b.A,{variant:"caption",display:"block",color:"text.secondary",sx:{fontWeight:"bold",pl:2},children:["Services for ",e.name,":"]}),t.services.map(e=>{let t=Number(e.price),r=Number(e.quantity),s=t*r;return(0,a.jsxs)(b.A,{variant:"caption",display:"block",color:"text.secondary",sx:{pl:3},children:["• ",e.name,": $",isNaN(t)?"0.00":t.toFixed(2)," x ",isNaN(r)?1:r," = $",isNaN(s)?"0.00":s.toFixed(2)]},`service-${e.id}`)})]}),t.products.length>0&&(0,a.jsxs)(g.A,{sx:{mt:.5},children:[(0,a.jsxs)(b.A,{variant:"caption",display:"block",color:"text.secondary",sx:{fontWeight:"bold",pl:2},children:["Products for ",e.name,":"]}),t.products.map(e=>{let t=Number(e.price),r=Number(e.quantity),s=t*r;return(0,a.jsxs)(b.A,{variant:"caption",display:"block",color:"text.secondary",sx:{pl:3},children:["• ",e.name,": $",isNaN(t)?"0.00":t.toFixed(2)," x ",isNaN(r)?1:r," = $",isNaN(s)?"0.00":s.toFixed(2)]},`product-${e.id}`)})]})]},e.id)}return null})]})]})]}),(0,a.jsx)(b.A,{variant:"body1",gutterBottom:!0,children:"Payment Method:"}),(0,a.jsxs)(P.A,{direction:"row",spacing:1,sx:{mb:3},children:[(0,a.jsx)(y.A,{variant:"cash"===i?"contained":"outlined",onClick:()=>n("cash"),children:"Cash"}),(0,a.jsx)(y.A,{variant:"card"===i?"contained":"outlined",onClick:()=>n("card"),children:"Card"}),(0,a.jsx)(y.A,{variant:"mobile"===i?"contained":"outlined",onClick:()=>n("mobile"),children:"Mobile"})]}),"cash"===i&&(0,a.jsx)(j.A,{label:"Amount Received",type:"number",fullWidth:!0,value:l,onChange:e=>c(e.target.value),InputProps:{startAdornment:(0,a.jsx)(C.A,{position:"start",children:"$"}),inputProps:{min:0}},sx:{mb:2}}),"cash"===i&&parseFloat(l)>=p&&""!==l&&(0,a.jsx)(g.A,{sx:{mb:2},children:(0,a.jsxs)(b.A,{variant:"body2",children:["Change: $",(parseFloat(l)-p).toFixed(2)]})})]}),(0,a.jsxs)(L.A,{children:[(0,a.jsx)(y.A,{onClick:t,children:"Cancel"}),(0,a.jsx)(y.A,{onClick:d,variant:"contained",disabled:"cash"===i&&(parseFloat(l)<p||""===l),children:"Complete Transaction"})]})]})};var _=r(45525),E=r(64216),B=r(93010),G=r(12879),q=r(76533);let X=({open:e,onClose:t,members:r,onSelectMember:s,onSelectWalkIn:o,currentMemberId:i})=>(0,a.jsxs)(W.A,{open:e,onClose:t,maxWidth:"lg",fullWidth:!0,children:[(0,a.jsx)(F.A,{children:"Select Member"}),(0,a.jsxs)(D.A,{children:[(0,a.jsx)(j.A,{autoFocus:!0,margin:"dense",label:"Search Members",type:"text",fullWidth:!0,variant:"outlined",InputProps:{startAdornment:(0,a.jsx)(C.A,{position:"start",children:(0,a.jsx)(l.A,{})})},sx:{mb:2}}),(0,a.jsxs)(_.A,{sx:{pt:0},children:[(0,a.jsx)(E.A,{onClick:o,selected:null===i,children:(0,a.jsx)(B.A,{primary:"No Member (Walk-in)"})}),r.map(e=>(0,a.jsxs)(E.A,{onClick:()=>s(e),selected:i===e.id,children:[(0,a.jsx)(G.A,{sx:{mr:2,bgcolor:"primary.main"},children:e.name.charAt(0)}),(0,a.jsx)(B.A,{primary:e.name,secondary:e.email}),void 0!==e.visits&&e.visits>0&&(0,a.jsx)(q.A,{label:`Visits: ${e.visits}`,size:"small",sx:{mr:1}})]},e.id))]})]}),(0,a.jsx)(L.A,{children:(0,a.jsx)(y.A,{onClick:t,children:"Cancel"})})]});var V=r(49384),Y=r(99282),Z=r(4144),H=r(82816);function U(e){return(0,H.Ay)("MuiPagination",e)}(0,Z.A)("MuiPagination",["root","ul","outlined","text"]);var Q=r(13560),K=r(2899),J=r(71779);function ee(e){return(0,H.Ay)("MuiPaginationItem",e)}let et=(0,Z.A)("MuiPaginationItem",["root","page","sizeSmall","sizeLarge","text","textPrimary","textSecondary","outlined","outlinedPrimary","outlinedSecondary","rounded","ellipsis","firstLast","previousNext","focusVisible","disabled","selected","icon","colorPrimary","colorSecondary"]);var er=r(30748),ea=r(61543),es=r(48285),eo=r(10078),ei=r(56676);let en=(0,i.A)((0,a.jsx)("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"}),"NavigateBefore"),el=(0,i.A)((0,a.jsx)("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"}),"NavigateNext");var ec=r(34414),ed=r(13555),ep=r(45258),ex=r(84754);let eu=(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],t[`size${(0,ea.A)(r.size)}`],"text"===r.variant&&t[`text${(0,ea.A)(r.color)}`],"outlined"===r.variant&&t[`outlined${(0,ea.A)(r.color)}`],"rounded"===r.shape&&t.rounded,"page"===r.type&&t.page,("start-ellipsis"===r.type||"end-ellipsis"===r.type)&&t.ellipsis,("previous"===r.type||"next"===r.type)&&t.previousNext,("first"===r.type||"last"===r.type)&&t.firstLast]},eh=e=>{let{classes:t,color:r,disabled:a,selected:s,size:o,shape:i,type:n,variant:l}=e,c={root:["root",`size${(0,ea.A)(o)}`,l,i,"standard"!==r&&`color${(0,ea.A)(r)}`,"standard"!==r&&`${l}${(0,ea.A)(r)}`,a&&"disabled",s&&"selected",{page:"page",first:"firstLast",last:"firstLast","start-ellipsis":"ellipsis","end-ellipsis":"ellipsis",previous:"previousNext",next:"previousNext"}[n]],icon:["icon"]};return(0,Y.A)(c,ee,t)},em=(0,ed.Ay)("div",{name:"MuiPaginationItem",slot:"Root",overridesResolver:eu})((0,ep.A)(({theme:e})=>({...e.typography.body2,borderRadius:16,textAlign:"center",boxSizing:"border-box",minWidth:32,padding:"0 6px",margin:"0 3px",color:(e.vars||e).palette.text.primary,height:"auto",[`&.${et.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},variants:[{props:{size:"small"},style:{minWidth:26,borderRadius:13,margin:"0 1px",padding:"0 4px"}},{props:{size:"large"},style:{minWidth:40,borderRadius:20,padding:"0 10px",fontSize:e.typography.pxToRem(15)}}]}))),eg=(0,ed.Ay)(er.A,{name:"MuiPaginationItem",slot:"Root",overridesResolver:eu})((0,ep.A)(({theme:e})=>({...e.typography.body2,borderRadius:16,textAlign:"center",boxSizing:"border-box",minWidth:32,height:32,padding:"0 6px",margin:"0 3px",color:(e.vars||e).palette.text.primary,[`&.${et.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${et.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},transition:e.transitions.create(["color","background-color"],{duration:e.transitions.duration.short}),"&:hover":{backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${et.selected}`]:{backgroundColor:(e.vars||e).palette.action.selected,"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:(0,K.X4)(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:(e.vars||e).palette.action.selected}},[`&.${et.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.action.selectedChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:(0,K.X4)(e.palette.action.selected,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)},[`&.${et.disabled}`]:{opacity:1,color:(e.vars||e).palette.action.disabled,backgroundColor:(e.vars||e).palette.action.selected}},variants:[{props:{size:"small"},style:{minWidth:26,height:26,borderRadius:13,margin:"0 1px",padding:"0 4px"}},{props:{size:"large"},style:{minWidth:40,height:40,borderRadius:20,padding:"0 10px",fontSize:e.typography.pxToRem(15)}},{props:{shape:"rounded"},style:{borderRadius:(e.vars||e).shape.borderRadius}},{props:{variant:"outlined"},style:{border:e.vars?`1px solid rgba(${e.vars.palette.common.onBackgroundChannel} / 0.23)`:`1px solid ${"light"===e.palette.mode?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)"}`,[`&.${et.selected}`]:{[`&.${et.disabled}`]:{borderColor:(e.vars||e).palette.action.disabledBackground,color:(e.vars||e).palette.action.disabled}}}},{props:{variant:"text"},style:{[`&.${et.selected}`]:{[`&.${et.disabled}`]:{color:(e.vars||e).palette.action.disabled}}}},...Object.entries(e.palette).filter((0,es.A)(["dark","contrastText"])).map(([t])=>({props:{variant:"text",color:t},style:{[`&.${et.selected}`]:{color:(e.vars||e).palette[t].contrastText,backgroundColor:(e.vars||e).palette[t].main,"&:hover":{backgroundColor:(e.vars||e).palette[t].dark,"@media (hover: none)":{backgroundColor:(e.vars||e).palette[t].main}},[`&.${et.focusVisible}`]:{backgroundColor:(e.vars||e).palette[t].dark},[`&.${et.disabled}`]:{color:(e.vars||e).palette.action.disabled}}}})),...Object.entries(e.palette).filter((0,es.A)(["light"])).map(([t])=>({props:{variant:"outlined",color:t},style:{[`&.${et.selected}`]:{color:(e.vars||e).palette[t].main,border:`1px solid ${e.vars?`rgba(${e.vars.palette[t].mainChannel} / 0.5)`:(0,K.X4)(e.palette[t].main,.5)}`,backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.activatedOpacity})`:(0,K.X4)(e.palette[t].main,e.palette.action.activatedOpacity),"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / calc(${e.vars.palette.action.activatedOpacity} + ${e.vars.palette.action.focusOpacity}))`:(0,K.X4)(e.palette[t].main,e.palette.action.activatedOpacity+e.palette.action.focusOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${et.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / calc(${e.vars.palette.action.activatedOpacity} + ${e.vars.palette.action.focusOpacity}))`:(0,K.X4)(e.palette[t].main,e.palette.action.activatedOpacity+e.palette.action.focusOpacity)}}}}))]}))),eb=(0,ed.Ay)("div",{name:"MuiPaginationItem",slot:"Icon"})((0,ep.A)(({theme:e})=>({fontSize:e.typography.pxToRem(20),margin:"0 -8px",variants:[{props:{size:"small"},style:{fontSize:e.typography.pxToRem(18)}},{props:{size:"large"},style:{fontSize:e.typography.pxToRem(22)}}]}))),ev=s.forwardRef(function(e,t){let r=(0,ex.b)({props:e,name:"MuiPaginationItem"}),{className:s,color:o="standard",component:i,components:n={},disabled:l=!1,page:c,selected:d=!1,shape:p="circular",size:x="medium",slots:u={},slotProps:h={},type:m="page",variant:g="text",...b}=r,v={...r,color:o,disabled:l,selected:d,shape:p,size:x,type:m,variant:g},y=(0,J.I)(),A=eh(v),f={slots:{previous:u.previous??n.previous,next:u.next??n.next,first:u.first??n.first,last:u.last??n.last},slotProps:h},[j,C]=(0,ec.A)("previous",{elementType:en,externalForwardedProps:f,ownerState:v}),[w,S]=(0,ec.A)("next",{elementType:el,externalForwardedProps:f,ownerState:v}),[k,$]=(0,ec.A)("first",{elementType:eo.A,externalForwardedProps:f,ownerState:v}),[M,P]=(0,ec.A)("last",{elementType:ei.A,externalForwardedProps:f,ownerState:v}),z=y?({previous:"next",next:"previous",first:"last",last:"first"})[m]:m,T={previous:j,next:w,first:k,last:M}[z],R={previous:C,next:S,first:$,last:P}[z];return"start-ellipsis"===m||"end-ellipsis"===m?(0,a.jsx)(em,{ref:t,ownerState:v,className:(0,V.A)(A.root,s),children:"…"}):(0,a.jsxs)(eg,{ref:t,ownerState:v,component:i,disabled:l,className:(0,V.A)(A.root,s),...b,children:["page"===m&&c,T?(0,a.jsx)(eb,{...R,className:A.icon,as:T}):null]})}),ey=e=>{let{classes:t,variant:r}=e;return(0,Y.A)({root:["root",r],ul:["ul"]},U,t)},eA=(0,ed.Ay)("nav",{name:"MuiPagination",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant]]}})({}),ef=(0,ed.Ay)("ul",{name:"MuiPagination",slot:"Ul"})({display:"flex",flexWrap:"wrap",alignItems:"center",padding:0,margin:0,listStyle:"none"});function ej(e,t,r){return"page"===e?`${r?"":"Go to "}page ${t}`:`Go to ${e} page`}let eC=s.forwardRef(function(e,t){let r=(0,ex.b)({props:e,name:"MuiPagination"}),{boundaryCount:s=1,className:o,color:i="standard",count:n=1,defaultPage:l=1,disabled:c=!1,getItemAriaLabel:d=ej,hideNextButton:p=!1,hidePrevButton:x=!1,onChange:u,page:h,renderItem:m=e=>(0,a.jsx)(ev,{...e}),shape:g="circular",showFirstButton:b=!1,showLastButton:v=!1,siblingCount:y=1,size:A="medium",variant:f="text",...j}=r,{items:C}=function(e={}){let{boundaryCount:t=1,componentName:r="usePagination",count:a=1,defaultPage:s=1,disabled:o=!1,hideNextButton:i=!1,hidePrevButton:n=!1,onChange:l,page:c,showFirstButton:d=!1,showLastButton:p=!1,siblingCount:x=1,...u}=e,[h,m]=(0,Q.A)({controlled:c,default:s,name:r,state:"page"}),g=(e,t)=>{c||m(t),l&&l(e,t)},b=(e,t)=>Array.from({length:t-e+1},(t,r)=>e+r),v=b(1,Math.min(t,a)),y=b(Math.max(a-t+1,t+1),a),A=Math.max(Math.min(h-x,a-t-2*x-1),t+2),f=Math.min(Math.max(h+x,t+2*x+2),a-t-1),j=[...d?["first"]:[],...n?[]:["previous"],...v,...A>t+2?["start-ellipsis"]:t+1<a-t?[t+1]:[],...b(A,f),...f<a-t-1?["end-ellipsis"]:a-t>t?[a-t]:[],...y,...i?[]:["next"],...p?["last"]:[]],C=e=>{switch(e){case"first":return 1;case"previous":return h-1;case"next":return h+1;case"last":return a;default:return null}};return{items:j.map(e=>"number"==typeof e?{onClick:t=>{g(t,e)},type:"page",page:e,selected:e===h,disabled:o,"aria-current":e===h?"page":void 0}:{onClick:t=>{g(t,C(e))},type:e,page:C(e),selected:!1,disabled:o||!e.includes("ellipsis")&&("next"===e||"last"===e?h>=a:h<=1)}),...u}}({...r,componentName:"Pagination"}),w={...r,boundaryCount:s,color:i,count:n,defaultPage:l,disabled:c,getItemAriaLabel:d,hideNextButton:p,hidePrevButton:x,renderItem:m,shape:g,showFirstButton:b,showLastButton:v,siblingCount:y,size:A,variant:f},S=ey(w);return(0,a.jsx)(eA,{"aria-label":"pagination navigation",className:(0,V.A)(S.root,o),ownerState:w,ref:t,...j,children:(0,a.jsx)(ef,{className:S.ul,ownerState:w,children:C.map((e,t)=>(0,a.jsx)("li",{children:m({...e,color:i,"aria-label":d(e.type,e.page,e.selected),shape:g,size:A,variant:f})},t))})})}),ew=({open:e,onClose:t,services:r,onSelectService:i,title:n="Select Service"})=>{let[c,d]=(0,s.useState)(""),[p,x]=(0,s.useState)("all"),[u,v]=(0,s.useState)(1),A=(0,s.useMemo)(()=>r?Array.from(new Set(r.map(e=>e.category).filter(Boolean))).sort():[],[r]),f=(0,s.useMemo)(()=>r.filter(e=>{let t=e.name.toLowerCase().includes(c.toLowerCase())||e.description&&e.description.toLowerCase().includes(c.toLowerCase()),r="all"===p||e.category===p;return t&&r}),[r,c,p]),M=(0,s.useMemo)(()=>{let e=(u-1)*8;return f.slice(e,e+8)},[f,u]),P=Math.ceil(f.length/8),z=e=>{i(e),d(""),x("all"),v(1),t()},T=()=>{d(""),x("all"),v(1),t()};return o().useEffect(()=>{v(1)},[c,p]),(0,a.jsxs)(W.A,{open:e,onClose:T,maxWidth:"lg",fullWidth:!0,scroll:"paper",children:[(0,a.jsx)(F.A,{children:n}),(0,a.jsxs)(D.A,{sx:{pb:1,display:"flex",flexDirection:"column"},children:[(0,a.jsxs)(g.A,{sx:{display:"flex",gap:2,mb:2,position:"sticky",top:0,bgcolor:"background.paper",zIndex:1,pt:1,pb:1},children:[(0,a.jsx)(j.A,{autoFocus:!0,margin:"none",label:"Search services...",type:"text",fullWidth:!0,variant:"outlined",value:c,onChange:e=>d(e.target.value),InputProps:{startAdornment:(0,a.jsx)(C.A,{position:"start",children:(0,a.jsx)(l.A,{})})},sx:{flexGrow:2}}),(0,a.jsxs)(w.A,{sx:{minWidth:200,flexGrow:1},children:[(0,a.jsx)(S.A,{children:"Service Type"}),(0,a.jsxs)(k.A,{value:p,label:"Service Type",onChange:e=>x(e.target.value),children:[(0,a.jsx)($.A,{value:"all",children:"All Types"}),A.map(e=>(0,a.jsx)($.A,{value:e,children:e},e))]})]})]}),(0,a.jsx)(g.A,{sx:{flexGrow:1,overflowY:"auto",pb:1},children:M.length>0?(0,a.jsx)(g.A,{sx:{display:"flex",flexWrap:"wrap",gap:2,justifyContent:"flex-start",pt:1},children:M.map(e=>(0,a.jsx)(h.A,{sx:{width:200,cursor:"pointer",transition:"transform 0.2s","&:hover":{transform:"scale(1.03)",boxShadow:3}},onClick:()=>z(e),children:(0,a.jsxs)(m.A,{children:[(0,a.jsx)(b.A,{gutterBottom:!0,variant:"h6",component:"div",children:e.name}),(0,a.jsx)(b.A,{variant:"body2",color:"text.secondary",children:e.duration?`${e.duration} minutes`:e.unit?`Per ${e.unit}`:"Fixed price"}),(0,a.jsxs)(b.A,{variant:"h6",color:"primary",sx:{mt:1},children:["$",Number(e.price).toFixed(2)]})]})},e.id))}):(0,a.jsx)(b.A,{sx:{textAlign:"center",mt:2},children:c||"all"!==p?"No services match your search or filter.":"No services available."})})]}),(0,a.jsxs)(L.A,{sx:{justifyContent:"flex-end",alignItems:"center",pt:1,pb:1,gap:2},children:[P>1&&(0,a.jsx)(eC,{count:P,page:u,onChange:(e,t)=>{v(t)},color:"primary",size:"small",sx:{mr:"auto"}}),(0,a.jsx)(y.A,{onClick:T,children:"Cancel"})]})]})};var eS=r(70031);let ek=({open:e,onClose:t,products:r,onSelectProduct:i,title:n="Select Product"})=>{let[c,d]=(0,s.useState)(""),[p,x]=(0,s.useState)("all"),[u,v]=(0,s.useState)(1),A=(0,s.useMemo)(()=>r?Array.from(new Set(r.map(e=>e.category))).sort():[],[r]),f=(0,s.useMemo)(()=>r.filter(e=>{let t=e.name.toLowerCase().includes(c.toLowerCase())||e.category.toLowerCase().includes(c.toLowerCase()),r="all"===p||e.category===p;return t&&r}),[r,c,p]),M=(0,s.useMemo)(()=>{let e=(u-1)*8;return f.slice(e,e+8)},[f,u]),P=Math.ceil(f.length/8),z=e=>{i(e),d(""),x("all"),v(1),t()},T=()=>{d(""),x("all"),v(1),t()};return o().useEffect(()=>{v(1)},[c,p]),(0,a.jsxs)(W.A,{open:e,onClose:T,maxWidth:"lg",fullWidth:!0,scroll:"paper",children:[(0,a.jsx)(F.A,{children:n}),(0,a.jsxs)(D.A,{sx:{pb:1,display:"flex",flexDirection:"column"},children:[(0,a.jsxs)(g.A,{sx:{display:"flex",gap:2,mb:2,position:"sticky",top:0,bgcolor:"background.paper",zIndex:1,pt:1,pb:1},children:[(0,a.jsx)(j.A,{autoFocus:!0,margin:"none",label:"Search products...",type:"text",fullWidth:!0,variant:"outlined",value:c,onChange:e=>d(e.target.value),InputProps:{startAdornment:(0,a.jsx)(C.A,{position:"start",children:(0,a.jsx)(l.A,{})})},sx:{flexGrow:2}}),(0,a.jsxs)(w.A,{sx:{minWidth:200,flexGrow:1},children:[(0,a.jsx)(S.A,{children:"Category"}),(0,a.jsxs)(k.A,{value:p,label:"Category",onChange:e=>x(e.target.value),children:[(0,a.jsx)($.A,{value:"all",children:"All Categories"}),A.map(e=>(0,a.jsx)($.A,{value:e,children:e},e))]})]})]}),(0,a.jsx)(g.A,{sx:{flexGrow:1,overflowY:"auto",pb:1},children:M.length>0?(0,a.jsx)(g.A,{sx:{display:"flex",flexWrap:"wrap",gap:2,justifyContent:"flex-start",pt:1},children:M.map(e=>(0,a.jsxs)(h.A,{sx:{width:200,cursor:"pointer",transition:"transform 0.2s","&:hover":{transform:"scale(1.03)",boxShadow:3}},onClick:()=>z(e),children:[e.image?(0,a.jsx)(eS.A,{component:"img",height:"140",image:e.image,alt:e.name}):(0,a.jsx)(g.A,{sx:{height:140,display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"primary.light",color:"white"},children:(0,a.jsx)(b.A,{variant:"h6",align:"center",sx:{p:2},children:e.name})}),(0,a.jsxs)(m.A,{children:[(0,a.jsx)(b.A,{gutterBottom:!0,variant:"h6",component:"div",children:e.name}),(0,a.jsx)(b.A,{variant:"body2",color:"text.secondary",children:e.category}),(0,a.jsxs)(b.A,{variant:"h6",color:"primary",sx:{mt:1},children:["$",Number(e.price).toFixed(2)]})]})]},e.id))}):(0,a.jsx)(b.A,{sx:{textAlign:"center",mt:2},children:c||"all"!==p?"No products match your search or filter.":"No products available."})})]}),(0,a.jsxs)(L.A,{sx:{justifyContent:"flex-end",alignItems:"center",pt:1,pb:1,gap:2},children:[P>1&&(0,a.jsx)(eC,{count:P,page:u,onChange:(e,t)=>{v(t)},color:"primary",size:"small",sx:{mr:"auto"}}),(0,a.jsx)(y.A,{onClick:T,children:"Cancel"})]})]})},e$=o().memo(({item:e,resourceDetails:t,session:r,formatCurrency:s,onRemoveItem:o,onAddServiceToSession:i,onAddProductToSession:l})=>(0,a.jsx)(h.A,{sx:{mb:2,bgcolor:"primary.light",color:"primary.contrastText"},children:(0,a.jsxs)(m.A,{sx:{px:1,pt:1,pb:2},children:[(0,a.jsxs)(g.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,a.jsxs)(g.A,{sx:{flexGrow:1,mr:1},children:[(0,a.jsx)(b.A,{variant:"subtitle1",sx:{fontWeight:"bold"},children:e.name}),(0,a.jsxs)(b.A,{variant:"body2",sx:{color:"primary.contrastText"},children:["$",Number(e.price).toFixed(2),"/hr • ",t?.floor," • ",t?.zone]})]}),(0,a.jsx)(v.A,{edge:"end","aria-label":"delete",onClick:()=>o(e.id),sx:{color:"primary.contrastText",mr:.1},children:(0,a.jsx)(n,{})})]}),r&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(b.A,{variant:"body2",component:"span",sx:{display:"block",color:"primary.contrastText",opacity:.7},children:["Duration: ",Math.round((Date.now()-new Date(r.start_time).getTime())/6e4)," min"]}),(()=>{let t=10*Math.ceil(Math.round((Date.now()-new Date(r.start_time).getTime())/6e4)/10),s=t/60,o=s*e.price;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(b.A,{variant:"body2",component:"span",sx:{display:"block",color:"primary.contrastText",opacity:.7},children:["Billed: ",t," min (",s.toFixed(2)," hrs)"]}),(0,a.jsxs)(b.A,{variant:"body2",component:"span",sx:{display:"block",fontWeight:"bold",mt:.5},children:["Resource Total: $",o.toFixed(2)]})]})})()]}),(0,a.jsxs)(g.A,{sx:{mt:1,pt:+!!r,px:1,borderTop:r?"1px solid rgba(255,255,255,0.2)":"none"},children:[(0,a.jsxs)(g.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:r&&r.services.length>0?.5:0},children:[(0,a.jsx)(b.A,{variant:"subtitle2",sx:{color:"rgba(255,255,255,0.8)"},children:"Services:"}),(0,a.jsx)(y.A,{size:"small",variant:"text",onClick:()=>i(e.id),sx:{color:"primary.contrastText",textTransform:"none",p:0,minWidth:"auto","&:hover":{backgroundColor:"transparent",textDecoration:"underline",color:"secondary.light"}},children:"Add"})]}),r&&r.services.length>0?r.services.map((e,t)=>(0,a.jsxs)(g.A,{sx:{py:.5,px:0,display:"flex",justifyContent:"space-between"},children:[(0,a.jsxs)(b.A,{variant:"body2",sx:{color:"primary.contrastText"},children:[e.name," (x",e.quantity,")"]}),(0,a.jsx)(b.A,{variant:"body2",sx:{color:"primary.contrastText"},children:s(e.price*e.quantity)})]},`session-${r.id}-service-${e.id}-${t}`)):(0,a.jsx)(b.A,{variant:"caption",sx:{fontStyle:"italic",color:"rgba(255,255,255,0.6)",display:"block",pl:0},children:"No services added to this session."})]}),(0,a.jsxs)(g.A,{sx:{mt:2,pt:1,px:1,borderTop:"1px solid rgba(255,255,255,0.2)"},children:[(0,a.jsxs)(g.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:r&&r.products.length>0?.5:0},children:[(0,a.jsx)(b.A,{variant:"subtitle2",sx:{color:"rgba(255,255,255,0.8)"},children:"Products:"}),(0,a.jsx)(y.A,{size:"small",variant:"text",onClick:()=>l(e.id),sx:{color:"primary.contrastText",textTransform:"none",p:0,minWidth:"auto","&:hover":{backgroundColor:"transparent",textDecoration:"underline",color:"secondary.light"}},children:"Add"})]}),r&&r.products.length>0?r.products.map((e,t)=>(0,a.jsxs)(g.A,{sx:{py:.5,px:0,display:"flex",justifyContent:"space-between"},children:[(0,a.jsxs)(b.A,{variant:"body2",sx:{color:"primary.contrastText"},children:[e.name," (x",e.quantity,")"]}),(0,a.jsx)(b.A,{variant:"body2",sx:{color:"primary.contrastText"},children:s(e.price*e.quantity)})]},`session-${r.id}-product-${e.id}-${t}`)):(0,a.jsx)(b.A,{variant:"caption",sx:{fontStyle:"italic",color:"rgba(255,255,255,0.6)",display:"block",pl:0},children:"No products added to this session."})]})]})}));function eM(){let[e,t]=(0,s.useState)(!1),[r,o]=(0,s.useState)(null),[i,n]=(0,s.useState)("all"),[h,m]=(0,s.useState)(""),[v,R]=(0,s.useState)(!1),[W,F]=(0,s.useState)(!1),[D,L]=(0,s.useState)(!1),[_,E]=(0,s.useState)(!1),[B,G]=(0,s.useState)("cash"),[q,V]=(0,s.useState)(""),[Y,Z]=(0,s.useState)(1),[H,U]=(0,s.useState)(null),[Q,K]=(0,s.useState)(null),[J,ee]=(0,s.useState)(null),[et,er]=(0,s.useState)(!1),[ea,es]=(0,s.useState)(""),[eo,ei]=(0,s.useState)(""),{products:en,services:el,resources:ec,members:ed,cart:ep,addToCart:ex,removeFromCart:eu,clearCart:eh,cartTotal:em,selectedMember:eg,setSelectedMember:eb,updateResourceStatus:ev,startSession:ey,endSession:eA,getActiveSession:ef,addServiceToActiveSession:ej,addProductToActiveSession:eC,fetchActiveSessions:eS,fetchResources:eM,fetchProducts:eP,fetchServices:ez,authUser:eT}=(0,z.A)(),eR=e=>{ex({id:e.id,name:e.name,price:e.hourly_rate})},eN=()=>{R(!1)},eI=()=>{V(em().toFixed(2)),F(!0)},eW=(0,s.useCallback)(()=>L(!0),[]),eF=(0,s.useCallback)(()=>{L(!1),ee(null)},[]),eD=async e=>{if(null!==J){if(!ef(J))try{await ey(J)}catch(e){console.error(`[Session] Failed to create session for resource ${J}:`,e),eB("Session Error","Failed to create a new session. Please try again."),eF();return}await ej(J,{id:e.id,name:e.name,price:e.price})}else console.log("[Cart] Added service to general cart:",e.name);eF()},eL=(0,s.useCallback)(()=>E(!0),[]),eO=(0,s.useCallback)(()=>{E(!1),ee(null)},[]),e_=async e=>{if(null!==J){if(!ef(J))try{await ey(J)}catch(e){console.error(`[Session] Failed to create session for resource ${J}:`,e),eB("Session Error","Failed to create a new session. Please try again."),eO();return}await eC(J,{id:e.id,name:e.name,price:e.price})}else console.log("[Cart] Added product to general cart:",e.name);eO()},eE=e=>`$${e.toFixed(2)}`,eB=(0,s.useCallback)((e,t)=>{es(e),ei(t),er(!0)},[]),eG=(0,s.useCallback)(()=>{er(!1),es(""),ei("")},[]),eq=(0,s.useCallback)(e=>{eu(e),ev(e,"available")},[eu,ev]),eX=(0,s.useCallback)(e=>{ee(e),eW()},[ee,eW]),eV=(0,s.useCallback)(e=>{ee(e),eL()},[ee,eL]);return(0,a.jsxs)(g.A,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"calc(100vh - 64px)"},children:[(0,a.jsx)(g.A,{sx:{borderBottom:1,borderColor:"divider"},children:(0,a.jsxs)(A.A,{value:i,onChange:(e,t)=>{t&&(n(t),m(""))},"aria-label":"Resource Status Filter Tabs",sx:{bgcolor:"background.paper",color:"text.primary"},children:[(0,a.jsx)(f.A,{label:"All Resources",value:"all"}),(0,a.jsx)(f.A,{label:"Available",value:"available"}),(0,a.jsx)(f.A,{label:"In Use",value:"in-use"}),(0,a.jsx)(f.A,{label:"Booked",value:"booked"}),(0,a.jsx)(f.A,{label:"Maintenance",value:"maintenance"})]})}),(0,a.jsxs)(g.A,{sx:{flexGrow:1,display:"flex",overflow:"hidden"},children:[(0,a.jsx)(g.A,{sx:{width:"70%",height:"100%",overflow:"hidden",p:2,display:"flex",flexDirection:"column"},children:(0,a.jsxs)(g.A,{sx:{height:"100%",display:"flex",flexDirection:"column"},children:[(0,a.jsxs)(g.A,{sx:{mb:2,display:"flex",alignItems:"center",gap:2,flexWrap:"wrap"},children:[(0,a.jsx)(j.A,{placeholder:"Search Resources...",variant:"outlined",size:"small",value:h,onChange:e=>m(e.target.value),InputProps:{startAdornment:(0,a.jsx)(C.A,{position:"start",children:(0,a.jsx)(l.A,{})})},sx:{minWidth:"200px",flexGrow:1,maxWidth:"300px"}}),(0,a.jsxs)(w.A,{sx:{minWidth:120},size:"small",children:[(0,a.jsx)(S.A,{children:"Floor"}),(0,a.jsx)(k.A,{value:Y,label:"Floor",onChange:e=>Z(Number(e.target.value)),children:Array.from(new Set(ec.map(e=>e.floor))).sort((e,t)=>e-t).map(e=>(0,a.jsx)($.A,{value:e,children:`Floor ${e}`},e))})]}),(0,a.jsxs)(w.A,{sx:{minWidth:120},size:"small",children:[(0,a.jsx)(S.A,{children:"Zone"}),(0,a.jsxs)(k.A,{value:H||"all",label:"Zone",onChange:e=>U("all"===e.target.value?null:e.target.value),children:[(0,a.jsx)($.A,{value:"all",children:"All Zones"}),Array.from(new Set(ec.map(e=>e.zone).filter(Boolean))).sort().map(e=>(0,a.jsx)($.A,{value:e,children:e},e))]})]})]}),(0,a.jsx)(g.A,{sx:{flexGrow:1,overflowY:"auto",p:0,border:"1px solid",borderColor:"divider",borderRadius:1,position:"relative"},children:(0,a.jsx)(N,{resources:ec,selectedResource:Q,getActiveSession:ef,currentResourceStatusFilter:i,currentSearchQuery:h,currentSelectedFloor:Y,currentSelectedZone:H,onShowInfoAlert:eB,onResourceSelect:e=>{let t=ec.find(t=>t.id===e),r=ef(e),a=ep.some(t=>t.id===e);if(t){if(a)return;r||"available"===t.status?(K(e),eR(t)):console.log("[Resource] Resource not available:",{status:t.status})}},onBackgroundClick:()=>{K(null)}})})]})}),(0,a.jsxs)(g.A,{sx:{width:"30%",display:"flex",flexDirection:"column",borderLeft:1,borderColor:"divider"},children:[(0,a.jsxs)(M.A,{sx:{width:"100%",height:"100%",display:"flex",flexDirection:"column",bgcolor:"background.paper"},children:[(0,a.jsxs)(g.A,{sx:{p:2,borderBottom:1,borderColor:"divider"},children:[(0,a.jsx)(b.A,{variant:"h6",gutterBottom:!0,children:"Current Order"}),(0,a.jsx)(y.A,{variant:"outlined",startIcon:(0,a.jsx)(c.A,{}),onClick:()=>{R(!0)},fullWidth:!0,sx:{mb:1},children:eg?eg.name:"Select Member"})]}),(0,a.jsx)(g.A,{sx:{flex:1,overflow:"auto",p:2},children:0===ep.length?(0,a.jsxs)(g.A,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%"},children:[(0,a.jsx)(d.A,{sx:{fontSize:60,color:"text.disabled",mb:2}}),(0,a.jsx)(b.A,{variant:"body1",color:"text.secondary",children:"Cart is empty"}),(0,a.jsx)(b.A,{variant:"body2",color:"text.disabled",children:"Add products or services to get started"})]}):ep.map(e=>{let t=ec.find(t=>t.id===e.id),r=ef(e.id);return(0,a.jsx)(e$,{item:e,resourceDetails:t,session:r||null,formatCurrency:eE,onRemoveItem:eq,onAddServiceToSession:eX,onAddProductToSession:eV},`${e.id}`)})}),(0,a.jsxs)(g.A,{sx:{p:2,borderTop:1,borderColor:"divider"},children:[(0,a.jsxs)(g.A,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,a.jsx)(b.A,{variant:"h6",children:"Total:"}),(0,a.jsx)(T,{})]}),(0,a.jsxs)(P.A,{direction:"row",spacing:1,sx:{mb:2},children:[(0,a.jsx)(y.A,{variant:"outlined",color:"error",startIcon:(0,a.jsx)(p.A,{}),onClick:()=>{let e=ep.filter(e=>ef(e.id));e.length>0?(eh(),e.forEach(e=>{ex({id:e.id,name:e.name,price:e.price})})):(ep.forEach(e=>{ev(e.id,"available")}),eh())},disabled:0===ep.length,fullWidth:!0,children:"Clear"}),ep.length>0?(0,a.jsx)(y.A,{variant:"contained",color:"primary",onClick:()=>{ep.some(e=>ef(e.id))?(ep.forEach(e=>{let t=ef(e.id);t&&eA(t.id)}),eI()):(ep.forEach(e=>{ey(e.id),ev(e.id,"in-use")}),eh())},startIcon:ep.some(e=>ef(e.id))?(0,a.jsx)(x,{}):(0,a.jsx)(u,{}),fullWidth:!0,children:ep.some(e=>ef(e.id))?"End Session & Pay":"Start Sessions"}):(0,a.jsx)(y.A,{variant:"contained",startIcon:(0,a.jsx)(x,{}),onClick:eI,disabled:0===ep.length,fullWidth:!0,children:"Pay"})]})]})]}),(0,a.jsx)(X,{open:v,onClose:eN,members:ed,onSelectMember:e=>{eb(e),eN()},currentMemberId:eg?eg.id:null,onSelectWalkIn:()=>{eb(null),eN()}}),(0,a.jsx)(ew,{open:D,onClose:eF,services:el,onSelectService:eD}),(0,a.jsx)(ek,{open:_,onClose:eO,products:en,onSelectProduct:e_}),(0,a.jsx)(O,{open:W,onClose:()=>{F(!1)},cart:ep,cartTotal:em,getActiveSession:ef,paymentMethod:B,setPaymentMethod:G,paymentAmount:q,setPaymentAmount:V,onCompleteTransaction:()=>{let{cart:e,activeSessions:t,addTransaction:r,clearCart:a,selectedMember:s}=z.A.getState(),o=new Date().toISOString(),i=[],n=0;e.forEach(e=>{let r=t.find(t=>t.resource_id===e.id);if(r){let t=new Date(r.start_time).getTime(),a=Math.max(0,(new Date(o).getTime()-t)/6e4),s=(a>0?10*Math.ceil(a/10):0)/60*e.price;n+=s;let l=0;r.products.forEach(e=>{l+=e.price*e.quantity}),r.services.forEach(e=>{l+=e.price*e.quantity}),n+=l;let c={...r,end_time:o,status:"completed",products:r.products.map(e=>({...e})),services:r.services.map(e=>({...e}))};i.push(c)}}),i.length>0?r({memberId:s?s.id:null,memberName:s?s.name:"Walk-in",sessions:i,totalAmount:n,paymentMethod:B,status:"completed",createdAt:o}):console.log("[Transaction] No active resource sessions found in cart to complete a resource-based transaction."),a(),F(!1)}}),(0,a.jsx)(I.A,{open:e,title:"End Session",message:"Are you sure you want to end this session? This will finalize the resource usage and proceed to payment.",onConfirm:()=>{r&&(eA(r.id),o(null),eI()),t(!1)},onCancel:()=>{t(!1),o(null)}}),(0,a.jsx)(I.A,{open:et,title:ea,message:eo,onConfirm:eG,onCancel:eG,infoMode:!0,confirmText:"OK"})]})]})]})}e$.displayName="ResourceCartItem"},33873:e=>{"use strict";e.exports=require("path")},56676:(e,t,r)=>{"use strict";r.d(t,{A:()=>o}),r(43210);var a=r(23428),s=r(60687);let o=(0,a.A)((0,s.jsx)("path",{d:"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"}),"LastPage")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},84431:(e,t,r)=>{Promise.resolve().then(r.bind(r,7937))},92679:(e,t,r)=>{Promise.resolve().then(r.bind(r,33074))},97990:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>c});var a=r(65239),s=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["pos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7937)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\pos\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\yunsell\\evospace\\evospace-pos\\src\\app\\pos\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/pos/page",pathname:"/pos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,991,619,111,117,575,154,790,79,98],()=>r(97990));module.exports=a})();