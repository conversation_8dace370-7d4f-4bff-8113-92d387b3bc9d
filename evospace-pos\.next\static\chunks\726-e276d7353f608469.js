"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[726],{35726:(e,t,n)=>{n.d(t,{A:()=>B});var r=n(12115),o=n(17472),a=n(10704),i=n(29905),l=n(40428);let s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoHideDuration:t=null,disableWindowBlurListener:n=!1,onClose:o,open:s,resumeHideDuration:c}=e,u=(0,i.A)();r.useEffect(()=>{if(s)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){e.defaultPrevented||"Escape"!==e.key||null==o||o(e,"escapeKeyDown")}},[s,o]);let p=(0,a.A)((e,t)=>{null==o||o(e,t)}),d=(0,a.A)(e=>{o&&null!=e&&u.start(e,()=>{p(null,"timeout")})});r.useEffect(()=>(s&&d(t),u.clear),[s,t,d,u]);let f=u.clear,m=r.useCallback(()=>{null!=t&&d(null!=c?c:.5*t)},[t,c,d]),g=e=>t=>{let n=e.onBlur;null==n||n(t),m()},h=e=>t=>{let n=e.onFocus;null==n||n(t),f()},v=e=>t=>{let n=e.onMouseEnter;null==n||n(t),f()},A=e=>t=>{let n=e.onMouseLeave;null==n||n(t),m()};return r.useEffect(()=>{if(!n&&s)return window.addEventListener("focus",m),window.addEventListener("blur",f),()=>{window.removeEventListener("focus",m),window.removeEventListener("blur",f)}},[n,s,m,f]),{getRootProps:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n={...(0,l.A)(e),...(0,l.A)(t)};return{role:"presentation",...t,...n,onBlur:g(n),onFocus:h(n),onMouseEnter:v(n),onMouseLeave:A(n)}},onClickAway:e=>{null==o||o(e,"clickaway")}}};var c=n(14810),u=n(81616),p=n(45292);function d(e){return e.substring(2).toLowerCase()}function f(e){let{children:t,disableReactTree:n=!1,mouseEvent:o="onClick",onClickAway:i,touchEvent:l="onTouchEnd"}=e,s=r.useRef(!1),f=r.useRef(null),m=r.useRef(!1),g=r.useRef(!1);r.useEffect(()=>(setTimeout(()=>{m.current=!0},0),()=>{m.current=!1}),[]);let h=(0,u.A)((0,p.A)(t),f),v=(0,a.A)(e=>{let t,r=g.current;g.current=!1;let o=(0,c.A)(f.current);if(!(!m.current||!f.current||"clientX"in e&&(o.documentElement.clientWidth<e.clientX||o.documentElement.clientHeight<e.clientY))){if(s.current){s.current=!1;return}(e.composedPath?e.composedPath().includes(f.current):!o.documentElement.contains(e.target)||f.current.contains(e.target))||!n&&r||i(e)}}),A=e=>n=>{g.current=!0;let r=t.props[e];r&&r(n)},y={ref:h};return!1!==l&&(y[l]=A(l)),r.useEffect(()=>{if(!1!==l){let e=d(l),t=(0,c.A)(f.current),n=()=>{s.current=!0};return t.addEventListener(e,v),t.addEventListener("touchmove",n),()=>{t.removeEventListener(e,v),t.removeEventListener("touchmove",n)}}},[v,l]),!1!==o&&(y[o]=A(o)),r.useEffect(()=>{if(!1!==o){let e=d(o),t=(0,c.A)(f.current);return t.addEventListener(e,v),()=>{t.removeEventListener(e,v)}}},[v,o]),r.cloneElement(t,y)}var m=n(75955),g=n(16324),h=n(40680),v=n(10186),A=n(13209),y=n(18560),k=n(52596),b=n(14391),w=n(18407),E=n(55170),C=n(90870);function x(e){return(0,C.Ay)("MuiSnackbarContent",e)}(0,E.A)("MuiSnackbarContent",["root","message","action"]);var L=n(95155);let S=e=>{let{classes:t}=e;return(0,o.A)({root:["root"],action:["action"],message:["message"]},x,t)},O=(0,m.Ay)(w.A,{name:"MuiSnackbarContent",slot:"Root"})((0,h.A)(e=>{let{theme:t}=e,n="light"===t.palette.mode?.8:.98,r=(0,b.tL)(t.palette.background.default,n);return{...t.typography.body2,color:t.vars?t.vars.palette.SnackbarContent.color:t.palette.getContrastText(r),backgroundColor:t.vars?t.vars.palette.SnackbarContent.bg:r,display:"flex",alignItems:"center",flexWrap:"wrap",padding:"6px 16px",borderRadius:(t.vars||t).shape.borderRadius,flexGrow:1,[t.breakpoints.up("sm")]:{flexGrow:"initial",minWidth:288}}})),M=(0,m.Ay)("div",{name:"MuiSnackbarContent",slot:"Message"})({padding:"8px 0"}),P=(0,m.Ay)("div",{name:"MuiSnackbarContent",slot:"Action"})({display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:16,marginRight:-8}),R=r.forwardRef(function(e,t){let n=(0,v.b)({props:e,name:"MuiSnackbarContent"}),{action:r,className:o,message:a,role:i="alert",...l}=n,s=S(n);return(0,L.jsxs)(O,{role:i,square:!0,elevation:6,className:(0,k.A)(s.root,o),ownerState:n,ref:t,...l,children:[(0,L.jsx)(M,{className:s.message,ownerState:n,children:a}),r?(0,L.jsx)(P,{className:s.action,ownerState:n,children:r}):null]})});function T(e){return(0,C.Ay)("MuiSnackbar",e)}(0,E.A)("MuiSnackbar",["root","anchorOriginTopCenter","anchorOriginBottomCenter","anchorOriginTopRight","anchorOriginBottomRight","anchorOriginTopLeft","anchorOriginBottomLeft"]);var j=n(47798);let z=e=>{let{classes:t,anchorOrigin:n}=e,r={root:["root","anchorOrigin".concat((0,A.A)(n.vertical)).concat((0,A.A)(n.horizontal))]};return(0,o.A)(r,T,t)},N=(0,m.Ay)("div",{name:"MuiSnackbar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t["anchorOrigin".concat((0,A.A)(n.anchorOrigin.vertical)).concat((0,A.A)(n.anchorOrigin.horizontal))]]}})((0,h.A)(e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.snackbar,position:"fixed",display:"flex",left:8,right:8,justifyContent:"center",alignItems:"center",variants:[{props:e=>{let{ownerState:t}=e;return"top"===t.anchorOrigin.vertical},style:{top:8,[t.breakpoints.up("sm")]:{top:24}}},{props:e=>{let{ownerState:t}=e;return"top"!==t.anchorOrigin.vertical},style:{bottom:8,[t.breakpoints.up("sm")]:{bottom:24}}},{props:e=>{let{ownerState:t}=e;return"left"===t.anchorOrigin.horizontal},style:{justifyContent:"flex-start",[t.breakpoints.up("sm")]:{left:24,right:"auto"}}},{props:e=>{let{ownerState:t}=e;return"right"===t.anchorOrigin.horizontal},style:{justifyContent:"flex-end",[t.breakpoints.up("sm")]:{right:24,left:"auto"}}},{props:e=>{let{ownerState:t}=e;return"center"===t.anchorOrigin.horizontal},style:{[t.breakpoints.up("sm")]:{left:"50%",right:"auto",transform:"translateX(-50%)"}}}]}})),B=r.forwardRef(function(e,t){let n=(0,v.b)({props:e,name:"MuiSnackbar"}),o=(0,g.A)(),a={enter:o.transitions.duration.enteringScreen,exit:o.transitions.duration.leavingScreen},{action:i,anchorOrigin:{vertical:l,horizontal:c}={vertical:"bottom",horizontal:"left"},autoHideDuration:u=null,children:p,className:d,ClickAwayListenerProps:m,ContentProps:h,disableWindowBlurListener:A=!1,message:k,onBlur:b,onClose:w,onFocus:E,onMouseEnter:C,onMouseLeave:x,open:S,resumeHideDuration:O,slots:M={},slotProps:P={},TransitionComponent:T,transitionDuration:B=a,TransitionProps:{onEnter:I,onExited:F,..._}={},...W}=n,X={...n,anchorOrigin:{vertical:l,horizontal:c},autoHideDuration:u,disableWindowBlurListener:A,TransitionComponent:T,transitionDuration:B},G=z(X),{getRootProps:q,onClickAway:D}=s({...X}),[H,K]=r.useState(!0),Y=e=>{K(!0),F&&F(e)},J=(e,t)=>{K(!1),I&&I(e,t)},Q={slots:{transition:T,...M},slotProps:{content:h,clickAwayListener:m,transition:_,...P}},[U,V]=(0,j.A)("root",{ref:t,className:[G.root,d],elementType:N,getSlotProps:q,externalForwardedProps:{...Q,...W},ownerState:X}),[Z,{ownerState:$,...ee}]=(0,j.A)("clickAwayListener",{elementType:f,externalForwardedProps:Q,getSlotProps:e=>({onClickAway:function(){for(var t,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];let a=r[0];null==(t=e.onClickAway)||t.call(e,...r),null!=a&&a.defaultMuiPrevented||D(...r)}}),ownerState:X}),[et,en]=(0,j.A)("content",{elementType:R,shouldForwardComponentProp:!0,externalForwardedProps:Q,additionalProps:{message:k,action:i},ownerState:X}),[er,eo]=(0,j.A)("transition",{elementType:y.A,externalForwardedProps:Q,getSlotProps:e=>({onEnter:function(){for(var t,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];null==(t=e.onEnter)||t.call(e,...r),J(...r)},onExited:function(){for(var t,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];null==(t=e.onExited)||t.call(e,...r),Y(...r)}}),additionalProps:{appear:!0,in:S,timeout:B,direction:"top"===l?"down":"up"},ownerState:X});return!S&&H?null:(0,L.jsx)(Z,{...ee,...M.clickAwayListener&&{ownerState:$},children:(0,L.jsx)(U,{...V,children:(0,L.jsx)(er,{...eo,children:p||(0,L.jsx)(et,{...en})})})})})}}]);