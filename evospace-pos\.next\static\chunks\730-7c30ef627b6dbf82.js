"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[730],{2730:(e,t,s)=>{s.d(t,{A:()=>d});var r=s(65453),a=s(85055),o=s(88242);let c=async(e,t,s)=>{try{let r=await e();if(r.success&&r.data.items&&r.data.items.length>0)return r.data.items;return console.warn("API returned no ".concat(s,", using mock data")),t}catch(e){return console.error("Error fetching ".concat(s,":"),e),t}},n=async(e,t,s,r,a,o)=>{try{let r=await t();if(r.success&&r.data.transaction)return o(t=>({transactions:t.transactions.map(t=>t.id===e?r.data.transaction:t)})),r.data.transaction;return o(t=>({transactions:t.transactions.map(t=>t.id===e?{...t,status:s}:t)})),a.transactions.find(t=>t.id===e)}catch(t){return console.error("Error ".concat(r," transaction ").concat(e,":"),t),o(t=>({transactions:t.transactions.map(t=>t.id===e?{...t,status:s}:t)})),a.transactions.find(t=>t.id===e)}},i=async(e,t,s,r,a,o)=>{let c=r(e);if(!c)return void console.error("No active session found for resource ".concat(e));let n="product"===s?{product_id:t.id,quantity:1,price:t.price}:{service_id:t.id,unit:"hour",quantity:1,price:t.price};try{await a(c.id,n),o(e=>({activeSessions:e.activeSessions.map(e=>{if(e.id===c.id){let r,a="product"===s?e.products:e.services;return r=a.find(e=>e.id===t.id)?a.map(e=>e.id===t.id?{...e,quantity:e.quantity+1}:e):[...a,{id:t.id,type:s,name:t.name,price:t.price,quantity:1,["".concat(s,"_id")]:t.id}],"product"===s?{...e,products:r}:{...e,services:r}}return e})}))}catch(e){console.error("Error adding ".concat(s," ").concat(t.id," to session ").concat(c.id,":"),e),o(e=>({activeSessions:e.activeSessions.map(e=>{if(e.id===c.id){let r,a="product"===s?e.products:e.services;return r=a.find(e=>e.id===t.id)?a.map(e=>e.id===t.id?{...e,quantity:e.quantity+1}:e):[...a,{id:t.id,type:s,name:t.name,price:t.price,quantity:1,["".concat(s,"_id")]:t.id}],"product"===s?{...e,products:r}:{...e,services:r}}return e})}))}},d=(0,r.v)((e,t)=>({authUser:{username:null,token:null,store_id:null,store_name:null},stores:[],fetchStores:async()=>{try{let t=await o.Kp.auth.getStores();t.success&&t.data&&t.data.items&&e({stores:t.data.items})}catch(t){console.error("Error fetching stores:",t),e({stores:[{id:1,name:"Main Store",address:"123 Main St",phone:"555-0100"},{id:2,name:"Branch Store",address:"456 Oak Ave",phone:"555-0200"}]})}},setUser:t=>e({authUser:t}),loadAuthToken:()=>{{let t=localStorage.getItem("authToken"),s=localStorage.getItem("storeId"),r=localStorage.getItem("storeName");t&&e(e=>({authUser:{...e.authUser,token:t,store_id:s?parseInt(s):null,store_name:r||null}}))}},currentUser:a.VV[0],isAuthenticated:!1,login:async(t,s,r)=>{try{let a=await o.Kp.auth.login({username:t,password:s,store_id:r});if(a.success&&a.data){let{user:t,token:s}=a.data;return e({currentUser:t,isAuthenticated:!0,authUser:{username:t.username,token:s,store_id:t.store_id||r,store_name:t.store_name||null}}),localStorage.setItem("authToken",s),localStorage.setItem("storeId",String(t.store_id||r)),localStorage.setItem("storeName",t.store_name||""),!0}return!1}catch(e){return console.error("Login error:",e),!1}},logout:()=>{localStorage.removeItem("authToken"),localStorage.removeItem("storeId"),localStorage.removeItem("storeName"),e({currentUser:a.VV[0],isAuthenticated:!1,authUser:{username:null,token:null,store_id:null,store_name:null}})},products:[],getProduct:e=>t().products.find(t=>t.id===e),fetchProducts:async()=>{e({products:await c(()=>o.Kp.products.getProducts(),a.ZE,"products")})},addProduct:async t=>{try{let s=await o.Kp.products.createProduct(t);s.success&&s.data.product&&e(e=>({products:[...e.products,s.data.product]}))}catch(e){throw console.error("Error adding product:",e),e}},updateProduct:async(t,s)=>{try{let r=await o.Kp.products.updateProduct(t,s);r.success&&r.data.product&&e(e=>({products:e.products.map(e=>e.id===t?r.data.product:e)}))}catch(e){throw console.error("Error updating product ".concat(t,":"),e),e}},deleteProduct:async t=>{try{(await o.Kp.products.deleteProduct(t)).success&&e(e=>({products:e.products.filter(e=>e.id!==t)}))}catch(e){throw console.error("Error deleting product ".concat(t,":"),e),e}},categories:a.LZ,getCategory:e=>t().categories.find(t=>t.id===e),services:[],getService:e=>t().services.find(t=>t.id===e),fetchServices:async()=>{e({services:await c(()=>o.Kp.services.getServices(),a.$p,"services")})},addService:async t=>{try{let s=await o.Kp.services.createService(t);s.success&&s.data.service&&e(e=>({services:[...e.services,s.data.service]}))}catch(e){throw console.error("Error adding service:",e),e}},updateService:async(t,s)=>{try{let r=await o.Kp.services.updateService(t,s);r.success&&r.data.service&&e(e=>({services:e.services.map(e=>e.id===t?r.data.service:e)}))}catch(e){throw console.error("Error updating service ".concat(t,":"),e),e}},deleteService:async t=>{try{(await o.Kp.services.deleteService(t)).success&&e(e=>({services:e.services.filter(e=>e.id!==t)}))}catch(e){throw console.error("Error deleting service ".concat(t,":"),e),e}},resources:a.ES,getResource:e=>t().resources.find(t=>t.id===e),fetchResources:async()=>{e({resources:await c(()=>o.Kp.resources.getResources(),a.ES,"resources")})},addResource:async t=>{try{let s=await o.Kp.resources.createResource(t);s.success&&s.data.resource&&e(e=>({resources:[...e.resources,s.data.resource]}))}catch(e){throw console.error("Error adding resource:",e),e}},updateResource:async(t,s)=>{try{let r=await o.Kp.resources.updateResource(t,s);r.success&&r.data.resource&&e(e=>({resources:e.resources.map(e=>e.id===t?r.data.resource:e)}))}catch(e){throw console.error("Error updating resource ".concat(t,":"),e),e}},updateResourceStatus:async(t,s)=>{try{let r=await o.Kp.resources.updateResource(t,{status:s});r.success&&r.data.resource&&e(e=>({resources:e.resources.map(e=>e.id===t?r.data.resource:e)}))}catch(e){throw console.error("Error updating resource status ".concat(t,":"),e),e}},setResources:t=>e({resources:t}),deleteResource:async t=>{try{(await o.Kp.resources.deleteResource(t)).success&&e(e=>({resources:e.resources.filter(e=>e.id!==t)}))}catch(e){throw console.error("Error deleting resource ".concat(t,":"),e),e}},members:[],getMember:e=>t().members.find(t=>t.id===e),addMember:async t=>{try{let s=await o.Kp.members.createMember(t);s.success&&s.data.member&&e(e=>({members:[...e.members,s.data.member]}))}catch(e){throw console.error("Error adding member:",e),e}},updateMember:async(t,s)=>{try{let r=await o.Kp.members.updateMember(t,s);r.success&&r.data.member&&e(e=>({members:e.members.map(e=>e.id===t?r.data.member:e)}))}catch(e){throw console.error("Error updating member ".concat(t,":"),e),e}},deleteMember:async t=>{try{(await o.Kp.members.deleteMember(t)).success&&e(e=>({members:e.members.filter(e=>e.id!==t)}))}catch(e){throw console.error("Error deleting member ".concat(t,":"),e),e}},fetchMembers:async()=>{let{members:t}=await Promise.resolve().then(s.bind(s,85055));e({members:await c(()=>o.Kp.members.getMembers(),t,"members")})},transactions:[],fetchTransactions:async()=>{try{let t=await o.Kp.transactions.getTransactions();t.success&&t.data.items?e({transactions:t.data.items}):console.warn("API returned no transactions")}catch(e){console.error("Error fetching transactions:",e)}},addTransaction:async s=>{try{let r={member_id:s.memberId||null,subtotal:s.subtotal||s.totalAmount,tax_amount:s.tax_amount||0,tax_rate:s.tax_rate||0,discount_amount:s.discount_amount||0,discount_rate:s.discount_rate||0,total_amount:s.totalAmount,actual_amount:s.actual_amount||s.totalAmount,payment_method:s.paymentMethod,payment_reference:s.payment_reference||"",notes:s.notes||"",sessions:s.sessions||[]},a=await o.Kp.transactions.createTransaction(r);if(a.success&&a.data.transaction)return e(e=>({transactions:[...e.transactions,a.data.transaction]})),t().clearCart(),a.data.transaction;{let r={id:Math.max(0,...t().transactions.map(e=>e.id))+1,...s,createdAt:new Date().toISOString(),status:"pending"};return e(e=>({transactions:[...e.transactions,r]})),t().clearCart(),r}}catch(a){console.error("Error adding transaction:",a);let r={id:Math.max(0,...t().transactions.map(e=>e.id))+1,...s,createdAt:new Date().toISOString(),status:"pending"};return e(e=>({transactions:[...e.transactions,r]})),t().clearCart(),r}},completeTransaction:async s=>n(s,()=>o.Kp.transactions.completeTransaction(s),"completed","completing",t(),e),cancelTransaction:async function(s){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Cancelled by user";return n(s,()=>o.Kp.transactions.cancelTransaction(s,r),"cancelled","cancelling",t(),e)},cart:[],addToCart:s=>{let{cart:r}=t();r.find(e=>e.id===s.id)||e(e=>({cart:[...e.cart,{...s}]}))},removeFromCart:t=>{e(e=>({cart:e.cart.filter(e=>e.id!==t)}))},clearCart:()=>e({cart:[]}),cartTotal:()=>{let{cart:e,getActiveSession:s}=t();return e.reduce((e,t)=>{let r=s(t.id);if(r){let s=10*Math.ceil((Date.now()-new Date(r.start_time).getTime())/6e4/10)/60*t.price;return e+s+r.products.reduce((e,t)=>e+t.price*t.quantity,0)+r.services.reduce((e,t)=>e+t.price*t.quantity,0)}return e},0)},selectedMember:null,setSelectedMember:t=>e({selectedMember:t}),activeSessions:[],fetchActiveSessions:async()=>{try{let t=await o.Kp.sessions.getSessions();if(t.success&&t.data.items){let s=t.data.items.filter(e=>"active"===e.status||"open"===e.status).map(e=>{let t=Array.isArray(e.products)?e.products.map(e=>({id:e.id,type:"product",name:e.name,price:e.price,quantity:e.quantity})):[],s=Array.isArray(e.services)?e.services.map(e=>({id:e.id,type:"service",name:e.name,price:e.price,quantity:e.quantity})):[];return{id:e.id,resource_id:e.resource_id||0,user_id:e.user_id,member_id:e.member_id,start_time:e.start_time||new Date().toISOString(),end_time:void 0,status:e.status,products:t,services:s,notes:e.notes}});e({activeSessions:s})}else console.warn("API returned no active sessions")}catch(e){console.error("Error fetching active sessions:",e)}},startSession:async(s,r,a)=>{try{let c=await o.Kp.sessions.createSession({resource_id:s,member_id:r,notes:a||"Session for resource ".concat(s)});if(c.success&&c.data.session){let o=c.data.session.id,n={id:o,resource_id:s,user_id:c.data.session.user_id,member_id:r,start_time:c.data.session.start_time||new Date().toISOString(),status:"open",products:[],services:[],notes:a};return await t().updateResourceStatus(s,"in-use"),e(e=>({activeSessions:[...e.activeSessions,n]})),o}{console.warn("API session creation failed, using local session");let o=Date.now(),c={id:o,resource_id:s,member_id:r,start_time:new Date().toISOString(),status:"active",products:[],services:[],notes:a};return await t().updateResourceStatus(s,"in-use"),e(e=>({activeSessions:[...e.activeSessions,c]})),o}}catch(n){console.error("Error starting session:",n);let o=Date.now(),c={id:o,resource_id:s,member_id:r,start_time:new Date().toISOString(),status:"active",products:[],services:[],notes:a};try{await t().updateResourceStatus(s,"in-use")}catch(e){console.error("Error updating resource status:",e)}return e(e=>({activeSessions:[...e.activeSessions,c]})),o}},endSession:async(s,r)=>{try{let a=t().activeSessions.find(e=>e.id===s);if(!a)return void console.error("Session with ID ".concat(s," not found"));let c=await o.Kp.sessions.closeSession(s,{status:"closed",notes:r||"Session for resource ".concat(a.resource_id," ended")});c.success&&c.data.session?e(e=>({activeSessions:e.activeSessions.filter(e=>e.id!==s)})):e(e=>({activeSessions:e.activeSessions.filter(e=>e.id!==s)})),await t().updateResourceStatus(a.resource_id,"available")}catch(r){console.error("Error ending session ".concat(s,":"),r),e(e=>({activeSessions:e.activeSessions.filter(e=>e.id!==s)}));try{let e=t().activeSessions.find(e=>e.id===s);e&&await t().updateResourceStatus(e.resource_id,"available")}catch(e){console.error("Error updating resource status:",e)}}},getActiveSession:e=>t().activeSessions.find(t=>t.resource_id===e&&("active"===t.status||"open"===t.status)),addServiceToActiveSession:async(s,r)=>i(s,r,"service",t().getActiveSession,(e,t)=>o.Kp.sessions.addServiceToSession(e,{service_id:t.service_id,unit:t.unit,quantity:t.quantity,price:t.price}),e),addProductToActiveSession:async(s,r)=>i(s,r,"product",t().getActiveSession,(e,t)=>o.Kp.sessions.addProductToSession(e,{product_id:t.product_id,quantity:t.quantity,price:t.price}),e)}))},85055:(e,t,s)=>{s.d(t,{$p:()=>c,ES:()=>n,LZ:()=>o,VV:()=>r,ZE:()=>a,members:()=>i});let r=[{id:1,username:"admin",name:"Administrator",email:"<EMAIL>",role:"admin"},{id:2,username:"staff",name:"Staff User",email:"<EMAIL>",role:"staff"}],a=[{id:1,name:"Coffee",category:"Beverages",price:2.5,stock:100,image:""},{id:2,name:"Tea",category:"Beverages",price:1.8,stock:120,image:""},{id:3,name:"Sandwich",category:"Food",price:4.5,stock:30,image:""},{id:4,name:"Croissant",category:"Bakery",price:2.2,stock:45,image:""},{id:5,name:"Salad",category:"Food",price:5.5,stock:25,image:""},{id:6,name:"Cake Slice",category:"Desserts",price:3.8,stock:20,image:""},{id:7,name:"Water Bottle",category:"Beverages",price:1,stock:150,image:""},{id:8,name:"Muffin",category:"Bakery",price:2,stock:40,image:""}],o=[{id:1,name:"Beverages"},{id:2,name:"Food"},{id:3,name:"Bakery"},{id:4,name:"Desserts"}],c=[{id:1,name:"Room Rental",price:25,duration:60,description:"Rent a meeting room for 1 hour",category:"Workspace"},{id:2,name:"Workspace",price:15,duration:60,description:"Access to workspace for 1 hour",category:"Workspace"},{id:3,name:"Printing",price:.1,unit:"per page",description:"Black and white printing service",category:"Office Services"},{id:4,name:"Color Printing",price:.25,unit:"per page",description:"Color printing service",category:"Office Services"},{id:5,name:"Coffee Service",price:10,description:"Coffee service for meetings (serves 5)",category:"Catering"}],n=[{id:1,name:"Conference Room A",type:"room",capacity:12,hourly_rate:50,status:"available",x:0,y:0,width:3,height:2,floor:1,zone:"North Wing"},{id:2,name:"Office Desk 1",type:"desk",capacity:1,hourly_rate:10,status:"booked",x:3,y:0,width:1,height:1,floor:1,zone:"North Wing"},{id:3,name:"Meeting Room B",type:"room",capacity:8,hourly_rate:35,status:"maintenance",x:0,y:3,width:2,height:2,floor:1,zone:"South Wing"},{id:4,name:"Office Desk 2",type:"desk",capacity:1,hourly_rate:10,status:"available",x:3,y:1,width:1,height:1,floor:1,zone:"North Wing"},{id:5,name:"Projector",type:"equipment",capacity:0,hourly_rate:15,status:"available",x:5,y:5,width:1,height:1,floor:1,zone:"Storage"},{id:6,name:"Conference Room C",type:"room",capacity:20,hourly_rate:75,status:"available",x:6,y:0,width:3,height:3,floor:2,zone:"Executive Wing"},{id:7,name:"Standing Desk 1",type:"desk",capacity:1,hourly_rate:12,status:"available",x:1,y:0,width:1,height:1,floor:2,zone:"Open Space"},{id:8,name:"Meeting Pod",type:"room",capacity:4,hourly_rate:25,status:"booked",x:2,y:0,width:2,height:2,floor:2,zone:"Open Space"},{id:9,name:"Laptop",type:"equipment",capacity:0,hourly_rate:20,status:"available",x:5,y:6,width:1,height:1,floor:1,zone:"Storage"},{id:10,name:"Focus Room",type:"room",capacity:1,hourly_rate:15,status:"available",x:9,y:0,width:1,height:1,floor:1,zone:"Quiet Zone"}],i=[{id:1,name:"John Doe",email:"<EMAIL>",phone:"555-1234",visits:10,totalSpent:250.5},{id:2,name:"Jane Smith",email:"<EMAIL>",phone:"555-5678",visits:5,totalSpent:120.75},{id:3,name:"Bob Johnson",email:"<EMAIL>",phone:"555-9012",visits:8,totalSpent:180.25},{id:4,name:"Alice Brown",email:"<EMAIL>",phone:"555-3456",visits:12,totalSpent:320},{id:5,name:"Charlie Wilson",email:"<EMAIL>",phone:"555-7890",visits:3,totalSpent:75.5}];new Date(Date.now()-36e5).toISOString(),new Date().toISOString(),new Date().toISOString(),new Date(Date.now()-72e5).toISOString(),new Date(Date.now()-36e5).toISOString(),new Date().toISOString()},88242:(e,t,s)=>{s.d(t,{Kp:()=>i,Ay:()=>d,SL:()=>n});let r=s(49509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:5000/api";class a extends Error{constructor(e,t,s){super(e),this.name="ApiError",this.status=t,this.errors=s}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:s="GET",token:o=null,body:c=null,params:n={}}=t,i={Accept:"application/json"};if(c&&(i["Content-Type"]="application/json"),o)i.Authorization="Bearer ".concat(o);else if(1){let e=localStorage.getItem("authToken");e&&(i.Authorization="Bearer ".concat(e))}let d=new URLSearchParams;Object.entries(n).forEach(e=>{let[t,s]=e;void 0!==s&&d.append(t,String(s))});let u=d.toString(),m="".concat(r).concat(e).concat(u?"?".concat(u):""),l=await fetch(m,{method:s,headers:i,body:c?JSON.stringify(c):void 0});if(!l.ok){let e;401===l.status&&(console.error("Unauthorized (401). Redirecting to login."),localStorage.removeItem("authToken"),window.location.href="/auth/login");let t="API Error (".concat(l.status,"): ");try{let s=await l.json();t+=s.message||l.statusText||"Unknown error",e=s.errors}catch(e){t+=l.statusText||"Unknown error (failed to parse error response)"}throw console.error("".concat(s," ").concat(m," failed:"),t),new a(t,l.status,e)}if(204===l.status)return;let S=await l.json();if("success"in S&&!S.success){let e=S.message||"API request failed";throw console.error("".concat(s," ").concat(m," failed:"),e),new a(e,l.status,S.errors)}return S}let c={AUTH:{LOGIN:"/auth/login",STORES:"/auth/stores"},MEMBERS:{BASE:"/members",DETAIL:e=>"/members/".concat(e)},PRODUCTS:{BASE:"/products",DETAIL:e=>"/products/".concat(e)},SERVICES:{BASE:"/services",DETAIL:e=>"/services/".concat(e)},RESOURCES:{BASE:"/resources",DETAIL:e=>"/resources/".concat(e)},TRANSACTIONS:{BASE:"/transactions",DETAIL:e=>"/transactions/".concat(e),ITEMS:e=>"/transactions/".concat(e,"/items"),ITEM_DETAIL:(e,t)=>"/transactions/".concat(e,"/items/").concat(t),COMPLETE:e=>"/transactions/".concat(e,"/complete"),REFUND:e=>"/transactions/".concat(e,"/refund"),CANCEL:e=>"/transactions/".concat(e,"/cancel")},SESSIONS:{BASE:"/sessions",DETAIL:e=>"/sessions/".concat(e),CLOSE:e=>"/sessions/".concat(e,"/close"),SERVICES:e=>"/sessions/".concat(e,"/services"),PRODUCTS:e=>"/sessions/".concat(e,"/products")},REPORTS:{SALES_SUMMARY:"/reports/sales_summary",SALES_BY_CATEGORY:"/reports/sales_by_category",PRODUCT_PERFORMANCE:"/reports/product_performance",RESOURCE_UTILIZATION:"/reports/resource_utilization",DASHBOARD_SUMMARY:"/reports/dashboard_summary",SALES_BY_PAYMENT_METHOD:"/reports/sales_by_payment_method",MEMBER_ACTIVITY:"/reports/member_activity"}},n={getSalesSummary:async e=>o(c.REPORTS.SALES_SUMMARY,{params:e}),getSalesByCategory:async e=>o(c.REPORTS.SALES_BY_CATEGORY,{params:e}),getProductPerformance:async e=>o(c.REPORTS.PRODUCT_PERFORMANCE,{params:e}),getResourceUtilization:async e=>o(c.REPORTS.RESOURCE_UTILIZATION,{params:e}),getDashboardSummary:async e=>o(c.REPORTS.DASHBOARD_SUMMARY,{params:e}),getSalesByPaymentMethod:async e=>o(c.REPORTS.SALES_BY_PAYMENT_METHOD,{params:e}),getMemberActivity:async e=>o(c.REPORTS.MEMBER_ACTIVITY,{params:e})},i={auth:{login:async e=>o(c.AUTH.LOGIN,{method:"POST",body:e}),getStores:async()=>o(c.AUTH.STORES,{method:"GET"}),getUserStores:async e=>o(c.AUTH.STORES,{method:"POST",body:e})},members:{getMembers:async e=>o(c.MEMBERS.BASE,{params:e}),getMember:async e=>o(c.MEMBERS.DETAIL(e)),createMember:async e=>o(c.MEMBERS.BASE,{method:"POST",body:e}),updateMember:async(e,t)=>o(c.MEMBERS.DETAIL(e),{method:"PUT",body:t}),deleteMember:async e=>o(c.MEMBERS.DETAIL(e),{method:"DELETE"})},products:{getProducts:async e=>o(c.PRODUCTS.BASE,{params:e}),getProduct:async e=>o(c.PRODUCTS.DETAIL(e)),createProduct:async e=>o(c.PRODUCTS.BASE,{method:"POST",body:e}),updateProduct:async(e,t)=>o(c.PRODUCTS.DETAIL(e),{method:"PUT",body:t}),deleteProduct:async e=>o(c.PRODUCTS.DETAIL(e),{method:"DELETE"})},services:{getServices:async e=>o(c.SERVICES.BASE,{params:e}),getService:async e=>o(c.SERVICES.DETAIL(e)),createService:async e=>o(c.SERVICES.BASE,{method:"POST",body:e}),updateService:async(e,t)=>o(c.SERVICES.DETAIL(e),{method:"PUT",body:t}),deleteService:async e=>o(c.SERVICES.DETAIL(e),{method:"DELETE"})},resources:{getResources:async e=>o(c.RESOURCES.BASE,{params:e}),getResource:async e=>o(c.RESOURCES.DETAIL(e)),createResource:async e=>o(c.RESOURCES.BASE,{method:"POST",body:e}),updateResource:async(e,t)=>o(c.RESOURCES.DETAIL(e),{method:"PUT",body:t}),deleteResource:async e=>o(c.RESOURCES.DETAIL(e),{method:"DELETE"})},transactions:{getTransactions:async e=>o(c.TRANSACTIONS.BASE,{params:e}),getTransaction:async e=>o(c.TRANSACTIONS.DETAIL(e)),createTransaction:async e=>o(c.TRANSACTIONS.BASE,{method:"POST",body:e}),getTransactionItems:async e=>o(c.TRANSACTIONS.ITEMS(e)),addTransactionItem:async(e,t)=>o(c.TRANSACTIONS.ITEMS(e),{method:"POST",body:t}),removeTransactionItem:async(e,t)=>o(c.TRANSACTIONS.ITEM_DETAIL(e,t),{method:"DELETE"}),completeTransaction:async e=>o(c.TRANSACTIONS.COMPLETE(e),{method:"PUT"}),refundTransaction:async(e,t)=>o(c.TRANSACTIONS.REFUND(e),{method:"PUT",body:t}),cancelTransaction:async(e,t)=>o(c.TRANSACTIONS.CANCEL(e),{method:"PUT",body:{reason:t}})},sessions:{getSessions:async e=>o(c.SESSIONS.BASE,{params:e}),getSession:async e=>o(c.SESSIONS.DETAIL(e)),createSession:async e=>o(c.SESSIONS.BASE,{method:"POST",body:e}),updateSession:async(e,t)=>o(c.SESSIONS.DETAIL(e),{method:"PUT",body:t}),closeSession:async(e,t)=>o(c.SESSIONS.CLOSE(e),{method:"PUT",body:t}),addServiceToSession:async(e,t)=>o(c.SESSIONS.SERVICES(e),{method:"POST",body:t}),addProductToSession:async(e,t)=>o(c.SESSIONS.PRODUCTS(e),{method:"POST",body:t})},reports:n},d=i}}]);