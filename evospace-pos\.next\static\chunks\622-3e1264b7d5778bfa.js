"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[622],{8025:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(57515),i=n(95155);let o=(0,r.A)((0,i.jsx)("path",{d:"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3m-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3m0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5m8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5"}),"People")},20250:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(57515),i=n(95155);let o=(0,r.A)((0,i.jsx)("path",{d:"m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"}),"TrendingUp")},21351:(t,e,n)=>{let r;function i(t){return t+.5|0}n.d(e,{$:()=>eu,A:()=>tE,B:()=>tI,C:()=>ec,D:()=>tO,E:()=>ew,F:()=>X,G:()=>eJ,H:()=>tf,I:()=>eX,J:()=>e1,K:()=>e0,L:()=>tD,M:()=>e$,N:()=>tm,O:()=>B,P:()=>to,Q:()=>$,R:()=>e_,S:()=>tR,T:()=>ta,U:()=>tw,V:()=>er,W:()=>tj,X:()=>eo,Y:()=>ef,Z:()=>eg,_:()=>tB,a:()=>eO,a0:()=>ek,a1:()=>t$,a2:()=>tX,a3:()=>t6,a4:()=>Q,a5:()=>tt,a6:()=>t9,a7:()=>tn,a8:()=>function t(e,n,r,i){return new Proxy({_cacheable:!1,_proxy:e,_context:n,_subProxy:r,_stack:new Set,_descriptors:eS(e,i),setContext:n=>t(e,n,r,i),override:o=>t(e.override(o),n,r,i)},{deleteProperty:(t,n)=>(delete t[n],delete e[n],!0),get:(e,n,r)=>eC(e,n,()=>(function(e,n,r){let{_proxy:i,_context:o,_subProxy:a,_descriptors:l}=e,s=i[n];return tn(s)&&l.isScriptable(n)&&(s=function(t,e,n,r){let{_proxy:i,_context:o,_subProxy:a,_stack:l}=n;if(l.has(t))throw Error("Recursion detected: "+Array.from(l).join("->")+"->"+t);l.add(t);let s=e(o,a||r);return l.delete(t),ej(t,s)&&(s=eI(i._scopes,i,t,s)),s}(n,s,e,r)),W(s)&&s.length&&(s=function(e,n,r,i){let{_proxy:o,_context:a,_subProxy:l,_descriptors:s}=r;if(void 0!==a.index&&i(e))return n[a.index%n.length];if(N(n[0])){let r=n,i=o._scopes.filter(t=>t!==r);for(let c of(n=[],r)){let r=eI(i,o,e,c);n.push(t(r,a,l&&l[e],s))}}return n}(n,s,e,l.isIndexable)),ej(n,s)&&(s=t(s,o,a&&a[n],l)),s})(e,n,r)),getOwnPropertyDescriptor:(t,n)=>t._descriptors.allKeys?Reflect.has(e,n)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,n),getPrototypeOf:()=>Reflect.getPrototypeOf(e),has:(t,n)=>Reflect.has(e,n),ownKeys:()=>Reflect.ownKeys(e),set:(t,n,r)=>(e[n]=r,delete t[n],!0)})},a9:()=>eP,aA:()=>e4,aB:()=>e8,aC:()=>tV,aD:()=>e6,aE:()=>es,aF:()=>t_,aG:()=>I,aH:()=>tv,aI:()=>tb,aJ:()=>tx,aK:()=>tp,aL:()=>tk,aM:()=>t8,aN:()=>td,aO:()=>ei,aP:()=>tF,aQ:()=>tA,aa:()=>eS,ab:()=>K,ac:()=>E,ad:()=>tH,ae:()=>eG,af:()=>ea,ag:()=>tr,ah:()=>na,ai:()=>V,aj:()=>ti,ak:()=>tC,al:()=>ex,am:()=>eH,an:()=>nn,ao:()=>ne,ap:()=>e2,aq:()=>e3,ar:()=>e5,as:()=>eh,at:()=>ed,au:()=>el,av:()=>ep,aw:()=>ev,ax:()=>eM,ay:()=>nt,az:()=>tP,b:()=>W,c:()=>tG,d:()=>en,e:()=>tU,f:()=>G,g:()=>Y,h:()=>te,i:()=>N,j:()=>eT,k:()=>F,l:()=>tN,m:()=>D,n:()=>H,o:()=>t3,p:()=>tS,q:()=>tq,r:()=>tL,s:()=>tg,t:()=>tM,u:()=>tY,v:()=>L,w:()=>tz,x:()=>ty,y:()=>eN,z:()=>eK});let o=(t,e,n)=>Math.max(Math.min(t,n),e);function a(t){return o(i(2.55*t),0,255)}function l(t){return o(i(255*t),0,255)}function s(t){return o(i(t/2.55)/100,0,1)}function c(t){return o(i(100*t),0,100)}let f={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},u=[..."0123456789ABCDEF"],h=t=>u[15&t],d=t=>u[(240&t)>>4]+u[15&t],g=t=>(240&t)>>4==(15&t),p=t=>g(t.r)&&g(t.g)&&g(t.b)&&g(t.a),b=(t,e)=>t<255?e(t):"",m=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function y(t,e,n){let r=e*Math.min(n,1-n),i=(e,i=(e+t/30)%12)=>n-r*Math.max(Math.min(i-3,9-i,1),-1);return[i(0),i(8),i(4)]}function x(t,e,n){let r=(r,i=(r+t/60)%6)=>n-n*e*Math.max(Math.min(i,4-i,1),0);return[r(5),r(3),r(1)]}function v(t,e,n){let r,i=y(t,1,.5);for(e+n>1&&(r=1/(e+n),e*=r,n*=r),r=0;r<3;r++)i[r]*=1-e-n,i[r]+=e;return i}function M(t){let e,n,r,i=t.r/255,o=t.g/255,a=t.b/255,l=Math.max(i,o,a),s=Math.min(i,o,a),c=(l+s)/2;l!==s&&(r=l-s,n=c>.5?r/(2-l-s):r/(l+s),e=60*(e=i===l?(o-a)/r+6*(o<a):o===l?(a-i)/r+2:(i-o)/r+4)+.5);return[0|e,n||0,c]}function w(t,e,n,r){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,r)).map(l)}function k(t){return(t%360+360)%360}let O={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},_={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},T=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,P=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,S=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function R(t,e,n){if(t){let r=M(t);r[e]=Math.max(0,Math.min(r[e]+r[e]*n,0===e?360:1)),t.r=(r=w(y,r,void 0,void 0))[0],t.g=r[1],t.b=r[2]}}function j(t,e){return t?Object.assign(e||{},t):t}function C(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=l(t[3]))):(e=j(t,{r:0,g:0,b:0,a:1})).a=l(e.a),e}class A{constructor(t){let e;if(t instanceof A)return t;let n=typeof t;"object"===n?e=C(t):"string"===n&&(e=function(t){var e,n=t.length;return"#"===t[0]&&(4===n||5===n?e={r:255&17*f[t[1]],g:255&17*f[t[2]],b:255&17*f[t[3]],a:5===n?17*f[t[4]]:255}:(7===n||9===n)&&(e={r:f[t[1]]<<4|f[t[2]],g:f[t[3]]<<4|f[t[4]],b:f[t[5]]<<4|f[t[6]],a:9===n?f[t[7]]<<4|f[t[8]]:255})),e}(t)||function(t){r||((r=function(){let t,e,n,r,i,o={},a=Object.keys(_),l=Object.keys(O);for(t=0;t<a.length;t++){for(e=0,r=i=a[t];e<l.length;e++)n=l[e],i=i.replace(n,O[n]);n=parseInt(_[r],16),o[i]=[n>>16&255,n>>8&255,255&n]}return o}()).transparent=[0,0,0,0]);let e=r[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,n,r,i=T.exec(t),l=255;if(i){if(i[7]!==e){let t=+i[7];l=i[8]?a(t):o(255*t,0,255)}return e=+i[1],n=+i[3],r=+i[5],e=255&(i[2]?a(e):o(e,0,255)),{r:e,g:n=255&(i[4]?a(n):o(n,0,255)),b:r=255&(i[6]?a(r):o(r,0,255)),a:l}}}(t):function(t){let e,n=m.exec(t),r=255;if(!n)return;n[5]!==e&&(r=n[6]?a(+n[5]):l(+n[5]));let i=k(+n[2]),o=n[3]/100,s=n[4]/100;return{r:(e="hwb"===n[1]?w(v,i,o,s):"hsv"===n[1]?w(x,i,o,s):w(y,i,o,s))[0],g:e[1],b:e[2],a:r}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=j(this._rgb);return t&&(t.a=s(t.a)),t}set rgb(t){this._rgb=C(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${s(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;return this._valid?(e=p(t=this._rgb)?h:d,t?"#"+e(t.r)+e(t.g)+e(t.b)+b(t.a,e):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=M(t),n=e[0],r=c(e[1]),i=c(e[2]);return t.a<255?`hsla(${n}, ${r}%, ${i}%, ${s(t.a)})`:`hsl(${n}, ${r}%, ${i}%)`}(this._rgb):void 0}mix(t,e){if(t){let n,r=this.rgb,i=t.rgb,o=e===n?.5:e,a=2*o-1,l=r.a-i.a,s=((a*l==-1?a:(a+l)/(1+a*l))+1)/2;n=1-s,r.r=255&s*r.r+n*i.r+.5,r.g=255&s*r.g+n*i.g+.5,r.b=255&s*r.b+n*i.b+.5,r.a=o*r.a+(1-o)*i.a,this.rgb=r}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,n){let r=S(s(t.r)),i=S(s(t.g)),o=S(s(t.b));return{r:l(P(r+n*(S(s(e.r))-r))),g:l(P(i+n*(S(s(e.g))-i))),b:l(P(o+n*(S(s(e.b))-o))),a:t.a+n*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new A(this.rgb)}alpha(t){return this._rgb.a=l(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=i(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return R(this._rgb,2,t),this}darken(t){return R(this._rgb,2,-t),this}saturate(t){return R(this._rgb,1,t),this}desaturate(t){return R(this._rgb,1,-t),this}rotate(t){var e,n;return e=this._rgb,(n=M(e))[0]=k(n[0]+t),e.r=(n=w(y,n,void 0,void 0))[0],e.g=n[1],e.b=n[2],this}}function I(){}let E=(()=>{let t=0;return()=>t++})();function F(t){return null==t}function W(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function N(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function Y(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function B(t,e){return Y(t)?t:e}function L(t,e){return void 0===t?e:t}let D=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,H=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function $(t,e,n){if(t&&"function"==typeof t.call)return t.apply(n,e)}function X(t,e,n,r){let i,o,a;if(W(t))if(o=t.length,r)for(i=o-1;i>=0;i--)e.call(n,t[i],i);else for(i=0;i<o;i++)e.call(n,t[i],i);else if(N(t))for(i=0,o=(a=Object.keys(t)).length;i<o;i++)e.call(n,t[a[i]],a[i])}function V(t,e){let n,r,i,o;if(!t||!e||t.length!==e.length)return!1;for(n=0,r=t.length;n<r;++n)if(i=t[n],o=e[n],i.datasetIndex!==o.datasetIndex||i.index!==o.index)return!1;return!0}function q(t){if(W(t))return t.map(q);if(N(t)){let e=Object.create(null),n=Object.keys(t),r=n.length,i=0;for(;i<r;++i)e[n[i]]=q(t[n[i]]);return e}return t}function z(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function Z(t,e,n,r){if(!z(t))return;let i=e[t],o=n[t];N(i)&&N(o)?Q(i,o,r):e[t]=q(o)}function Q(t,e,n){let r,i=W(e)?e:[e],o=i.length;if(!N(t))return t;let a=(n=n||{}).merger||Z;for(let e=0;e<o;++e){if(!N(r=i[e]))continue;let o=Object.keys(r);for(let e=0,i=o.length;e<i;++e)a(o[e],t,r,n)}return t}function K(t,e){return Q(t,e,{merger:U})}function U(t,e,n){if(!z(t))return;let r=e[t],i=n[t];N(r)&&N(i)?K(r,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=q(i))}let J={"":t=>t,x:t=>t.x,y:t=>t.y};function G(t,e){return(J[e]||(J[e]=function(t){let e=function(t){let e=t.split("."),n=[],r="";for(let t of e)(r+=t).endsWith("\\")?r=r.slice(0,-1)+".":(n.push(r),r="");return n}(t);return t=>{for(let n of e){if(""===n)break;t=t&&t[n]}return t}}(e)))(t)}function tt(t){return t.charAt(0).toUpperCase()+t.slice(1)}let te=t=>void 0!==t,tn=t=>"function"==typeof t,tr=(t,e)=>{if(t.size!==e.size)return!1;for(let n of t)if(!e.has(n))return!1;return!0};function ti(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}let to=Math.PI,ta=2*to,tl=ta+to,ts=Number.POSITIVE_INFINITY,tc=to/180,tf=to/2,tu=to/4,th=2*to/3,td=Math.log10,tg=Math.sign;function tp(t,e,n){return Math.abs(t-e)<n}function tb(t){let e=Math.round(t),n=Math.pow(10,Math.floor(td(t=tp(t,e,t/1e3)?e:t))),r=t/n;return(r<=1?1:r<=2?2:r<=5?5:10)*n}function tm(t){let e,n=[],r=Math.sqrt(t);for(e=1;e<r;e++)t%e==0&&(n.push(e),n.push(t/e));return r===(0|r)&&n.push(r),n.sort((t,e)=>t-e).pop(),n}function ty(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function tx(t,e){let n=Math.round(t);return n-e<=t&&n+e>=t}function tv(t,e,n){let r,i,o;for(r=0,i=t.length;r<i;r++)isNaN(o=t[r][n])||(e.min=Math.min(e.min,o),e.max=Math.max(e.max,o))}function tM(t){return to/180*t}function tw(t){return 180/to*t}function tk(t){if(!Y(t))return;let e=1,n=0;for(;Math.round(t*e)/e!==t;)e*=10,n++;return n}function tO(t,e){let n=e.x-t.x,r=e.y-t.y,i=Math.sqrt(n*n+r*r),o=Math.atan2(r,n);return o<-.5*to&&(o+=ta),{angle:o,distance:i}}function t_(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function tT(t,e){return(t-e+tl)%ta-to}function tP(t){return(t%ta+ta)%ta}function tS(t,e,n,r){let i=tP(t),o=tP(e),a=tP(n),l=tP(o-i),s=tP(a-i),c=tP(i-o),f=tP(i-a);return i===o||i===a||r&&o===a||l>s&&c<f}function tR(t,e,n){return Math.max(e,Math.min(n,t))}function tj(t){return tR(t,-32768,32767)}function tC(t,e,n,r=1e-6){return t>=Math.min(e,n)-r&&t<=Math.max(e,n)+r}function tA(t,e,n){let r;n=n||(n=>t[n]<e);let i=t.length-1,o=0;for(;i-o>1;)n(r=o+i>>1)?o=r:i=r;return{lo:o,hi:i}}let tI=(t,e,n,r)=>tA(t,n,r?r=>{let i=t[r][e];return i<n||i===n&&t[r+1][e]===n}:r=>t[r][e]<n),tE=(t,e,n)=>tA(t,n,r=>t[r][e]>=n);function tF(t,e,n){let r=0,i=t.length;for(;r<i&&t[r]<e;)r++;for(;i>r&&t[i-1]>n;)i--;return r>0||i<t.length?t.slice(r,i):t}let tW=["push","pop","shift","splice","unshift"];function tN(t,e){if(t._chartjs)return void t._chartjs.listeners.push(e);Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),tW.forEach(e=>{let n="_onData"+tt(e),r=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let i=r.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[n]&&t[n](...e)}),i}})})}function tY(t,e){let n=t._chartjs;if(!n)return;let r=n.listeners,i=r.indexOf(e);-1!==i&&r.splice(i,1),r.length>0||(tW.forEach(e=>{delete t[e]}),delete t._chartjs)}function tB(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let tL="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function tD(t,e){let n=[],r=!1;return function(...i){n=i,r||(r=!0,tL.call(window,()=>{r=!1,t.apply(e,n)}))}}function tH(t,e){let n;return function(...r){return e?(clearTimeout(n),n=setTimeout(t,e,r)):t.apply(this,r),e}}let t$=t=>"start"===t?"left":"end"===t?"right":"center",tX=(t,e,n)=>"start"===t?e:"end"===t?n:(e+n)/2,tV=(t,e,n,r)=>t===(r?"left":"right")?n:"center"===t?(e+n)/2:e;function tq(t,e,n){let r=e.length,i=0,o=r;if(t._sorted){let{iScale:a,vScale:l,_parsed:s}=t,c=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,f=a.axis,{min:u,max:h,minDefined:d,maxDefined:g}=a.getUserBounds();if(d){if(i=Math.min(tI(s,f,u).lo,n?r:tI(e,f,a.getPixelForValue(u)).lo),c){let t=s.slice(0,i+1).reverse().findIndex(t=>!F(t[l.axis]));i-=Math.max(0,t)}i=tR(i,0,r-1)}if(g){let t=Math.max(tI(s,a.axis,h,!0).hi+1,n?0:tI(e,f,a.getPixelForValue(h),!0).hi+1);if(c){let e=s.slice(t-1).findIndex(t=>!F(t[l.axis]));t+=Math.max(0,e)}o=tR(t,i,r)-i}else o=r-i}return{start:i,count:o}}function tz(t){let{xScale:e,yScale:n,_scaleRanges:r}=t,i={xmin:e.min,xmax:e.max,ymin:n.min,ymax:n.max};if(!r)return t._scaleRanges=i,!0;let o=r.xmin!==e.min||r.xmax!==e.max||r.ymin!==n.min||r.ymax!==n.max;return Object.assign(r,i),o}let tZ=t=>0===t||1===t,tQ=(t,e,n)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*ta/n)),tK=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*ta/n)+1,tU={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*tf)+1,easeOutSine:t=>Math.sin(t*tf),easeInOutSine:t=>-.5*(Math.cos(to*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>tZ(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>tZ(t)?t:tQ(t,.075,.3),easeOutElastic:t=>tZ(t)?t:tK(t,.075,.3),easeInOutElastic:t=>tZ(t)?t:t<.5?.5*tQ(2*t,.1125,.45):.5+.5*tK(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-tU.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*tU.easeInBounce(2*t):.5*tU.easeOutBounce(2*t-1)+.5};function tJ(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function tG(t){return tJ(t)?t:new A(t)}function t0(t){return tJ(t)?t:new A(t).saturate(.5).darken(.1).hexString()}let t1=["x","y","borderWidth","radius","tension"],t5=["color","borderColor","backgroundColor"],t2=new Map;function t3(t,e,n){return(function(t,e){let n=t+JSON.stringify(e=e||{}),r=t2.get(n);return r||(r=new Intl.NumberFormat(t,e),t2.set(n,r)),r})(e,n).format(t)}let t4={values:t=>W(t)?t:""+t,numeric(t,e,n){let r;if(0===t)return"0";let i=this.chart.options.locale,o=t;if(n.length>1){var a,l;let e,i=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(i<1e-4||i>1e15)&&(r="scientific"),a=t,Math.abs(e=(l=n).length>3?l[2].value-l[1].value:l[1].value-l[0].value)>=1&&a!==Math.floor(a)&&(e=a-Math.floor(a)),o=e}let s=td(Math.abs(o)),c=isNaN(s)?1:Math.max(Math.min(-1*Math.floor(s),20),0),f={notation:r,minimumFractionDigits:c,maximumFractionDigits:c};return Object.assign(f,this.options.ticks.format),t3(t,i,f)},logarithmic(t,e,n){return 0===t?"0":[1,2,3,5,10,15].includes(n[e].significand||t/Math.pow(10,Math.floor(td(t))))||e>.8*n.length?t4.numeric.call(this,t,e,n):""}};var t8={formatters:t4};let t6=Object.create(null),t9=Object.create(null);function t7(t,e){if(!e)return t;let n=e.split(".");for(let e=0,r=n.length;e<r;++e){let r=n[e];t=t[r]||(t[r]=Object.create(null))}return t}function et(t,e,n){return"string"==typeof e?Q(t7(t,e),n):Q(t7(t,""),e)}class ee{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>t0(e.backgroundColor),this.hoverBorderColor=(t,e)=>t0(e.borderColor),this.hoverColor=(t,e)=>t0(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return et(this,t,e)}get(t){return t7(this,t)}describe(t,e){return et(t9,t,e)}override(t,e){return et(t6,t,e)}route(t,e,n,r){let i=t7(this,t),o=t7(this,n),a="_"+e;Object.defineProperties(i,{[a]:{value:i[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[a],e=o[r];return N(t)?Object.assign({},e,t):L(t,e)},set(t){this[a]=t}}})}apply(t){t.forEach(t=>t(this))}}var en=new ee({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:t5},numbers:{type:"number",properties:t1}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:t8.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function er(t,e,n,r,i){let o=e[i];return o||(o=e[i]=t.measureText(i).width,n.push(i)),o>r&&(r=o),r}function ei(t,e,n,r){let i,o,a,l,s,c=(r=r||{}).data=r.data||{},f=r.garbageCollect=r.garbageCollect||[];r.font!==e&&(c=r.data={},f=r.garbageCollect=[],r.font=e),t.save(),t.font=e;let u=0,h=n.length;for(i=0;i<h;i++)if(null==(l=n[i])||W(l)){if(W(l))for(o=0,a=l.length;o<a;o++)null==(s=l[o])||W(s)||(u=er(t,c,f,u,s))}else u=er(t,c,f,u,l);t.restore();let d=f.length/2;if(d>n.length){for(i=0;i<d;i++)delete c[f[i]];f.splice(0,d)}return u}function eo(t,e,n){let r=t.currentDevicePixelRatio,i=0!==n?Math.max(n/2,.5):0;return Math.round((e-i)*r)/r+i}function ea(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function el(t,e,n,r){es(t,e,n,r,null)}function es(t,e,n,r,i){let o,a,l,s,c,f,u,h,d=e.pointStyle,g=e.rotation,p=e.radius,b=(g||0)*tc;if(d&&"object"==typeof d&&("[object HTMLImageElement]"===(o=d.toString())||"[object HTMLCanvasElement]"===o)){t.save(),t.translate(n,r),t.rotate(b),t.drawImage(d,-d.width/2,-d.height/2,d.width,d.height),t.restore();return}if(!isNaN(p)&&!(p<=0)){switch(t.beginPath(),d){default:i?t.ellipse(n,r,i/2,p,0,0,ta):t.arc(n,r,p,0,ta),t.closePath();break;case"triangle":f=i?i/2:p,t.moveTo(n+Math.sin(b)*f,r-Math.cos(b)*p),b+=th,t.lineTo(n+Math.sin(b)*f,r-Math.cos(b)*p),b+=th,t.lineTo(n+Math.sin(b)*f,r-Math.cos(b)*p),t.closePath();break;case"rectRounded":c=.516*p,a=Math.cos(b+tu)*(s=p-c),u=Math.cos(b+tu)*(i?i/2-c:s),l=Math.sin(b+tu)*s,h=Math.sin(b+tu)*(i?i/2-c:s),t.arc(n-u,r-l,c,b-to,b-tf),t.arc(n+h,r-a,c,b-tf,b),t.arc(n+u,r+l,c,b,b+tf),t.arc(n-h,r+a,c,b+tf,b+to),t.closePath();break;case"rect":if(!g){s=Math.SQRT1_2*p,f=i?i/2:s,t.rect(n-f,r-s,2*f,2*s);break}b+=tu;case"rectRot":u=Math.cos(b)*(i?i/2:p),a=Math.cos(b)*p,l=Math.sin(b)*p,h=Math.sin(b)*(i?i/2:p),t.moveTo(n-u,r-l),t.lineTo(n+h,r-a),t.lineTo(n+u,r+l),t.lineTo(n-h,r+a),t.closePath();break;case"crossRot":b+=tu;case"cross":u=Math.cos(b)*(i?i/2:p),a=Math.cos(b)*p,l=Math.sin(b)*p,h=Math.sin(b)*(i?i/2:p),t.moveTo(n-u,r-l),t.lineTo(n+u,r+l),t.moveTo(n+h,r-a),t.lineTo(n-h,r+a);break;case"star":u=Math.cos(b)*(i?i/2:p),a=Math.cos(b)*p,l=Math.sin(b)*p,h=Math.sin(b)*(i?i/2:p),t.moveTo(n-u,r-l),t.lineTo(n+u,r+l),t.moveTo(n+h,r-a),t.lineTo(n-h,r+a),b+=tu,u=Math.cos(b)*(i?i/2:p),a=Math.cos(b)*p,l=Math.sin(b)*p,h=Math.sin(b)*(i?i/2:p),t.moveTo(n-u,r-l),t.lineTo(n+u,r+l),t.moveTo(n+h,r-a),t.lineTo(n-h,r+a);break;case"line":a=i?i/2:Math.cos(b)*p,l=Math.sin(b)*p,t.moveTo(n-a,r-l),t.lineTo(n+a,r+l);break;case"dash":t.moveTo(n,r),t.lineTo(n+Math.cos(b)*(i?i/2:p),r+Math.sin(b)*p);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function ec(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function ef(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function eu(t){t.restore()}function eh(t,e,n,r,i){if(!e)return t.lineTo(n.x,n.y);if("middle"===i){let r=(e.x+n.x)/2;t.lineTo(r,e.y),t.lineTo(r,n.y)}else"after"===i!=!!r?t.lineTo(e.x,n.y):t.lineTo(n.x,e.y);t.lineTo(n.x,n.y)}function ed(t,e,n,r){if(!e)return t.lineTo(n.x,n.y);t.bezierCurveTo(r?e.cp1x:e.cp2x,r?e.cp1y:e.cp2y,r?n.cp2x:n.cp1x,r?n.cp2y:n.cp1y,n.x,n.y)}function eg(t,e,n,r,i,o={}){let a,l,s=W(e)?e:[e],c=o.strokeWidth>0&&""!==o.strokeColor;for(t.save(),t.font=i.string,o.translation&&t.translate(o.translation[0],o.translation[1]),F(o.rotation)||t.rotate(o.rotation),o.color&&(t.fillStyle=o.color),o.textAlign&&(t.textAlign=o.textAlign),o.textBaseline&&(t.textBaseline=o.textBaseline),a=0;a<s.length;++a)l=s[a],o.backdrop&&function(t,e){let n=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=n}(t,o.backdrop),c&&(o.strokeColor&&(t.strokeStyle=o.strokeColor),F(o.strokeWidth)||(t.lineWidth=o.strokeWidth),t.strokeText(l,n,r,o.maxWidth)),t.fillText(l,n,r,o.maxWidth),function(t,e,n,r,i){if(i.strikethrough||i.underline){let o=t.measureText(r),a=e-o.actualBoundingBoxLeft,l=e+o.actualBoundingBoxRight,s=n-o.actualBoundingBoxAscent,c=n+o.actualBoundingBoxDescent,f=i.strikethrough?(s+c)/2:c;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=i.decorationWidth||2,t.moveTo(a,f),t.lineTo(l,f),t.stroke()}}(t,n,r,l,o),r+=Number(i.lineHeight);t.restore()}function ep(t,e){let{x:n,y:r,w:i,h:o,radius:a}=e;t.arc(n+a.topLeft,r+a.topLeft,a.topLeft,1.5*to,to,!0),t.lineTo(n,r+o-a.bottomLeft),t.arc(n+a.bottomLeft,r+o-a.bottomLeft,a.bottomLeft,to,tf,!0),t.lineTo(n+i-a.bottomRight,r+o),t.arc(n+i-a.bottomRight,r+o-a.bottomRight,a.bottomRight,tf,0,!0),t.lineTo(n+i,r+a.topRight),t.arc(n+i-a.topRight,r+a.topRight,a.topRight,0,-tf,!0),t.lineTo(n+a.topLeft,r)}let eb=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,em=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,ey=t=>+t||0;function ex(t,e){let n={},r=N(e),i=r?Object.keys(e):e,o=N(t)?r?n=>L(t[n],t[e[n]]):e=>t[e]:()=>t;for(let t of i)n[t]=ey(o(t));return n}function ev(t){return ex(t,{top:"y",right:"x",bottom:"y",left:"x"})}function eM(t){return ex(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ew(t){let e=ev(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function ek(t,e){t=t||{},e=e||en.font;let n=L(t.size,e.size);"string"==typeof n&&(n=parseInt(n,10));let r=L(t.style,e.style);r&&!(""+r).match(em)&&(console.warn('Invalid font style specified: "'+r+'"'),r=void 0);let i={family:L(t.family,e.family),lineHeight:function(t,e){let n=(""+t).match(eb);if(!n||"normal"===n[1])return 1.2*e;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100}return e*t}(L(t.lineHeight,e.lineHeight),n),size:n,style:r,weight:L(t.weight,e.weight),string:""};return i.string=!i||F(i.size)||F(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family,i}function eO(t,e,n,r){let i,o,a,l=!0;for(i=0,o=t.length;i<o;++i)if(void 0!==(a=t[i])&&(void 0!==e&&"function"==typeof a&&(a=a(e),l=!1),void 0!==n&&W(a)&&(a=a[n%a.length],l=!1),void 0!==a))return r&&!l&&(r.cacheable=!1),a}function e_(t,e,n){let{min:r,max:i}=t,o=H(e,(i-r)/2),a=(t,e)=>n&&0===t?0:t+e;return{min:a(r,-Math.abs(o)),max:a(i,o)}}function eT(t,e){return Object.assign(Object.create(t),e)}function eP(t,e=[""],n,r,i=()=>t[0]){let o=n||t;return void 0===r&&(r=eF("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:o,_fallback:r,_getTarget:i,override:n=>eP([n,...t],e,o,r)},{deleteProperty:(e,n)=>(delete e[n],delete e._keys,delete t[0][n],!0),get:(n,r)=>eC(n,r,()=>(function(t,e,n,r){let i;for(let o of e)if(void 0!==(i=eF(eR(o,t),n)))return ej(t,i)?eI(n,r,t,i):i})(r,e,t,n)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>eW(t).includes(e),ownKeys:t=>eW(t),set(t,e,n){let r=t._storage||(t._storage=i());return t[e]=r[e]=n,delete t._keys,!0}})}function eS(t,e={scriptable:!0,indexable:!0}){let{_scriptable:n=e.scriptable,_indexable:r=e.indexable,_allKeys:i=e.allKeys}=t;return{allKeys:i,scriptable:n,indexable:r,isScriptable:tn(n)?n:()=>n,isIndexable:tn(r)?r:()=>r}}let eR=(t,e)=>t?t+tt(e):e,ej=(t,e)=>N(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function eC(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let r=n();return t[e]=r,r}let eA=(t,e)=>!0===t?e:"string"==typeof t?G(e,t):void 0;function eI(t,e,n,r){var i;let o=e._rootScopes,a=(i=e._fallback,tn(i)?i(n,r):i),l=[...t,...o],s=new Set;s.add(r);let c=eE(s,l,n,a||n,r);return null!==c&&(void 0===a||a===n||null!==(c=eE(s,l,a,c,r)))&&eP(Array.from(s),[""],o,a,()=>(function(t,e,n){let r=t._getTarget();e in r||(r[e]={});let i=r[e];return W(i)&&N(n)?n:i||{}})(e,n,r))}function eE(t,e,n,r,i){for(;n;)n=function(t,e,n,r,i){for(let a of e){let e=eA(n,a);if(e){var o;t.add(e);let a=(o=e._fallback,tn(o)?o(n,i):o);if(void 0!==a&&a!==n&&a!==r)return a}else if(!1===e&&void 0!==r&&n!==r)return null}return!1}(t,e,n,r,i);return n}function eF(t,e){for(let n of e){if(!n)continue;let e=n[t];if(void 0!==e)return e}}function eW(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let n of t)for(let t of Object.keys(n).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function eN(t,e,n,r){let i,o,a,{iScale:l}=t,{key:s="r"}=this._parsing,c=Array(r);for(i=0;i<r;++i)a=e[o=i+n],c[i]={r:l.parse(G(a,s),o)};return c}let eY=Number.EPSILON||1e-14,eB=(t,e)=>e<t.length&&!t[e].skip&&t[e],eL=t=>"x"===t?"y":"x";function eD(t,e,n){return Math.max(Math.min(t,n),e)}function eH(t,e,n,r,i){let o,a,l,s;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let n,r,i,o=eL(e),a=t.length,l=Array(a).fill(0),s=Array(a),c=eB(t,0);for(n=0;n<a;++n)if(r=i,i=c,c=eB(t,n+1),i){if(c){let t=c[e]-i[e];l[n]=0!==t?(c[o]-i[o])/t:0}s[n]=r?c?tg(l[n-1])!==tg(l[n])?0:(l[n-1]+l[n])/2:l[n-1]:l[n]}!function(t,e,n){let r,i,o,a,l,s=t.length,c=eB(t,0);for(let f=0;f<s-1;++f)if(l=c,c=eB(t,f+1),l&&c){if(tp(e[f],0,eY)){n[f]=n[f+1]=0;continue}(a=Math.pow(r=n[f]/e[f],2)+Math.pow(i=n[f+1]/e[f],2))<=9||(o=3/Math.sqrt(a),n[f]=r*o*e[f],n[f+1]=i*o*e[f])}}(t,l,s),function(t,e,n="x"){let r,i,o,a=eL(n),l=t.length,s=eB(t,0);for(let c=0;c<l;++c){if(i=o,o=s,s=eB(t,c+1),!o)continue;let l=o[n],f=o[a];i&&(r=(l-i[n])/3,o[`cp1${n}`]=l-r,o[`cp1${a}`]=f-r*e[c]),s&&(r=(s[n]-l)/3,o[`cp2${n}`]=l+r,o[`cp2${a}`]=f+r*e[c])}}(t,s,e)}(t,i);else{let n=r?t[t.length-1]:t[0];for(o=0,a=t.length;o<a;++o)s=function(t,e,n,r){let i=t.skip?e:t,o=n.skip?e:n,a=t_(e,i),l=t_(o,e),s=a/(a+l),c=l/(a+l);s=isNaN(s)?0:s,c=isNaN(c)?0:c;let f=r*s,u=r*c;return{previous:{x:e.x-f*(o.x-i.x),y:e.y-f*(o.y-i.y)},next:{x:e.x+u*(o.x-i.x),y:e.y+u*(o.y-i.y)}}}(n,l=t[o],t[Math.min(o+1,a-!r)%a],e.tension),l.cp1x=s.previous.x,l.cp1y=s.previous.y,l.cp2x=s.next.x,l.cp2y=s.next.y,n=l}e.capBezierPoints&&function(t,e){let n,r,i,o,a,l=ec(t[0],e);for(n=0,r=t.length;n<r;++n)a=o,o=l,l=n<r-1&&ec(t[n+1],e),o&&(i=t[n],a&&(i.cp1x=eD(i.cp1x,e.left,e.right),i.cp1y=eD(i.cp1y,e.top,e.bottom)),l&&(i.cp2x=eD(i.cp2x,e.left,e.right),i.cp2y=eD(i.cp2y,e.top,e.bottom)))}(t,n)}function e$(){return"undefined"!=typeof window&&"undefined"!=typeof document}function eX(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function eV(t,e,n){let r;return"string"==typeof t?(r=parseInt(t,10),-1!==t.indexOf("%")&&(r=r/100*e.parentNode[n])):r=t,r}let eq=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),ez=["top","right","bottom","left"];function eZ(t,e,n){let r={};n=n?"-"+n:"";for(let i=0;i<4;i++){let o=ez[i];r[o]=parseFloat(t[e+"-"+o+n])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}let eQ=(t,e,n)=>(t>0||e>0)&&(!n||!n.shadowRoot);function eK(t,e){if("native"in t)return t;let{canvas:n,currentDevicePixelRatio:r}=e,i=eq(n),o="border-box"===i.boxSizing,a=eZ(i,"padding"),l=eZ(i,"border","width"),{x:s,y:c,box:f}=function(t,e){let n,r,i=t.touches,o=i&&i.length?i[0]:t,{offsetX:a,offsetY:l}=o,s=!1;if(eQ(a,l,t.target))n=a,r=l;else{let t=e.getBoundingClientRect();n=o.clientX-t.left,r=o.clientY-t.top,s=!0}return{x:n,y:r,box:s}}(t,n),u=a.left+(f&&l.left),h=a.top+(f&&l.top),{width:d,height:g}=e;return o&&(d-=a.width+l.width,g-=a.height+l.height),{x:Math.round((s-u)/d*n.width/r),y:Math.round((c-h)/g*n.height/r)}}let eU=t=>Math.round(10*t)/10;function eJ(t,e,n,r){let i=eq(t),o=eZ(i,"margin"),a=eV(i.maxWidth,t,"clientWidth")||ts,l=eV(i.maxHeight,t,"clientHeight")||ts,s=function(t,e,n){let r,i;if(void 0===e||void 0===n){let o=t&&eX(t);if(o){let t=o.getBoundingClientRect(),a=eq(o),l=eZ(a,"border","width"),s=eZ(a,"padding");e=t.width-s.width-l.width,n=t.height-s.height-l.height,r=eV(a.maxWidth,o,"clientWidth"),i=eV(a.maxHeight,o,"clientHeight")}else e=t.clientWidth,n=t.clientHeight}return{width:e,height:n,maxWidth:r||ts,maxHeight:i||ts}}(t,e,n),{width:c,height:f}=s;if("content-box"===i.boxSizing){let t=eZ(i,"border","width"),e=eZ(i,"padding");c-=e.width+t.width,f-=e.height+t.height}return c=Math.max(0,c-o.width),f=Math.max(0,r?c/r:f-o.height),c=eU(Math.min(c,a,s.maxWidth)),f=eU(Math.min(f,l,s.maxHeight)),c&&!f&&(f=eU(c/2)),(void 0!==e||void 0!==n)&&r&&s.height&&f>s.height&&(c=eU(Math.floor((f=s.height)*r))),{width:c,height:f}}function eG(t,e,n){let r=e||1,i=Math.floor(t.height*r),o=Math.floor(t.width*r);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let a=t.canvas;return a.style&&(n||!a.style.height&&!a.style.width)&&(a.style.height=`${t.height}px`,a.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==r||a.height!==i||a.width!==o)&&(t.currentDevicePixelRatio=r,a.height=i,a.width=o,t.ctx.setTransform(r,0,0,r,0,0),!0)}let e0=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};e$()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function e1(t,e){let n=eq(t).getPropertyValue(e),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?+r[1]:void 0}function e5(t,e,n,r){return{x:t.x+n*(e.x-t.x),y:t.y+n*(e.y-t.y)}}function e2(t,e,n,r){return{x:t.x+n*(e.x-t.x),y:"middle"===r?n<.5?t.y:e.y:"after"===r?n<1?t.y:e.y:n>0?e.y:t.y}}function e3(t,e,n,r){let i={x:t.cp2x,y:t.cp2y},o={x:e.cp1x,y:e.cp1y},a=e5(t,i,n),l=e5(i,o,n),s=e5(o,e,n),c=e5(a,l,n),f=e5(l,s,n);return e5(c,f,n)}function e4(t,e,n){var r;return t?(r=n,{x:t=>e+e+r-t,setWidth(t){r=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function e8(t,e){let n,r;("ltr"===e||"rtl"===e)&&(r=[(n=t.canvas.style).getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=r)}function e6(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function e9(t){return"angle"===t?{between:tS,compare:tT,normalize:tP}:{between:tC,compare:(t,e)=>t-e,normalize:t=>t}}function e7({start:t,end:e,count:n,loop:r,style:i}){return{start:t%n,end:e%n,loop:r&&(e-t+1)%n==0,style:i}}function nt(t,e,n){let r,i,o;if(!n)return[t];let{property:a,start:l,end:s}=n,c=e.length,{compare:f,between:u,normalize:h}=e9(a),{start:d,end:g,loop:p,style:b}=function(t,e,n){let r,{property:i,start:o,end:a}=n,{between:l,normalize:s}=e9(i),c=e.length,{start:f,end:u,loop:h}=t;if(h){for(f+=c,u+=c,r=0;r<c&&l(s(e[f%c][i]),o,a);++r)f--,u--;f%=c,u%=c}return u<f&&(u+=c),{start:f,end:u,loop:h,style:t.style}}(t,e,n),m=[],y=!1,x=null,v=()=>u(l,o,r)&&0!==f(l,o),M=()=>0===f(s,r)||u(s,o,r),w=()=>y||v(),k=()=>!y||M();for(let t=d,n=d;t<=g;++t)(i=e[t%c]).skip||(r=h(i[a]))!==o&&(y=u(r,l,s),null===x&&w()&&(x=0===f(r,l)?t:n),null!==x&&k()&&(m.push(e7({start:x,end:t,loop:p,count:c,style:b})),x=null),n=t,o=r);return null!==x&&m.push(e7({start:x,end:g,loop:p,count:c,style:b})),m}function ne(t,e){let n=[],r=t.segments;for(let i=0;i<r.length;i++){let o=nt(r[i],t.points,e);o.length&&n.push(...o)}return n}function nn(t,e){let n=t.points,r=t.options.spanGaps,i=n.length;if(!i)return[];let o=!!t._loop,{start:a,end:l}=function(t,e,n,r){let i=0,o=e-1;if(n&&!r)for(;i<e&&!t[i].skip;)i++;for(;i<e&&t[i].skip;)i++;for(i%=e,n&&(o+=i);o>i&&t[o%e].skip;)o--;return{start:i,end:o%=e}}(n,i,o,r);if(!0===r)return nr(t,[{start:a,end:l,loop:o}],n,e);let s=l<a?l+i:l,c=!!t._fullLoop&&0===a&&l===i-1;return nr(t,function(t,e,n,r){let i,o=t.length,a=[],l=e,s=t[e];for(i=e+1;i<=n;++i){let n=t[i%o];n.skip||n.stop?s.skip||(r=!1,a.push({start:e%o,end:(i-1)%o,loop:r}),e=l=n.stop?i:null):(l=i,s.skip&&(e=i)),s=n}return null!==l&&a.push({start:e%o,end:l%o,loop:r}),a}(n,a,s,c),n,e)}function nr(t,e,n,r){return r&&r.setContext&&n?function(t,e,n,r){let i=t._chart.getContext(),o=ni(t.options),{_datasetIndex:a,options:{spanGaps:l}}=t,s=n.length,c=[],f=o,u=e[0].start,h=u;function d(t,e,r,i){let o=l?-1:1;if(t!==e){for(t+=s;n[t%s].skip;)t-=o;for(;n[e%s].skip;)e+=o;t%s!=e%s&&(c.push({start:t%s,end:e%s,loop:r,style:i}),f=i,u=e%s)}}for(let t of e){let e,o=n[(u=l?u:t.start)%s];for(h=u+1;h<=t.end;h++){let l=n[h%s];(function(t,e){if(!e)return!1;let n=[],r=function(t,e){return tJ(e)?(n.includes(e)||n.push(e),n.indexOf(e)):e};return JSON.stringify(t,r)!==JSON.stringify(e,r)})(e=ni(r.setContext(eT(i,{type:"segment",p0:o,p1:l,p0DataIndex:(h-1)%s,p1DataIndex:h%s,datasetIndex:a}))),f)&&d(u,h-1,t.loop,f),o=l,f=e}u<h-1&&d(u,h-1,t.loop,f)}return c}(t,e,n,r):e}function ni(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function no(t,e,n){return t.options.clip?t[n]:e[n]}function na(t,e){let n=e._clip;if(n.disabled)return!1;let r=function(t,e){let{xScale:n,yScale:r}=t;return n&&r?{left:no(n,e,"left"),right:no(n,e,"right"),top:no(r,e,"top"),bottom:no(r,e,"bottom")}:e}(e,t.chartArea);return{left:!1===n.left?0:r.left-(!0===n.left?0:n.left),right:!1===n.right?t.width:r.right+(!0===n.right?0:n.right),top:!1===n.top?0:r.top-(!0===n.top?0:n.top),bottom:!1===n.bottom?t.height:r.bottom+(!0===n.bottom?0:n.bottom)}}},37857:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(57515),i=n(95155);let o=(0,r.A)((0,i.jsx)("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"}),"Refresh")},63645:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(57515),i=n(95155);let o=(0,r.A)((0,i.jsx)("path",{d:"M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4"}),"AttachMoney")},64065:(t,e,n)=>{n.d(e,{Fq:()=>d,N1:()=>f,nu:()=>h,yP:()=>u});var r=n(12115),i=n(32502);let o="label";function a(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function l(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o,r=[];t.datasets=e.map(e=>{let i=t.datasets.find(t=>t[n]===e[n]);return!i||!e.data||r.includes(i)?{...e}:(r.push(i),Object.assign(i,e),i)})}let s=(0,r.forwardRef)(function(t,e){let{height:n=150,width:s=300,redraw:c=!1,datasetIdKey:f,type:u,data:h,options:d,plugins:g=[],fallbackContent:p,updateMode:b,...m}=t,y=(0,r.useRef)(null),x=(0,r.useRef)(null),v=()=>{y.current&&(x.current=new i.t1(y.current,{type:u,data:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o,n={labels:[],datasets:[]};return n.labels=t.labels,l(n,t.datasets,e),n}(h,f),options:d&&{...d},plugins:g}),a(e,x.current))},M=()=>{a(e,null),x.current&&(x.current.destroy(),x.current=null)};return(0,r.useEffect)(()=>{!c&&x.current&&d&&function(t,e){let n=t.options;n&&e&&Object.assign(n,e)}(x.current,d)},[c,d]),(0,r.useEffect)(()=>{!c&&x.current&&(x.current.config.data.labels=h.labels)},[c,h.labels]),(0,r.useEffect)(()=>{!c&&x.current&&h.datasets&&l(x.current.config.data,h.datasets,f)},[c,h.datasets]),(0,r.useEffect)(()=>{x.current&&(c?(M(),setTimeout(v)):x.current.update(b))},[c,d,h.labels,h.datasets,b]),(0,r.useEffect)(()=>{x.current&&(M(),setTimeout(v))},[u]),(0,r.useEffect)(()=>(v(),()=>M()),[]),r.createElement("canvas",{ref:y,role:"img",height:n,width:s,...m},p)});function c(t,e){return i.t1.register(e),(0,r.forwardRef)((e,n)=>r.createElement(s,{...e,ref:n,type:t}))}let f=c("line",i.ZT),u=c("bar",i.A6),h=c("doughnut",i.ju),d=c("pie",i.P$)}}]);