(()=>{var e={};e.id=246,e.ids=[246],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3297:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var s=n(23428),r=n(60687);let a=(0,s.A)((0,r.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"Add")},6024:(e,t,n)=>{Promise.resolve().then(n.bind(n,16485))},10078:(e,t,n)=>{"use strict";n.d(t,{A:()=>a}),n(43210);var s=n(23428),r=n(60687);let a=(0,s.A)((0,r.jsx)("path",{d:"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"}),"FirstPage")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11830:(e,t,n)=>{"use strict";n.d(t,{A:()=>y});var s=n(43210),r=n(49384),a=n(43648),o=n(82816),l=n(99282);let i=(0,n(88316).Ay)();var c=n(32856),d=n(44018),u=n(30437),p=n(98896),h=n(27887),x=n(60687);let m=(0,u.A)(),A=i("div",{name:"MuiStack",slot:"Root"});function j(e){return(0,c.A)({props:e,name:"MuiStack",defaultTheme:m})}let b=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],v=({ownerState:e,theme:t})=>{let n={display:"flex",flexDirection:"column",...(0,p.NI)({theme:t},(0,p.kW)({values:e.direction,breakpoints:t.breakpoints.values}),e=>({flexDirection:e}))};if(e.spacing){let s=(0,h.LX)(t),r=Object.keys(t.breakpoints.values).reduce((t,n)=>(("object"==typeof e.spacing&&null!=e.spacing[n]||"object"==typeof e.direction&&null!=e.direction[n])&&(t[n]=!0),t),{}),o=(0,p.kW)({values:e.direction,base:r}),l=(0,p.kW)({values:e.spacing,base:r});"object"==typeof o&&Object.keys(o).forEach((e,t,n)=>{if(!o[e]){let s=t>0?o[n[t-1]]:"column";o[e]=s}}),n=(0,a.A)(n,(0,p.NI)({theme:t},l,(t,n)=>e.useFlexGap?{gap:(0,h._W)(s,t)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${b(n?o[n]:e.direction)}`]:(0,h._W)(s,t)}}))}return(0,p.iZ)(t.breakpoints,n)};var g=n(13555),f=n(84754);let y=function(e={}){let{createStyledComponent:t=A,useThemeProps:n=j,componentName:a="MuiStack"}=e,i=()=>(0,l.A)({root:["root"]},e=>(0,o.Ay)(a,e),{}),c=t(v);return s.forwardRef(function(e,t){let a=n(e),{component:o="div",direction:l="column",spacing:u=0,divider:p,children:h,className:m,useFlexGap:A=!1,...j}=(0,d.A)(a),b=i();return(0,x.jsx)(c,{as:o,ownerState:{direction:l,spacing:u,useFlexGap:A},ref:t,className:(0,r.A)(b.root,m),...j,children:p?function(e,t){let n=s.Children.toArray(e).filter(Boolean);return n.reduce((e,r,a)=>(e.push(r),a<n.length-1&&e.push(s.cloneElement(t,{key:`separator-${a}`})),e),[])}(h,p):h})})}({createStyledComponent:(0,g.Ay)("div",{name:"MuiStack",slot:"Root"}),useThemeProps:e=>(0,f.b)({props:e,name:"MuiStack"})})},16485:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>em});var s,r=n(60687),a=n(43210),o=n(88931),l=n(87088),i=n(16184),c=n(51067),d=n(16393),u=n(51711),p=n(98577),h=n(86562),x=n(30076),m=n(76897),A=n(72132),j=n(91433),b=n(12879),v=n(83685),g=n(49384),f=n(99282),y=n(13555),w=n(45258),P=n(84754),M=n(35787),C=n(47651),S=n(41629),k=n(66932),I=n(71779),B=n(52436),R=n(30051),L=n(56676),T=n(10078);let z=a.forwardRef(function(e,t){let{backIconButtonProps:n,count:s,disabled:a=!1,getItemAriaLabel:o,nextIconButtonProps:l,onPageChange:i,page:c,rowsPerPage:d,showFirstButton:u,showLastButton:p,slots:h={},slotProps:x={},...m}=e,A=(0,I.I)(),j=h.firstButton??v.A,b=h.lastButton??v.A,g=h.nextButton??v.A,f=h.previousButton??v.A,y=h.firstButtonIcon??T.A,w=h.lastButtonIcon??L.A,P=h.nextButtonIcon??R.A,M=h.previousButtonIcon??B.A,C=A?b:j,S=A?g:f,k=A?f:g,z=A?j:b,N=A?x.lastButton:x.firstButton,_=A?x.nextButton:x.previousButton,E=A?x.previousButton:x.nextButton,D=A?x.firstButton:x.lastButton;return(0,r.jsxs)("div",{ref:t,...m,children:[u&&(0,r.jsx)(C,{onClick:e=>{i(e,0)},disabled:a||0===c,"aria-label":o("first",c),title:o("first",c),...N,children:A?(0,r.jsx)(w,{...x.lastButtonIcon}):(0,r.jsx)(y,{...x.firstButtonIcon})}),(0,r.jsx)(S,{onClick:e=>{i(e,c-1)},disabled:a||0===c,color:"inherit","aria-label":o("previous",c),title:o("previous",c),..._??n,children:A?(0,r.jsx)(P,{...x.nextButtonIcon}):(0,r.jsx)(M,{...x.previousButtonIcon})}),(0,r.jsx)(k,{onClick:e=>{i(e,c+1)},disabled:a||-1!==s&&c>=Math.ceil(s/d)-1,color:"inherit","aria-label":o("next",c),title:o("next",c),...E??l,children:A?(0,r.jsx)(M,{...x.previousButtonIcon}):(0,r.jsx)(P,{...x.nextButtonIcon})}),p&&(0,r.jsx)(z,{onClick:e=>{i(e,Math.max(0,Math.ceil(s/d)-1))},disabled:a||c>=Math.ceil(s/d)-1,"aria-label":o("last",c),title:o("last",c),...D,children:A?(0,r.jsx)(y,{...x.firstButtonIcon}):(0,r.jsx)(w,{...x.lastButtonIcon})})]})});var N=n(80931),_=n(4144),E=n(82816);function D(e){return(0,E.Ay)("MuiTablePagination",e)}let W=(0,_.A)("MuiTablePagination",["root","toolbar","spacer","selectLabel","selectRoot","select","selectIcon","input","menuItem","displayedRows","actions"]);var $=n(34414);let H=(0,y.Ay)(A.A,{name:"MuiTablePagination",slot:"Root"})((0,w.A)(({theme:e})=>({overflow:"auto",color:(e.vars||e).palette.text.primary,fontSize:e.typography.pxToRem(14),"&:last-child":{padding:0}}))),q=(0,y.Ay)(k.A,{name:"MuiTablePagination",slot:"Toolbar",overridesResolver:(e,t)=>({[`& .${W.actions}`]:t.actions,...t.toolbar})})((0,w.A)(({theme:e})=>({minHeight:52,paddingRight:2,[`${e.breakpoints.up("xs")} and (orientation: landscape)`]:{minHeight:52},[e.breakpoints.up("sm")]:{minHeight:52,paddingRight:2},[`& .${W.actions}`]:{flexShrink:0,marginLeft:20}}))),V=(0,y.Ay)("div",{name:"MuiTablePagination",slot:"Spacer"})({flex:"1 1 100%"}),G=(0,y.Ay)("p",{name:"MuiTablePagination",slot:"SelectLabel"})((0,w.A)(({theme:e})=>({...e.typography.body2,flexShrink:0}))),F=(0,y.Ay)(S.A,{name:"MuiTablePagination",slot:"Select",overridesResolver:(e,t)=>({[`& .${W.selectIcon}`]:t.selectIcon,[`& .${W.select}`]:t.select,...t.input,...t.selectRoot})})({color:"inherit",fontSize:"inherit",flexShrink:0,marginRight:32,marginLeft:8,[`& .${W.select}`]:{paddingLeft:8,paddingRight:24,textAlign:"right",textAlignLast:"right"}}),O=(0,y.Ay)(C.A,{name:"MuiTablePagination",slot:"MenuItem"})({}),K=(0,y.Ay)("p",{name:"MuiTablePagination",slot:"DisplayedRows"})((0,w.A)(({theme:e})=>({...e.typography.body2,flexShrink:0})));function U({from:e,to:t,count:n}){return`${e}–${t} of ${-1!==n?n:`more than ${t}`}`}function X(e){return`Go to ${e} page`}let Z=e=>{let{classes:t}=e;return(0,f.A)({root:["root"],toolbar:["toolbar"],spacer:["spacer"],selectLabel:["selectLabel"],select:["select"],input:["input"],selectIcon:["selectIcon"],menuItem:["menuItem"],displayedRows:["displayedRows"],actions:["actions"]},D,t)},J=a.forwardRef(function(e,t){let n,o=(0,P.b)({props:e,name:"MuiTablePagination"}),{ActionsComponent:l=z,backIconButtonProps:i,colSpan:c,component:d=A.A,count:u,disabled:p=!1,getItemAriaLabel:h=X,labelDisplayedRows:x=U,labelRowsPerPage:m="Rows per page:",nextIconButtonProps:j,onPageChange:b,onRowsPerPageChange:v,page:f,rowsPerPage:y,rowsPerPageOptions:w=[10,25,50,100],SelectProps:C={},showFirstButton:S=!1,showLastButton:k=!1,slotProps:I={},slots:B={},...R}=o,L=Z(o),T=I?.select??C,_=T.native?"option":O;(d===A.A||"td"===d)&&(n=c||1e3);let E=(0,N.A)(T.id),D=(0,N.A)(T.labelId),W={slots:B,slotProps:I},[J,Q]=(0,$.A)("root",{ref:t,className:L.root,elementType:H,externalForwardedProps:{...W,component:d,...R},ownerState:o,additionalProps:{colSpan:n}}),[Y,ee]=(0,$.A)("toolbar",{className:L.toolbar,elementType:q,externalForwardedProps:W,ownerState:o}),[et,en]=(0,$.A)("spacer",{className:L.spacer,elementType:V,externalForwardedProps:W,ownerState:o}),[es,er]=(0,$.A)("selectLabel",{className:L.selectLabel,elementType:G,externalForwardedProps:W,ownerState:o,additionalProps:{id:D}}),[ea,eo]=(0,$.A)("select",{className:L.select,elementType:F,externalForwardedProps:W,ownerState:o}),[el,ei]=(0,$.A)("menuItem",{className:L.menuItem,elementType:_,externalForwardedProps:W,ownerState:o}),[ec,ed]=(0,$.A)("displayedRows",{className:L.displayedRows,elementType:K,externalForwardedProps:W,ownerState:o});return(0,r.jsx)(J,{...Q,children:(0,r.jsxs)(Y,{...ee,children:[(0,r.jsx)(et,{...en}),w.length>1&&(0,r.jsx)(es,{...er,children:m}),w.length>1&&(0,r.jsx)(ea,{variant:"standard",...!T.variant&&{input:s||(s=(0,r.jsx)(M.Ay,{}))},value:y,onChange:v,id:E,labelId:D,...T,classes:{...T.classes,root:(0,g.A)(L.input,L.selectRoot,(T.classes||{}).root),select:(0,g.A)(L.select,(T.classes||{}).select),icon:(0,g.A)(L.selectIcon,(T.classes||{}).icon)},disabled:p,...eo,children:w.map(e=>(0,a.createElement)(el,{...ei,key:e.label?e.label:e,value:e.value?e.value:e},e.label?e.label:e))}),(0,r.jsx)(ec,{...ed,children:x({from:0===u?0:f*y+1,to:-1===u?(f+1)*y:-1===y?u:Math.min(u,(f+1)*y),count:-1===u?-1:u,page:f})}),(0,r.jsx)(l,{className:L.actions,backIconButtonProps:i,count:u,nextIconButtonProps:j,onPageChange:b,page:f,rowsPerPage:y,showFirstButton:S,showLastButton:k,slotProps:I.actions,slots:B.actions,getItemAriaLabel:h,disabled:p})]})})});var Q=n(12362),Y=n(24296),ee=n(90764),et=n(11830),en=n(27674),es=n(91176),er=n(3297),ea=n(41896),eo=n(23428);let el=(0,eo.A)((0,r.jsx)("path",{d:"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9m-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8z"}),"History");var ei=n(46380),ec=n(43323),ed=n(19257),eu=n(91270);let ep=(0,eo.A)((0,r.jsx)("path",{d:"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02z"}),"Phone");var eh=n(28840),ex=n(54534);function em(){let{members:e,addMember:t,updateMember:n,deleteMember:s,fetchMembers:g,authUser:f}=(0,eh.A)(),[y,w]=(0,a.useState)(""),[P,M]=(0,a.useState)(!1),[C,S]=(0,a.useState)(!1),[k,I]=(0,a.useState)({name:"",email:"",phone:""}),[B,R]=(0,a.useState)(!1),[L,T]=(0,a.useState)(null),[z,N]=(0,a.useState)(0),[_,E]=(0,a.useState)(10),[D,W]=(0,a.useState)(!1),[$,H]=(0,a.useState)(null),[q,V]=(0,a.useState)(!1),[G,F]=(0,a.useState)(null),O=(Array.isArray(e)?e:[]).filter(e=>(e.name??"").toLowerCase().includes(y.toLowerCase())||(e.email??"").toLowerCase().includes(y.toLowerCase())||(e.phone??"").includes(y)),K=(e=!1,t)=>{e&&t?(I({id:t.id,name:t.name,email:t.email,phone:t.phone}),S(!0)):(I({name:"",email:"",phone:""}),S(!1)),M(!0),F(null)},U=()=>{M(!1),F(null)},X=e=>{let{name:t,value:n}=e.target;I(e=>({...e,[t]:n}))},Z=async()=>{V(!0),F(null);try{C?k.id&&C&&await n(k.id,{name:k.name,email:k.email,phone:k.phone}):await t({name:k.name,email:k.email,phone:k.phone}),U()}catch(e){e instanceof Error?F(e.message):F("An unexpected error occurred.")}finally{V(!1)}},eo=e=>{T(e),R(!0)},em=e=>{H(e),W(!0)},eA=()=>{W(!1),H(null)};return(0,r.jsxs)(o.A,{sx:{flexGrow:1},children:[(0,r.jsxs)(o.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,r.jsx)(l.A,{variant:"h4",children:"Members"}),(0,r.jsx)(i.A,{variant:"contained",startIcon:(0,r.jsx)(er.A,{}),onClick:()=>K(),children:"Add Member"})]}),(0,r.jsx)(c.A,{sx:{p:2,mb:3},children:(0,r.jsx)(d.A,{fullWidth:!0,placeholder:"Search members by name, email, or phone...",value:y,onChange:e=>w(e.target.value),InputProps:{startAdornment:(0,r.jsx)(u.A,{position:"start",children:(0,r.jsx)(ea.A,{})})}})}),(0,r.jsxs)(c.A,{sx:{width:"100%",mb:2},children:[(0,r.jsx)(p.A,{children:(0,r.jsxs)(h.A,{sx:{minWidth:650},"aria-label":"members table",children:[(0,r.jsx)(x.A,{children:(0,r.jsxs)(m.A,{children:[(0,r.jsx)(A.A,{children:"Name"}),(0,r.jsx)(A.A,{children:"Email"}),(0,r.jsx)(A.A,{children:"Phone"}),(0,r.jsx)(A.A,{children:"Visits"}),(0,r.jsx)(A.A,{children:"Total Spent"}),(0,r.jsx)(A.A,{align:"right",children:"Actions"})]})}),(0,r.jsxs)(j.A,{children:[O.slice(z*_,z*_+_).map(e=>(0,r.jsxs)(m.A,{children:[(0,r.jsx)(A.A,{component:"th",scope:"row",children:(0,r.jsxs)(o.A,{sx:{display:"flex",alignItems:"center"},children:[(0,r.jsx)(b.A,{sx:{mr:2,bgcolor:"primary.main"},children:e.name&&e.name.length>0?e.name.charAt(0):"?"}),e.name]})}),(0,r.jsx)(A.A,{children:e.email}),(0,r.jsx)(A.A,{children:e.phone}),(0,r.jsx)(A.A,{children:e.visits}),(0,r.jsxs)(A.A,{children:["$",Number(e.totalSpent).toFixed(2)]}),(0,r.jsxs)(A.A,{align:"right",children:[(0,r.jsx)(v.A,{size:"small",onClick:()=>em(e),title:"View History",children:(0,r.jsx)(el,{})}),(0,r.jsx)(v.A,{size:"small",onClick:()=>K(!0,e),title:"Edit Member",children:(0,r.jsx)(ei.A,{})}),(0,r.jsx)(v.A,{size:"small",color:"error",onClick:()=>eo(e.id),title:"Delete Member",children:(0,r.jsx)(ec.A,{})})]})]},e.id)),0===O.length&&(0,r.jsx)(m.A,{children:(0,r.jsx)(A.A,{colSpan:6,align:"center",children:(0,r.jsx)(l.A,{variant:"body1",sx:{py:2},children:"No members found"})})})]})]})}),(0,r.jsx)(J,{rowsPerPageOptions:[5,10,25],component:"div",count:O.length,rowsPerPage:_,page:z,onPageChange:(e,t)=>{N(t)},onRowsPerPageChange:e=>{E(parseInt(e.target.value,10)),N(0)}})]}),(0,r.jsxs)(Q.A,{open:P,onClose:U,maxWidth:"sm",fullWidth:!0,children:[(0,r.jsx)(Y.A,{children:C?"Edit Member":"Add New Member"}),(0,r.jsx)(ee.A,{children:(0,r.jsxs)(et.A,{spacing:3,sx:{mt:1},children:[G&&(0,r.jsx)(l.A,{color:"error",textAlign:"center",children:G}),(0,r.jsx)(d.A,{autoFocus:!0,label:"Full Name",name:"name",value:k.name,onChange:X,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,r.jsx)(u.A,{position:"start",children:(0,r.jsx)(ed.A,{})})}}),(0,r.jsx)(d.A,{label:"Email Address",name:"email",type:"email",value:k.email,onChange:X,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,r.jsx)(u.A,{position:"start",children:(0,r.jsx)(eu.A,{})})}}),(0,r.jsx)(d.A,{label:"Phone Number",name:"phone",value:k.phone,onChange:X,fullWidth:!0,required:!0,InputProps:{startAdornment:(0,r.jsx)(u.A,{position:"start",children:(0,r.jsx)(ep,{})})}})]})}),(0,r.jsxs)(en.A,{children:[(0,r.jsx)(i.A,{onClick:U,children:"Cancel"}),(0,r.jsx)(i.A,{onClick:Z,variant:"contained",disabled:!k.name||!k.email||!k.phone||q,children:q?C?"Updating...":"Adding...":C?"Update":"Add"})]})]}),(0,r.jsx)(ex.A,{open:B,title:"Confirm Delete",message:"Are you sure you want to delete this member? This action cannot be undone.",onConfirm:()=>{null!==L&&s(L),R(!1),T(null)},onCancel:()=>{R(!1),T(null)},confirmText:"Delete",confirmButtonColor:"error"}),(0,r.jsxs)(Q.A,{open:D,onClose:eA,maxWidth:"md",fullWidth:!0,children:[(0,r.jsxs)(Y.A,{children:["Member History",$&&(0,r.jsx)(l.A,{variant:"subtitle1",color:"text.secondary",children:$.name})]}),(0,r.jsx)(ee.A,{dividers:!0,children:$&&(0,r.jsxs)(o.A,{children:[(0,r.jsxs)(o.A,{sx:{mb:3},children:[(0,r.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Member Details"}),(0,r.jsxs)(o.A,{sx:{display:"flex",flexWrap:"wrap",gap:2},children:[(0,r.jsxs)(o.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:[(0,r.jsx)(l.A,{variant:"body2",color:"text.secondary",children:"Email"}),(0,r.jsx)(l.A,{variant:"body1",children:$.email})]}),(0,r.jsxs)(o.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:[(0,r.jsx)(l.A,{variant:"body2",color:"text.secondary",children:"Phone"}),(0,r.jsx)(l.A,{variant:"body1",children:$.phone})]}),(0,r.jsxs)(o.A,{sx:{flexBasis:{xs:"100%",sm:"30%"}},children:[(0,r.jsx)(l.A,{variant:"body2",color:"text.secondary",children:"Total Visits"}),(0,r.jsx)(l.A,{variant:"body1",children:$.visits})]})]})]}),(0,r.jsx)(es.A,{sx:{my:2}}),(0,r.jsx)(l.A,{variant:"h6",gutterBottom:!0,children:"Purchase History"}),(0,r.jsxs)(l.A,{variant:"body1",color:"text.secondary",sx:{mb:2},children:["Total Spent: $",Number($.totalSpent).toFixed(2)]}),(0,r.jsx)(c.A,{variant:"outlined",sx:{p:2,bgcolor:"background.default"},children:(0,r.jsx)(l.A,{variant:"body2",color:"text.secondary",align:"center",children:"Transaction history will be displayed here when connected to a backend."})})]})}),(0,r.jsx)(en.A,{children:(0,r.jsx)(i.A,{onClick:eA,children:"Close"})})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24296:(e,t,n)=>{"use strict";n.d(t,{A:()=>x});var s=n(43210),r=n(49384),a=n(99282),o=n(87088),l=n(13555),i=n(84754),c=n(79222),d=n(44791),u=n(60687);let p=e=>{let{classes:t}=e;return(0,a.A)({root:["root"]},c.t,t)},h=(0,l.Ay)(o.A,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),x=s.forwardRef(function(e,t){let n=(0,i.b)({props:e,name:"MuiDialogTitle"}),{className:a,id:o,...l}=n,c=p(n),{titleId:x=o}=s.useContext(d.A);return(0,u.jsx)(h,{component:"h2",className:(0,r.A)(c.root,a),ownerState:n,ref:t,variant:"h6",id:o??x,...l})})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30051:(e,t,n)=>{"use strict";n.d(t,{A:()=>a}),n(43210);var s=n(23428),r=n(60687);let a=(0,s.A)((0,r.jsx)("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}),"KeyboardArrowRight")},33873:e=>{"use strict";e.exports=require("path")},41896:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var s=n(23428),r=n(60687);let a=(0,s.A)((0,r.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14"}),"Search")},42472:(e,t,n)=>{Promise.resolve().then(n.bind(n,75792))},43323:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var s=n(23428),r=n(60687);let a=(0,s.A)((0,r.jsx)("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"}),"Delete")},46380:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var s=n(23428),r=n(60687);let a=(0,s.A)((0,r.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit")},52436:(e,t,n)=>{"use strict";n.d(t,{A:()=>a}),n(43210);var s=n(23428),r=n(60687);let a=(0,s.A)((0,r.jsx)("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"}),"KeyboardArrowLeft")},53276:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=n(65239),r=n(48088),a=n(88170),o=n.n(a),l=n(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);n.d(t,i);let c={children:["",{children:["members",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,75792)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\members\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\yunsell\\evospace\\evospace-pos\\src\\app\\members\\page.tsx"],u={require:n,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/members/page",pathname:"/members",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},54534:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var s=n(60687);n(43210);var r=n(12362),a=n(24296),o=n(90764),l=n(87088),i=n(27674),c=n(16184);let d=({open:e,title:t,message:n,onConfirm:d,onCancel:u,infoMode:p=!1,confirmText:h,confirmButtonColor:x="primary"})=>(0,s.jsxs)(r.A,{open:e,onClose:u,children:[(0,s.jsx)(a.A,{children:t}),(0,s.jsx)(o.A,{children:(0,s.jsx)(l.A,{children:n})}),(0,s.jsxs)(i.A,{children:[!p&&(0,s.jsx)(c.A,{onClick:u,children:"Cancel"}),(0,s.jsx)(c.A,{onClick:d,variant:"contained",color:p?"primary":x,children:p?h||"OK":h||"Confirm"})]})]})},56676:(e,t,n)=>{"use strict";n.d(t,{A:()=>a}),n(43210);var s=n(23428),r=n(60687);let a=(0,s.A)((0,r.jsx)("path",{d:"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"}),"LastPage")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});var s=n(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},75792:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s});let s=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\yunsell\\\\evospace\\\\evospace-pos\\\\src\\\\app\\\\members\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\members\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},91270:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var s=n(23428),r=n(60687);let a=(0,s.A)((0,r.jsx)("path",{d:"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"}),"Email")}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),s=t.X(0,[447,991,619,111,117,154,438,79],()=>n(53276));module.exports=s})();