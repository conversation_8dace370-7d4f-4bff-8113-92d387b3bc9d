"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[216],{9049:(e,t,n)=>{n.d(t,{A:()=>eE});var r=n(32299),o=n(12115),i=n(14810),a=n(43430),s=n(81616);function l(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function c(e){var t=l(e).Element;return e instanceof t||e instanceof Element}function f(e){var t=l(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function p(e){if("undefined"==typeof ShadowRoot)return!1;var t=l(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var d=Math.max,u=Math.min,m=Math.round;function h(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function v(){return!/^((?!chrome|android).)*safari/i.test(h())}function g(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,i=1;t&&f(e)&&(o=e.offsetWidth>0&&m(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&m(r.height)/e.offsetHeight||1);var a=(c(e)?l(e):window).visualViewport,s=!v()&&n,p=(r.left+(s&&a?a.offsetLeft:0))/o,d=(r.top+(s&&a?a.offsetTop:0))/i,u=r.width/o,h=r.height/i;return{width:u,height:h,top:d,right:p+u,bottom:d+h,left:p,x:p,y:d}}function y(e){var t=l(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function b(e){return e?(e.nodeName||"").toLowerCase():null}function x(e){return((c(e)?e.ownerDocument:e.document)||window.document).documentElement}function w(e){return g(x(e)).left+y(e).scrollLeft}function A(e){return l(e).getComputedStyle(e)}function O(e){var t=A(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function j(e){var t=g(e),n=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-n)&&(n=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function E(e){return"html"===b(e)?e:e.assignedSlot||e.parentNode||(p(e)?e.host:null)||x(e)}function M(e,t){void 0===t&&(t=[]);var n,r=function e(t){return["html","body","#document"].indexOf(b(t))>=0?t.ownerDocument.body:f(t)&&O(t)?t:e(E(t))}(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),i=l(r),a=o?[i].concat(i.visualViewport||[],O(r)?r:[]):r,s=t.concat(a);return o?s:s.concat(M(E(a)))}function P(e){return f(e)&&"fixed"!==A(e).position?e.offsetParent:null}function C(e){for(var t=l(e),n=P(e);n&&["table","td","th"].indexOf(b(n))>=0&&"static"===A(n).position;)n=P(n);return n&&("html"===b(n)||"body"===b(n)&&"static"===A(n).position)?t:n||function(e){var t=/firefox/i.test(h());if(/Trident/i.test(h())&&f(e)&&"fixed"===A(e).position)return null;var n=E(e);for(p(n)&&(n=n.host);f(n)&&0>["html","body"].indexOf(b(n));){var r=A(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var S="bottom",L="right",k="left",R="auto",D=["top",S,L,k],W="start",T="viewport",B="popper",H=D.reduce(function(e,t){return e.concat([t+"-"+W,t+"-end"])},[]),I=[].concat(D,[R]).reduce(function(e,t){return e.concat([t,t+"-"+W,t+"-end"])},[]),z=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"],N={placement:"bottom",modifiers:[],strategy:"absolute"};function V(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var q={passive:!0};function F(e){return e.split("-")[0]}function _(e){return e.split("-")[1]}function U(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function X(e){var t,n=e.reference,r=e.element,o=e.placement,i=o?F(o):null,a=o?_(o):null,s=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(i){case"top":t={x:s,y:n.y-r.height};break;case S:t={x:s,y:n.y+n.height};break;case L:t={x:n.x+n.width,y:l};break;case k:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var c=i?U(i):null;if(null!=c){var f="y"===c?"height":"width";switch(a){case W:t[c]=t[c]-(n[f]/2-r[f]/2);break;case"end":t[c]=t[c]+(n[f]/2-r[f]/2)}}return t}var Y={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Z(e){var t,n,r,o,i,a,s,c=e.popper,f=e.popperRect,p=e.placement,d=e.variation,u=e.offsets,h=e.position,v=e.gpuAcceleration,g=e.adaptive,y=e.roundOffsets,b=e.isFixed,w=u.x,O=void 0===w?0:w,j=u.y,E=void 0===j?0:j,M="function"==typeof y?y({x:O,y:E}):{x:O,y:E};O=M.x,E=M.y;var P=u.hasOwnProperty("x"),R=u.hasOwnProperty("y"),D=k,W="top",T=window;if(g){var B=C(c),H="clientHeight",I="clientWidth";B===l(c)&&"static"!==A(B=x(c)).position&&"absolute"===h&&(H="scrollHeight",I="scrollWidth"),("top"===p||(p===k||p===L)&&"end"===d)&&(W=S,E-=(b&&B===T&&T.visualViewport?T.visualViewport.height:B[H])-f.height,E*=v?1:-1),(p===k||("top"===p||p===S)&&"end"===d)&&(D=L,O-=(b&&B===T&&T.visualViewport?T.visualViewport.width:B[I])-f.width,O*=v?1:-1)}var z=Object.assign({position:h},g&&Y),N=!0===y?(t={x:O,y:E},n=l(c),r=t.x,o=t.y,{x:m(r*(i=n.devicePixelRatio||1))/i||0,y:m(o*i)/i||0}):{x:O,y:E};return(O=N.x,E=N.y,v)?Object.assign({},z,((s={})[W]=R?"0":"",s[D]=P?"0":"",s.transform=1>=(T.devicePixelRatio||1)?"translate("+O+"px, "+E+"px)":"translate3d("+O+"px, "+E+"px, 0)",s)):Object.assign({},z,((a={})[W]=R?E+"px":"",a[D]=P?O+"px":"",a.transform="",a))}var $={left:"right",right:"left",bottom:"top",top:"bottom"};function G(e){return e.replace(/left|right|bottom|top/g,function(e){return $[e]})}var J={start:"end",end:"start"};function K(e){return e.replace(/start|end/g,function(e){return J[e]})}function Q(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&p(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function ee(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function et(e,t,n){var r,o,i,a,s,f,p,u,m,h;return t===T?ee(function(e,t){var n=l(e),r=x(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,s=0,c=0;if(o){i=o.width,a=o.height;var f=v();(f||!f&&"fixed"===t)&&(s=o.offsetLeft,c=o.offsetTop)}return{width:i,height:a,x:s+w(e),y:c}}(e,n)):c(t)?((r=g(t,!1,"fixed"===n)).top=r.top+t.clientTop,r.left=r.left+t.clientLeft,r.bottom=r.top+t.clientHeight,r.right=r.left+t.clientWidth,r.width=t.clientWidth,r.height=t.clientHeight,r.x=r.left,r.y=r.top,r):ee((o=x(e),a=x(o),s=y(o),f=null==(i=o.ownerDocument)?void 0:i.body,p=d(a.scrollWidth,a.clientWidth,f?f.scrollWidth:0,f?f.clientWidth:0),u=d(a.scrollHeight,a.clientHeight,f?f.scrollHeight:0,f?f.clientHeight:0),m=-s.scrollLeft+w(o),h=-s.scrollTop,"rtl"===A(f||a).direction&&(m+=d(a.clientWidth,f?f.clientWidth:0)-p),{width:p,height:u,x:m,y:h}))}function en(){return{top:0,right:0,bottom:0,left:0}}function er(e){return Object.assign({},en(),e)}function eo(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}function ei(e,t){void 0===t&&(t={});var n,r,o,i,a,s,l,p,m=t,h=m.placement,v=void 0===h?e.placement:h,y=m.strategy,w=void 0===y?e.strategy:y,O=m.boundary,j=m.rootBoundary,P=m.elementContext,k=void 0===P?B:P,R=m.altBoundary,W=m.padding,H=void 0===W?0:W,I=er("number"!=typeof H?H:eo(H,D)),z=e.rects.popper,N=e.elements[void 0!==R&&R?k===B?"reference":B:k],V=(n=c(N)?N:N.contextElement||x(e.elements.popper),r=void 0===O?"clippingParents":O,o=void 0===j?T:j,l=(s=[].concat("clippingParents"===r?(i=M(E(n)),!c(a=["absolute","fixed"].indexOf(A(n).position)>=0&&f(n)?C(n):n)?[]:i.filter(function(e){return c(e)&&Q(e,a)&&"body"!==b(e)})):[].concat(r),[o]))[0],(p=s.reduce(function(e,t){var r=et(n,t,w);return e.top=d(r.top,e.top),e.right=u(r.right,e.right),e.bottom=u(r.bottom,e.bottom),e.left=d(r.left,e.left),e},et(n,l,w))).width=p.right-p.left,p.height=p.bottom-p.top,p.x=p.left,p.y=p.top,p),q=g(e.elements.reference),F=X({reference:q,element:z,strategy:"absolute",placement:v}),_=ee(Object.assign({},z,F)),U=k===B?_:q,Y={top:V.top-U.top+I.top,bottom:U.bottom-V.bottom+I.bottom,left:V.left-U.left+I.left,right:U.right-V.right+I.right},Z=e.modifiersData.offset;if(k===B&&Z){var $=Z[v];Object.keys(Y).forEach(function(e){var t=[L,S].indexOf(e)>=0?1:-1,n=["top",S].indexOf(e)>=0?"y":"x";Y[e]+=$[n]*t})}return Y}function ea(e,t,n){return d(e,u(t,n))}function es(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function el(e){return["top",L,S,k].some(function(t){return e[t]>=0})}var ec=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,i=void 0===o?N:o;return function(e,t,n){void 0===n&&(n=i);var o,a,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},N,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},p=[],d=!1,u={state:s,setOptions:function(n){var o,a,l,f,d,m,v="function"==typeof n?n(s.options):n;h(),s.options=Object.assign({},i,s.options,v),s.scrollParents={reference:c(e)?M(e):e.contextElement?M(e.contextElement):[],popper:M(t)};var g=(a=Object.keys(o=[].concat(r,s.options.modifiers).reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{})).map(function(e){return o[e]}),l=new Map,f=new Set,d=[],a.forEach(function(e){l.set(e.name,e)}),a.forEach(function(e){f.has(e.name)||function e(t){f.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!f.has(t)){var n=l.get(t);n&&e(n)}}),d.push(t)}(e)}),m=d,z.reduce(function(e,t){return e.concat(m.filter(function(e){return e.phase===t}))},[]));return s.orderedModifiers=g.filter(function(e){return e.enabled}),s.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=e.effect;if("function"==typeof r){var o=r({state:s,name:t,instance:u,options:void 0===n?{}:n});p.push(o||function(){})}}),u.update()},forceUpdate:function(){if(!d){var e=s.elements,t=e.reference,n=e.popper;if(V(t,n)){s.rects={reference:(r=C(n),o="fixed"===s.options.strategy,i=f(r),h=f(r)&&(c=m((a=r.getBoundingClientRect()).width)/r.offsetWidth||1,p=m(a.height)/r.offsetHeight||1,1!==c||1!==p),v=x(r),A=g(t,h,o),E={scrollLeft:0,scrollTop:0},M={x:0,y:0},(i||!i&&!o)&&(("body"!==b(r)||O(v))&&(E=function(e){return e!==l(e)&&f(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:y(e)}(r)),f(r)?(M=g(r,!0),M.x+=r.clientLeft,M.y+=r.clientTop):v&&(M.x=w(v))),{x:A.left+E.scrollLeft-M.x,y:A.top+E.scrollTop-M.y,width:A.width,height:A.height}),popper:j(n)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach(function(e){return s.modifiersData[e.name]=Object.assign({},e.data)});for(var r,o,i,a,c,p,h,v,A,E,M,P=0;P<s.orderedModifiers.length;P++){if(!0===s.reset){s.reset=!1,P=-1;continue}var S=s.orderedModifiers[P],L=S.fn,k=S.options,R=void 0===k?{}:k,D=S.name;"function"==typeof L&&(s=L({state:s,options:R,name:D,instance:u})||s)}}}},update:(o=function(){return new Promise(function(e){u.forceUpdate(),e(s)})},function(){return a||(a=new Promise(function(e){Promise.resolve().then(function(){a=void 0,e(o())})})),a}),destroy:function(){h(),d=!0}};if(!V(e,t))return u;function h(){p.forEach(function(e){return e()}),p=[]}return u.setOptions(n).then(function(e){!d&&n.onFirstUpdate&&n.onFirstUpdate(e)}),u}}({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,i=void 0===o||o,a=r.resize,s=void 0===a||a,c=l(t.elements.popper),f=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&f.forEach(function(e){e.addEventListener("scroll",n.update,q)}),s&&c.addEventListener("resize",n.update,q),function(){i&&f.forEach(function(e){e.removeEventListener("scroll",n.update,q)}),s&&c.removeEventListener("resize",n.update,q)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=X({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=n.adaptive,i=n.roundOffsets,a=void 0===i||i,s={placement:F(t.placement),variation:_(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Z(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===o||o,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Z(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];f(o)&&b(o)&&(Object.assign(o.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});f(r)&&b(r)&&(Object.assign(r.style,i),Object.keys(o).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=void 0===o?[0,0]:o,a=I.reduce(function(e,n){var r,o,a,s,l,c;return e[n]=(r=t.rects,a=[k,"top"].indexOf(o=F(n))>=0?-1:1,l=(s="function"==typeof i?i(Object.assign({},r,{placement:n})):i)[0],c=s[1],l=l||0,c=(c||0)*a,[k,L].indexOf(o)>=0?{x:c,y:l}:{x:l,y:c}),e},{}),s=a[t.placement],l=s.x,c=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=a}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0===a||a,l=n.fallbackPlacements,c=n.padding,f=n.boundary,p=n.rootBoundary,d=n.altBoundary,u=n.flipVariations,m=void 0===u||u,h=n.allowedAutoPlacements,v=t.options.placement,g=F(v)===v,y=l||(g||!m?[G(v)]:function(e){if(F(e)===R)return[];var t=G(e);return[K(e),t,K(t)]}(v)),b=[v].concat(y).reduce(function(e,n){var r,o,i,a,s,l,d,u,v,g,y,b;return e.concat(F(n)===R?(o=(r={placement:n,boundary:f,rootBoundary:p,padding:c,flipVariations:m,allowedAutoPlacements:h}).placement,i=r.boundary,a=r.rootBoundary,s=r.padding,l=r.flipVariations,u=void 0===(d=r.allowedAutoPlacements)?I:d,0===(y=(g=(v=_(o))?l?H:H.filter(function(e){return _(e)===v}):D).filter(function(e){return u.indexOf(e)>=0})).length&&(y=g),Object.keys(b=y.reduce(function(e,n){return e[n]=ei(t,{placement:n,boundary:i,rootBoundary:a,padding:s})[F(n)],e},{})).sort(function(e,t){return b[e]-b[t]})):n)},[]),x=t.rects.reference,w=t.rects.popper,A=new Map,O=!0,j=b[0],E=0;E<b.length;E++){var M=b[E],P=F(M),C=_(M)===W,T=["top",S].indexOf(P)>=0,B=T?"width":"height",z=ei(t,{placement:M,boundary:f,rootBoundary:p,altBoundary:d,padding:c}),N=T?C?L:k:C?S:"top";x[B]>w[B]&&(N=G(N));var V=G(N),q=[];if(i&&q.push(z[P]<=0),s&&q.push(z[N]<=0,z[V]<=0),q.every(function(e){return e})){j=M,O=!1;break}A.set(M,q)}if(O)for(var U=m?3:1,X=function(e){var t=b.find(function(t){var n=A.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return j=t,"break"},Y=U;Y>0&&"break"!==X(Y);Y--);t.placement!==j&&(t.modifiersData[r]._skip=!0,t.placement=j,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,i=n.altAxis,a=n.boundary,s=n.rootBoundary,l=n.altBoundary,c=n.padding,f=n.tether,p=void 0===f||f,m=n.tetherOffset,h=void 0===m?0:m,v=ei(t,{boundary:a,rootBoundary:s,padding:c,altBoundary:l}),g=F(t.placement),y=_(t.placement),b=!y,x=U(g),w="x"===x?"y":"x",A=t.modifiersData.popperOffsets,O=t.rects.reference,E=t.rects.popper,M="function"==typeof h?h(Object.assign({},t.rects,{placement:t.placement})):h,P="number"==typeof M?{mainAxis:M,altAxis:M}:Object.assign({mainAxis:0,altAxis:0},M),R=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,D={x:0,y:0};if(A){if(void 0===o||o){var T,B="y"===x?"top":k,H="y"===x?S:L,I="y"===x?"height":"width",z=A[x],N=z+v[B],V=z-v[H],q=p?-E[I]/2:0,X=y===W?O[I]:E[I],Y=y===W?-E[I]:-O[I],Z=t.elements.arrow,$=p&&Z?j(Z):{width:0,height:0},G=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:en(),J=G[B],K=G[H],Q=ea(0,O[I],$[I]),ee=b?O[I]/2-q-Q-J-P.mainAxis:X-Q-J-P.mainAxis,et=b?-O[I]/2+q+Q+K+P.mainAxis:Y+Q+K+P.mainAxis,er=t.elements.arrow&&C(t.elements.arrow),eo=er?"y"===x?er.clientTop||0:er.clientLeft||0:0,es=null!=(T=null==R?void 0:R[x])?T:0,el=ea(p?u(N,z+ee-es-eo):N,z,p?d(V,z+et-es):V);A[x]=el,D[x]=el-z}if(void 0!==i&&i){var ec,ef,ep="x"===x?"top":k,ed="x"===x?S:L,eu=A[w],em="y"===w?"height":"width",eh=eu+v[ep],ev=eu-v[ed],eg=-1!==["top",k].indexOf(g),ey=null!=(ef=null==R?void 0:R[w])?ef:0,eb=eg?eh:eu-O[em]-E[em]-ey+P.altAxis,ex=eg?eu+O[em]+E[em]-ey-P.altAxis:ev,ew=p&&eg?(ec=ea(eb,eu,ex))>ex?ex:ec:ea(p?eb:eh,eu,p?ex:ev);A[w]=ew,D[w]=ew-eu}t.modifiersData[r]=D}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=F(n.placement),l=U(s),c=[k,L].indexOf(s)>=0?"height":"width";if(i&&a){var f,p=(f=o.padding,er("number"!=typeof(f="function"==typeof f?f(Object.assign({},n.rects,{placement:n.placement})):f)?f:eo(f,D))),d=j(i),u="y"===l?"top":k,m="y"===l?S:L,h=n.rects.reference[c]+n.rects.reference[l]-a[l]-n.rects.popper[c],v=a[l]-n.rects.reference[l],g=C(i),y=g?"y"===l?g.clientHeight||0:g.clientWidth||0:0,b=p[u],x=y-d[c]-p[m],w=y/2-d[c]/2+(h/2-v/2),A=ea(b,w,x);n.modifiersData[r]=((t={})[l]=A,t.centerOffset=A-w,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;if(null!=r)("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&Q(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=ei(t,{elementContext:"reference"}),s=ei(t,{altBoundary:!0}),l=es(a,r),c=es(s,o,i),f=el(l),p=el(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:f,hasPopperEscaped:p},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":p})}}]}),ef=n(17472),ep=n(50422),ed=n(59773),eu=n(55170),em=n(90870);function eh(e){return(0,em.Ay)("MuiPopper",e)}(0,eu.A)("MuiPopper",["root"]);var ev=n(95155);function eg(e){return"function"==typeof e?e():e}let ey=e=>{let{classes:t}=e;return(0,ef.A)({root:["root"]},eh,t)},eb={},ex=o.forwardRef(function(e,t){var n;let{anchorEl:r,children:i,direction:l,disablePortal:c,modifiers:f,open:p,placement:d,popperOptions:u,popperRef:m,slotProps:h={},slots:v={},TransitionProps:g,ownerState:y,...b}=e,x=o.useRef(null),w=(0,s.A)(x,t),A=o.useRef(null),O=(0,s.A)(A,m),j=o.useRef(O);(0,a.A)(()=>{j.current=O},[O]),o.useImperativeHandle(m,()=>A.current,[]);let E=function(e,t){if("ltr"===t)return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}(d,l),[M,P]=o.useState(E),[C,S]=o.useState(eg(r));o.useEffect(()=>{A.current&&A.current.forceUpdate()}),o.useEffect(()=>{r&&S(eg(r))},[r]),(0,a.A)(()=>{if(!C||!p)return;let e=e=>{P(e.placement)},t=[{name:"preventOverflow",options:{altBoundary:c}},{name:"flip",options:{altBoundary:c}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:t=>{let{state:n}=t;e(n)}}];null!=f&&(t=t.concat(f)),u&&null!=u.modifiers&&(t=t.concat(u.modifiers));let n=ec(C,x.current,{placement:E,...u,modifiers:t});return j.current(n),()=>{n.destroy(),j.current(null)}},[C,c,f,p,u,E]);let L={placement:M};null!==g&&(L.TransitionProps=g);let k=ey(e),R=null!=(n=v.root)?n:"div",D=(0,ep.A)({elementType:R,externalSlotProps:h.root,externalForwardedProps:b,additionalProps:{role:"tooltip",ref:w},ownerState:e,className:k.root});return(0,ev.jsx)(R,{...D,children:"function"==typeof i?i(L):i})}),ew=o.forwardRef(function(e,t){let n,{anchorEl:r,children:a,container:s,direction:l="ltr",disablePortal:c=!1,keepMounted:f=!1,modifiers:p,open:d,placement:u="bottom",popperOptions:m=eb,popperRef:h,style:v,transition:g=!1,slotProps:y={},slots:b={},...x}=e,[w,A]=o.useState(!0);if(!f&&!d&&(!g||w))return null;if(s)n=s;else if(r){let e=eg(r);n=e&&void 0!==e.nodeType?(0,i.A)(e).body:(0,i.A)(null).body}let O=!d&&f&&(!g||w)?"none":void 0,j=g?{in:d,onEnter:()=>{A(!1)},onExited:()=>{A(!0)}}:void 0;return(0,ev.jsx)(ed.A,{disablePortal:c,container:n,children:(0,ev.jsx)(ex,{anchorEl:r,direction:l,disablePortal:c,modifiers:p,ref:t,open:g?!w:d,placement:u,popperOptions:m,popperRef:h,slotProps:y,slots:b,...x,style:{position:"fixed",top:0,left:0,display:O,...v},TransitionProps:j,children:a})})});var eA=n(75955),eO=n(10186);let ej=(0,eA.Ay)(ew,{name:"MuiPopper",slot:"Root"})({}),eE=o.forwardRef(function(e,t){var n;let o=(0,r.I)(),{anchorEl:i,component:a,components:s,componentsProps:l,container:c,disablePortal:f,keepMounted:p,modifiers:d,open:u,placement:m,popperOptions:h,popperRef:v,transition:g,slots:y,slotProps:b,...x}=(0,eO.b)({props:e,name:"MuiPopper"}),w=null!=(n=null==y?void 0:y.root)?n:null==s?void 0:s.Root,A={anchorEl:i,container:c,disablePortal:f,keepMounted:p,modifiers:d,open:u,placement:m,popperOptions:h,popperRef:v,transition:g,...x};return(0,ev.jsx)(ej,{as:a,direction:o?"rtl":"ltr",slots:{root:w},slotProps:null!=b?b:l,...A,ref:t})})},92302:(e,t,n)=>{n.d(t,{A:()=>R});var r=n(12115),o=n(52596),i=n(17472),a=n(14391),s=n(75955),l=n(40680),c=n(10186),f=n(47798),p=n(13209),d=n(98963),u=n(18407),m=n(55170),h=n(90870);function v(e){return(0,h.Ay)("MuiAlert",e)}let g=(0,m.A)("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);var y=n(3127),b=n(57515),x=n(95155);let w=(0,b.A)((0,x.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),A=(0,b.A)((0,x.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),O=(0,b.A)((0,x.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),j=(0,b.A)((0,x.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),E=(0,b.A)((0,x.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close"),M=e=>{let{variant:t,color:n,severity:r,classes:o}=e,a={root:["root","color".concat((0,p.A)(n||r)),"".concat(t).concat((0,p.A)(n||r)),"".concat(t)],icon:["icon"],message:["message"],action:["action"]};return(0,i.A)(a,v,o)},P=(0,s.Ay)(u.A,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t[n.variant],t["".concat(n.variant).concat((0,p.A)(n.color||n.severity))]]}})((0,l.A)(e=>{let{theme:t}=e,n="light"===t.palette.mode?a.e$:a.a,r="light"===t.palette.mode?a.a:a.e$;return{...t.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(t.palette).filter((0,d.A)(["light"])).map(e=>{let[o]=e;return{props:{colorSeverity:o,variant:"standard"},style:{color:t.vars?t.vars.palette.Alert["".concat(o,"Color")]:n(t.palette[o].light,.6),backgroundColor:t.vars?t.vars.palette.Alert["".concat(o,"StandardBg")]:r(t.palette[o].light,.9),["& .".concat(g.icon)]:t.vars?{color:t.vars.palette.Alert["".concat(o,"IconColor")]}:{color:t.palette[o].main}}}}),...Object.entries(t.palette).filter((0,d.A)(["light"])).map(e=>{let[r]=e;return{props:{colorSeverity:r,variant:"outlined"},style:{color:t.vars?t.vars.palette.Alert["".concat(r,"Color")]:n(t.palette[r].light,.6),border:"1px solid ".concat((t.vars||t).palette[r].light),["& .".concat(g.icon)]:t.vars?{color:t.vars.palette.Alert["".concat(r,"IconColor")]}:{color:t.palette[r].main}}}}),...Object.entries(t.palette).filter((0,d.A)(["dark"])).map(e=>{let[n]=e;return{props:{colorSeverity:n,variant:"filled"},style:{fontWeight:t.typography.fontWeightMedium,...t.vars?{color:t.vars.palette.Alert["".concat(n,"FilledColor")],backgroundColor:t.vars.palette.Alert["".concat(n,"FilledBg")]}:{backgroundColor:"dark"===t.palette.mode?t.palette[n].dark:t.palette[n].main,color:t.palette.getContrastText(t.palette[n].main)}}}})]}})),C=(0,s.Ay)("div",{name:"MuiAlert",slot:"Icon"})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),S=(0,s.Ay)("div",{name:"MuiAlert",slot:"Message"})({padding:"8px 0",minWidth:0,overflow:"auto"}),L=(0,s.Ay)("div",{name:"MuiAlert",slot:"Action"})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),k={success:(0,x.jsx)(w,{fontSize:"inherit"}),warning:(0,x.jsx)(A,{fontSize:"inherit"}),error:(0,x.jsx)(O,{fontSize:"inherit"}),info:(0,x.jsx)(j,{fontSize:"inherit"})},R=r.forwardRef(function(e,t){let n=(0,c.b)({props:e,name:"MuiAlert"}),{action:r,children:i,className:a,closeText:s="Close",color:l,components:p={},componentsProps:d={},icon:u,iconMapping:m=k,onClose:h,role:v="alert",severity:g="success",slotProps:b={},slots:w={},variant:A="standard",...O}=n,j={...n,color:l,severity:g,variant:A,colorSeverity:l||g},R=M(j),D={slots:{closeButton:p.CloseButton,closeIcon:p.CloseIcon,...w},slotProps:{...d,...b}},[W,T]=(0,f.A)("root",{ref:t,shouldForwardComponentProp:!0,className:(0,o.A)(R.root,a),elementType:P,externalForwardedProps:{...D,...O},ownerState:j,additionalProps:{role:v,elevation:0}}),[B,H]=(0,f.A)("icon",{className:R.icon,elementType:C,externalForwardedProps:D,ownerState:j}),[I,z]=(0,f.A)("message",{className:R.message,elementType:S,externalForwardedProps:D,ownerState:j}),[N,V]=(0,f.A)("action",{className:R.action,elementType:L,externalForwardedProps:D,ownerState:j}),[q,F]=(0,f.A)("closeButton",{elementType:y.A,externalForwardedProps:D,ownerState:j}),[_,U]=(0,f.A)("closeIcon",{elementType:E,externalForwardedProps:D,ownerState:j});return(0,x.jsxs)(W,{...T,children:[!1!==u?(0,x.jsx)(B,{...H,children:u||m[g]||k[g]}):null,(0,x.jsx)(I,{...z,children:i}),null!=r?(0,x.jsx)(N,{...V,children:r}):null,null==r&&h?(0,x.jsx)(N,{...V,children:(0,x.jsx)(q,{size:"small","aria-label":s,title:s,color:"inherit",onClick:h,...F,children:(0,x.jsx)(_,{fontSize:"small",...U})})}):null]})})}}]);