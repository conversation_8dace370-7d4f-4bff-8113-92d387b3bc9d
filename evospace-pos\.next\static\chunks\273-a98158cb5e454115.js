"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[273],{72705:(e,t,o)=>{o.d(t,{A:()=>x});var r=o(12115),p=o(52596),n=o(72890),a=o(90870),l=o(17472);let i=(0,o(11772).Ay)();var s=o(25560),c=o(5300),u=o(85799),m=o(648),d=o(83130),g=o(95155);let f=(0,u.A)(),h=i("div",{name:"<PERSON><PERSON><PERSON><PERSON>ck",slot:"Root"});function y(e){return(0,s.A)({props:e,name:"<PERSON><PERSON><PERSON>tack",defaultTheme:f})}let v=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],b=e=>{let{ownerState:t,theme:o}=e,r={display:"flex",flexDirection:"column",...(0,m.NI)({theme:o},(0,m.kW)({values:t.direction,breakpoints:o.breakpoints.values}),e=>({flexDirection:e}))};if(t.spacing){let e=(0,d.LX)(o),p=Object.keys(o.breakpoints.values).reduce((e,o)=>(("object"==typeof t.spacing&&null!=t.spacing[o]||"object"==typeof t.direction&&null!=t.direction[o])&&(e[o]=!0),e),{}),a=(0,m.kW)({values:t.direction,base:p}),l=(0,m.kW)({values:t.spacing,base:p});"object"==typeof a&&Object.keys(a).forEach((e,t,o)=>{if(!a[e]){let r=t>0?a[o[t-1]]:"column";a[e]=r}}),r=(0,n.A)(r,(0,m.NI)({theme:o},l,(o,r)=>t.useFlexGap?{gap:(0,d._W)(e,o)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{["margin".concat(v(r?a[r]:t.direction))]:(0,d._W)(e,o)}}))}return(0,m.iZ)(o.breakpoints,r)};var A=o(75955),w=o(10186);let x=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{createStyledComponent:t=h,useThemeProps:o=y,componentName:n="MuiStack"}=e,i=()=>(0,l.A)({root:["root"]},e=>(0,a.Ay)(n,e),{}),s=t(b);return r.forwardRef(function(e,t){let n=o(e),{component:a="div",direction:l="column",spacing:u=0,divider:m,children:d,className:f,useFlexGap:h=!1,...y}=(0,c.A)(n),v=i();return(0,g.jsx)(s,{as:a,ownerState:{direction:l,spacing:u,useFlexGap:h},ref:t,className:(0,p.A)(v.root,f),...y,children:m?function(e,t){let o=r.Children.toArray(e).filter(Boolean);return o.reduce((e,p,n)=>(e.push(p),n<o.length-1&&e.push(r.cloneElement(t,{key:"separator-".concat(n)})),e),[])}(d,m):d})})}({createStyledComponent:(0,A.Ay)("div",{name:"MuiStack",slot:"Root"}),useThemeProps:e=>(0,w.b)({props:e,name:"MuiStack"})})},83729:(e,t,o)=>{o.d(t,{A:()=>I});var r=o(12115),p=o(52596),n=o(29905),a=o(17472),l=o(14391),i=o(32299),s=o(60848),c=o(45292),u=o(75955),m=o(16324),d=o(40680),g=o(10186),f=o(13209),h=o(18560),y=o(9049),v=o(37573),b=o(36863),A=o(74739),w=o(49800),x=o(47798),T=o(55170),R=o(90870);function k(e){return(0,R.Ay)("MuiTooltip",e)}let M=(0,T.A)("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]);var O=o(95155);let S=e=>{let{classes:t,disableInteractive:o,arrow:r,touch:p,placement:n}=e,l={popper:["popper",!o&&"popperInteractive",r&&"popperArrow"],tooltip:["tooltip",r&&"tooltipArrow",p&&"touch","tooltipPlacement".concat((0,f.A)(n.split("-")[0]))],arrow:["arrow"]};return(0,a.A)(l,k,t)},E=(0,u.Ay)(y.A,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{let{ownerState:o}=e;return[t.popper,!o.disableInteractive&&t.popperInteractive,o.arrow&&t.popperArrow,!o.open&&t.popperClose]}})((0,d.A)(e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.tooltip,pointerEvents:"none",variants:[{props:e=>{let{ownerState:t}=e;return!t.disableInteractive},style:{pointerEvents:"auto"}},{props:e=>{let{open:t}=e;return!t},style:{pointerEvents:"none"}},{props:e=>{let{ownerState:t}=e;return t.arrow},style:{['&[data-popper-placement*="bottom"] .'.concat(M.arrow)]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},['&[data-popper-placement*="top"] .'.concat(M.arrow)]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},['&[data-popper-placement*="right"] .'.concat(M.arrow)]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},['&[data-popper-placement*="left"] .'.concat(M.arrow)]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!t.isRtl},style:{['&[data-popper-placement*="right"] .'.concat(M.arrow)]:{left:0,marginLeft:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!!t.isRtl},style:{['&[data-popper-placement*="right"] .'.concat(M.arrow)]:{right:0,marginRight:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!t.isRtl},style:{['&[data-popper-placement*="left"] .'.concat(M.arrow)]:{right:0,marginRight:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!!t.isRtl},style:{['&[data-popper-placement*="left"] .'.concat(M.arrow)]:{left:0,marginLeft:"-0.71em"}}}]}})),W=(0,u.Ay)("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{let{ownerState:o}=e;return[t.tooltip,o.touch&&t.touch,o.arrow&&t.tooltipArrow,t["tooltipPlacement".concat((0,f.A)(o.placement.split("-")[0]))]]}})((0,d.A)(e=>{let{theme:t}=e;return{backgroundColor:t.vars?t.vars.palette.Tooltip.bg:(0,l.X4)(t.palette.grey[700],.92),borderRadius:(t.vars||t).shape.borderRadius,color:(t.vars||t).palette.common.white,fontFamily:t.typography.fontFamily,padding:"4px 8px",fontSize:t.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:t.typography.fontWeightMedium,[".".concat(M.popper,'[data-popper-placement*="left"] &')]:{transformOrigin:"right center"},[".".concat(M.popper,'[data-popper-placement*="right"] &')]:{transformOrigin:"left center"},[".".concat(M.popper,'[data-popper-placement*="top"] &')]:{transformOrigin:"center bottom",marginBottom:"14px"},[".".concat(M.popper,'[data-popper-placement*="bottom"] &')]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:e=>{let{ownerState:t}=e;return t.arrow},style:{position:"relative",margin:0}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{padding:"8px 16px",fontSize:t.typography.pxToRem(14),lineHeight:"".concat(Math.round(16/14*1e5)/1e5,"em"),fontWeight:t.typography.fontWeightRegular}},{props:e=>{let{ownerState:t}=e;return!t.isRtl},style:{[".".concat(M.popper,'[data-popper-placement*="left"] &')]:{marginRight:"14px"},[".".concat(M.popper,'[data-popper-placement*="right"] &')]:{marginLeft:"14px"}}},{props:e=>{let{ownerState:t}=e;return!t.isRtl&&t.touch},style:{[".".concat(M.popper,'[data-popper-placement*="left"] &')]:{marginRight:"24px"},[".".concat(M.popper,'[data-popper-placement*="right"] &')]:{marginLeft:"24px"}}},{props:e=>{let{ownerState:t}=e;return!!t.isRtl},style:{[".".concat(M.popper,'[data-popper-placement*="left"] &')]:{marginLeft:"14px"},[".".concat(M.popper,'[data-popper-placement*="right"] &')]:{marginRight:"14px"}}},{props:e=>{let{ownerState:t}=e;return!!t.isRtl&&t.touch},style:{[".".concat(M.popper,'[data-popper-placement*="left"] &')]:{marginLeft:"24px"},[".".concat(M.popper,'[data-popper-placement*="right"] &')]:{marginRight:"24px"}}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{[".".concat(M.popper,'[data-popper-placement*="top"] &')]:{marginBottom:"24px"}}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{[".".concat(M.popper,'[data-popper-placement*="bottom"] &')]:{marginTop:"24px"}}}]}})),L=(0,u.Ay)("span",{name:"MuiTooltip",slot:"Arrow"})((0,d.A)(e=>{let{theme:t}=e;return{overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:t.vars?t.vars.palette.Tooltip.bg:(0,l.X4)(t.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}}})),j=!1,C=new n.E,N={x:0,y:0};function P(e,t){return function(o){for(var r=arguments.length,p=Array(r>1?r-1:0),n=1;n<r;n++)p[n-1]=arguments[n];t&&t(o,...p),e(o,...p)}}let I=r.forwardRef(function(e,t){var o,a,l;let u=(0,g.b)({props:e,name:"MuiTooltip"}),{arrow:d=!1,children:f,classes:T,components:R={},componentsProps:k={},describeChild:M=!1,disableFocusListener:I=!1,disableHoverListener:B=!1,disableInteractive:F=!1,disableTouchListener:_=!1,enterDelay:z=100,enterNextDelay:U=0,enterTouchDelay:X=700,followCursor:D=!1,id:G,leaveDelay:H=0,leaveTouchDelay:V=1500,onClose:Y,onOpen:Z,open:q,placement:J="bottom",PopperComponent:K,PopperProps:Q={},slotProps:$={},slots:ee={},title:et,TransitionComponent:eo,TransitionProps:er,...ep}=u,en=r.isValidElement(f)?f:(0,O.jsx)("span",{children:f}),ea=(0,m.A)(),el=(0,i.I)(),[ei,es]=r.useState(),[ec,eu]=r.useState(null),em=r.useRef(!1),ed=F||D,eg=(0,n.A)(),ef=(0,n.A)(),eh=(0,n.A)(),ey=(0,n.A)(),[ev,eb]=(0,w.A)({controlled:q,default:!1,name:"Tooltip",state:"open"}),eA=ev,ew=(0,A.A)(G),ex=r.useRef(),eT=(0,v.A)(()=>{void 0!==ex.current&&(document.body.style.WebkitUserSelect=ex.current,ex.current=void 0),ey.clear()});r.useEffect(()=>eT,[eT]);let eR=e=>{C.clear(),j=!0,eb(!0),Z&&!eA&&Z(e)},ek=(0,v.A)(e=>{C.start(800+H,()=>{j=!1}),eb(!1),Y&&eA&&Y(e),eg.start(ea.transitions.duration.shortest,()=>{em.current=!1})}),eM=e=>{em.current&&"touchstart"!==e.type||(ei&&ei.removeAttribute("title"),ef.clear(),eh.clear(),z||j&&U?ef.start(j?U:z,()=>{eR(e)}):eR(e))},eO=e=>{ef.clear(),eh.start(H,()=>{ek(e)})},[,eS]=r.useState(!1),eE=e=>{(0,s.A)(e.target)||(eS(!1),eO(e))},eW=e=>{ei||es(e.currentTarget),(0,s.A)(e.target)&&(eS(!0),eM(e))},eL=e=>{em.current=!0;let t=en.props;t.onTouchStart&&t.onTouchStart(e)};r.useEffect(()=>{if(eA)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){"Escape"===e.key&&ek(e)}},[ek,eA]);let ej=(0,b.A)((0,c.A)(en),es,t);et||0===et||(eA=!1);let eC=r.useRef(),eN={},eP="string"==typeof et;M?(eN.title=eA||!eP||B?null:et,eN["aria-describedby"]=eA?ew:null):(eN["aria-label"]=eP?et:null,eN["aria-labelledby"]=eA&&!eP?ew:null);let eI={...eN,...ep,...en.props,className:(0,p.A)(ep.className,en.props.className),onTouchStart:eL,ref:ej,...D?{onMouseMove:e=>{let t=en.props;t.onMouseMove&&t.onMouseMove(e),N={x:e.clientX,y:e.clientY},eC.current&&eC.current.update()}}:{}},eB={};_||(eI.onTouchStart=e=>{eL(e),eh.clear(),eg.clear(),eT(),ex.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",ey.start(X,()=>{document.body.style.WebkitUserSelect=ex.current,eM(e)})},eI.onTouchEnd=e=>{en.props.onTouchEnd&&en.props.onTouchEnd(e),eT(),eh.start(V,()=>{ek(e)})}),!B&&(eI.onMouseOver=P(eM,eI.onMouseOver),eI.onMouseLeave=P(eO,eI.onMouseLeave),ed||(eB.onMouseOver=eM,eB.onMouseLeave=eO)),!I&&(eI.onFocus=P(eW,eI.onFocus),eI.onBlur=P(eE,eI.onBlur),ed||(eB.onFocus=eW,eB.onBlur=eE));let eF={...u,isRtl:el,arrow:d,disableInteractive:ed,placement:J,PopperComponentProp:K,touch:em.current},e_="function"==typeof $.popper?$.popper(eF):$.popper,ez=r.useMemo(()=>{var e,t;let o=[{name:"arrow",enabled:!!ec,options:{element:ec,padding:4}}];return(null==(e=Q.popperOptions)?void 0:e.modifiers)&&(o=o.concat(Q.popperOptions.modifiers)),(null==e_||null==(t=e_.popperOptions)?void 0:t.modifiers)&&(o=o.concat(e_.popperOptions.modifiers)),{...Q.popperOptions,...null==e_?void 0:e_.popperOptions,modifiers:o}},[ec,Q.popperOptions,null==e_?void 0:e_.popperOptions]),eU=S(eF),eX="function"==typeof $.transition?$.transition(eF):$.transition,eD={slots:{popper:R.Popper,transition:null!=(o=R.Transition)?o:eo,tooltip:R.Tooltip,arrow:R.Arrow,...ee},slotProps:{arrow:null!=(a=$.arrow)?a:k.arrow,popper:{...Q,...null!=e_?e_:k.popper},tooltip:null!=(l=$.tooltip)?l:k.tooltip,transition:{...er,...null!=eX?eX:k.transition}}},[eG,eH]=(0,x.A)("popper",{elementType:E,externalForwardedProps:eD,ownerState:eF,className:(0,p.A)(eU.popper,null==Q?void 0:Q.className)}),[eV,eY]=(0,x.A)("transition",{elementType:h.A,externalForwardedProps:eD,ownerState:eF}),[eZ,eq]=(0,x.A)("tooltip",{elementType:W,className:eU.tooltip,externalForwardedProps:eD,ownerState:eF}),[eJ,eK]=(0,x.A)("arrow",{elementType:L,className:eU.arrow,externalForwardedProps:eD,ownerState:eF,ref:eu});return(0,O.jsxs)(r.Fragment,{children:[r.cloneElement(en,eI),(0,O.jsx)(eG,{as:null!=K?K:y.A,placement:J,anchorEl:D?{getBoundingClientRect:()=>({top:N.y,left:N.x,right:N.x,bottom:N.y,width:0,height:0})}:ei,popperRef:eC,open:!!ei&&eA,id:ew,transition:!0,...eB,...eH,popperOptions:ez,children:e=>{let{TransitionProps:t}=e;return(0,O.jsx)(eV,{timeout:ea.transitions.duration.shorter,...t,...eY,children:(0,O.jsxs)(eZ,{...eq,children:[et,d?(0,O.jsx)(eJ,{...eK}):null]})})}})]})})}}]);