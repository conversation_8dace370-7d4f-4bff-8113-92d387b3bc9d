"use strict";exports.id=985,exports.ids=[985],exports.modules={59985:(e,t,n)=>{n.d(t,{A:()=>I});var r=n(43210),o=n(99282),i=n(72606),a=n(31324),s=n(99378);let l=function(e={}){let{autoHideDuration:t=null,disableWindowBlurListener:n=!1,onClose:o,open:l,resumeHideDuration:c}=e,u=(0,a.A)();r.useEffect(()=>{if(l)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){e.defaultPrevented||"Escape"!==e.key||o?.(e,"escapeKeyDown")}},[l,o]);let p=(0,i.A)((e,t)=>{o?.(e,t)}),d=(0,i.A)(e=>{o&&null!=e&&u.start(e,()=>{p(null,"timeout")})});r.useEffect(()=>(l&&d(t),u.clear),[l,t,d,u]);let f=u.clear,m=r.useCallback(()=>{null!=t&&d(null!=c?c:.5*t)},[t,c,d]),g=e=>t=>{let n=e.onBlur;n?.(t),m()},h=e=>t=>{let n=e.onFocus;n?.(t),f()},v=e=>t=>{let n=e.onMouseEnter;n?.(t),f()},A=e=>t=>{let n=e.onMouseLeave;n?.(t),m()};return r.useEffect(()=>{if(!n&&l)return window.addEventListener("focus",m),window.addEventListener("blur",f),()=>{window.removeEventListener("focus",m),window.removeEventListener("blur",f)}},[n,l,m,f]),{getRootProps:(t={})=>{let n={...(0,s.A)(e),...(0,s.A)(t)};return{role:"presentation",...t,...n,onBlur:g(n),onFocus:h(n),onMouseEnter:v(n),onMouseLeave:A(n)}},onClickAway:e=>{o?.(e,"clickaway")}}};var c=n(6352),u=n(83662),p=n(76070);function d(e){return e.substring(2).toLowerCase()}function f(e){let{children:t,disableReactTree:n=!1,mouseEvent:o="onClick",onClickAway:a,touchEvent:s="onTouchEnd"}=e,l=r.useRef(!1),f=r.useRef(null),m=r.useRef(!1),g=r.useRef(!1);r.useEffect(()=>(setTimeout(()=>{m.current=!0},0),()=>{m.current=!1}),[]);let h=(0,u.A)((0,p.A)(t),f),v=(0,i.A)(e=>{let t,r=g.current;g.current=!1;let o=(0,c.A)(f.current);if(!(!m.current||!f.current||"clientX"in e&&(o.documentElement.clientWidth<e.clientX||o.documentElement.clientHeight<e.clientY))){if(l.current){l.current=!1;return}(e.composedPath?e.composedPath().includes(f.current):!o.documentElement.contains(e.target)||f.current.contains(e.target))||!n&&r||a(e)}}),A=e=>n=>{g.current=!0;let r=t.props[e];r&&r(n)},y={ref:h};return!1!==s&&(y[s]=A(s)),r.useEffect(()=>{if(!1!==s){let e=d(s),t=(0,c.A)(f.current),n=()=>{l.current=!0};return t.addEventListener(e,v),t.addEventListener("touchmove",n),()=>{t.removeEventListener(e,v),t.removeEventListener("touchmove",n)}}},[v,s]),!1!==o&&(y[o]=A(o)),r.useEffect(()=>{if(!1!==o){let e=d(o),t=(0,c.A)(f.current);return t.addEventListener(e,v),()=>{t.removeEventListener(e,v)}}},[v,o]),r.cloneElement(t,y)}var m=n(13555),g=n(21360),h=n(45258),v=n(84754),A=n(61543),y=n(97752),b=n(49384),k=n(2899),w=n(51067),E=n(4144),x=n(82816);function C(e){return(0,x.Ay)("MuiSnackbarContent",e)}(0,E.A)("MuiSnackbarContent",["root","message","action"]);var L=n(60687);let S=e=>{let{classes:t}=e;return(0,o.A)({root:["root"],action:["action"],message:["message"]},C,t)},O=(0,m.Ay)(w.A,{name:"MuiSnackbarContent",slot:"Root"})((0,h.A)(({theme:e})=>{let t="light"===e.palette.mode?.8:.98,n=(0,k.tL)(e.palette.background.default,t);return{...e.typography.body2,color:e.vars?e.vars.palette.SnackbarContent.color:e.palette.getContrastText(n),backgroundColor:e.vars?e.vars.palette.SnackbarContent.bg:n,display:"flex",alignItems:"center",flexWrap:"wrap",padding:"6px 16px",borderRadius:(e.vars||e).shape.borderRadius,flexGrow:1,[e.breakpoints.up("sm")]:{flexGrow:"initial",minWidth:288}}})),M=(0,m.Ay)("div",{name:"MuiSnackbarContent",slot:"Message"})({padding:"8px 0"}),P=(0,m.Ay)("div",{name:"MuiSnackbarContent",slot:"Action"})({display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:16,marginRight:-8}),R=r.forwardRef(function(e,t){let n=(0,v.b)({props:e,name:"MuiSnackbarContent"}),{action:r,className:o,message:i,role:a="alert",...s}=n,l=S(n);return(0,L.jsxs)(O,{role:a,square:!0,elevation:6,className:(0,b.A)(l.root,o),ownerState:n,ref:t,...s,children:[(0,L.jsx)(M,{className:l.message,ownerState:n,children:i}),r?(0,L.jsx)(P,{className:l.action,ownerState:n,children:r}):null]})});function T(e){return(0,x.Ay)("MuiSnackbar",e)}(0,E.A)("MuiSnackbar",["root","anchorOriginTopCenter","anchorOriginBottomCenter","anchorOriginTopRight","anchorOriginBottomRight","anchorOriginTopLeft","anchorOriginBottomLeft"]);var j=n(34414);let z=e=>{let{classes:t,anchorOrigin:n}=e,r={root:["root",`anchorOrigin${(0,A.A)(n.vertical)}${(0,A.A)(n.horizontal)}`]};return(0,o.A)(r,T,t)},B=(0,m.Ay)("div",{name:"MuiSnackbar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t[`anchorOrigin${(0,A.A)(n.anchorOrigin.vertical)}${(0,A.A)(n.anchorOrigin.horizontal)}`]]}})((0,h.A)(({theme:e})=>({zIndex:(e.vars||e).zIndex.snackbar,position:"fixed",display:"flex",left:8,right:8,justifyContent:"center",alignItems:"center",variants:[{props:({ownerState:e})=>"top"===e.anchorOrigin.vertical,style:{top:8,[e.breakpoints.up("sm")]:{top:24}}},{props:({ownerState:e})=>"top"!==e.anchorOrigin.vertical,style:{bottom:8,[e.breakpoints.up("sm")]:{bottom:24}}},{props:({ownerState:e})=>"left"===e.anchorOrigin.horizontal,style:{justifyContent:"flex-start",[e.breakpoints.up("sm")]:{left:24,right:"auto"}}},{props:({ownerState:e})=>"right"===e.anchorOrigin.horizontal,style:{justifyContent:"flex-end",[e.breakpoints.up("sm")]:{right:24,left:"auto"}}},{props:({ownerState:e})=>"center"===e.anchorOrigin.horizontal,style:{[e.breakpoints.up("sm")]:{left:"50%",right:"auto",transform:"translateX(-50%)"}}}]}))),I=r.forwardRef(function(e,t){let n=(0,v.b)({props:e,name:"MuiSnackbar"}),o=(0,g.A)(),i={enter:o.transitions.duration.enteringScreen,exit:o.transitions.duration.leavingScreen},{action:a,anchorOrigin:{vertical:s,horizontal:c}={vertical:"bottom",horizontal:"left"},autoHideDuration:u=null,children:p,className:d,ClickAwayListenerProps:m,ContentProps:h,disableWindowBlurListener:A=!1,message:b,onBlur:k,onClose:w,onFocus:E,onMouseEnter:x,onMouseLeave:C,open:S,resumeHideDuration:O,slots:M={},slotProps:P={},TransitionComponent:T,transitionDuration:I=i,TransitionProps:{onEnter:F,onExited:N,...$}={},...W}=n,X={...n,anchorOrigin:{vertical:s,horizontal:c},autoHideDuration:u,disableWindowBlurListener:A,TransitionComponent:T,transitionDuration:I},G=z(X),{getRootProps:q,onClickAway:D}=l({...X}),[H,K]=r.useState(!0),Y=e=>{K(!0),N&&N(e)},J=(e,t)=>{K(!1),F&&F(e,t)},Q={slots:{transition:T,...M},slotProps:{content:h,clickAwayListener:m,transition:$,...P}},[U,V]=(0,j.A)("root",{ref:t,className:[G.root,d],elementType:B,getSlotProps:q,externalForwardedProps:{...Q,...W},ownerState:X}),[Z,{ownerState:_,...ee}]=(0,j.A)("clickAwayListener",{elementType:f,externalForwardedProps:Q,getSlotProps:e=>({onClickAway:(...t)=>{let n=t[0];e.onClickAway?.(...t),n?.defaultMuiPrevented||D(...t)}}),ownerState:X}),[et,en]=(0,j.A)("content",{elementType:R,shouldForwardComponentProp:!0,externalForwardedProps:Q,additionalProps:{message:b,action:a},ownerState:X}),[er,eo]=(0,j.A)("transition",{elementType:y.A,externalForwardedProps:Q,getSlotProps:e=>({onEnter:(...t)=>{e.onEnter?.(...t),J(...t)},onExited:(...t)=>{e.onExited?.(...t),Y(...t)}}),additionalProps:{appear:!0,in:S,timeout:I,direction:"top"===s?"down":"up"},ownerState:X});return!S&&H?null:(0,L.jsx)(Z,{...ee,...M.clickAwayListener&&{ownerState:_},children:(0,L.jsx)(U,{...V,children:(0,L.jsx)(er,{...eo,children:p||(0,L.jsx)(et,{...en})})})})})}};