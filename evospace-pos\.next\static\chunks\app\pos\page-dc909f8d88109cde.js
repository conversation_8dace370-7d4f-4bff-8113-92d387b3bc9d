(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[807],{21073:(e,t,r)=>{Promise.resolve().then(r.bind(r,89546))},89546:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>H});var o=r(95155),n=r(12115),s=r(69242),a=r(63954),i=r(60807),l=r(98028),c=r(19505),d=r(62232),x=r(12173),u=r(41218),h=r(16632),p=r(54581),m=r(700),g=r(3127),b=r(68534),A=r(49994),j=r(18096),f=r(78449),v=r(33989),y=r(27088),C=r(17348),w=r(68104),S=r(76380),k=r(18407),F=r(72705),P=r(2730);let T=()=>{let{cartTotal:e,cart:t}=(0,P.A)(),[r,s]=(0,n.useState)(e());return(0,n.useEffect)(()=>{s(e())},[t,e]),(0,o.jsxs)(m.A,{variant:"h6",children:["$",r.toFixed(2)]})},N=e=>{let{resource_id:t}=e,[r,s]=(0,n.useState)(0),{getActiveSession:a}=(0,P.A)();(0,n.useEffect)(()=>{let e=a(t);if(!e)return void s(0);let r=()=>{s(Math.round((Date.now()-new Date(e.start_time).getTime())/6e4))};r();let o=setInterval(r,1e3);return()=>clearInterval(o)},[t,a]);let i=10*Math.ceil(r/10);return r<=0&&!a(t)?null:(0,o.jsxs)(o.Fragment,{children:[r," min",i>r&&(0,o.jsxs)(m.A,{component:"span",variant:"caption",sx:{ml:.5,opacity:.7},children:["(Billed: ",i," min)"]})]})},M=e=>{let t=Math.max(...e.resources.map(e=>(e.x||0)+(e.width||1)),0),r=Math.max(...e.resources.map(e=>(e.y||0)+(e.height||1)),0),s=50*r+(r-1)*10,[a,i]=(0,n.useState)(!1),[l,c]=(0,n.useState)({x:0,y:0}),[d,x]=(0,n.useState)({x:0,y:0}),u=(0,n.useRef)(null),h=t=>{let r="all"===e.currentResourceStatusFilter||t.status===e.currentResourceStatusFilter,o=""===e.currentSearchQuery||t.name.toLowerCase().includes(e.currentSearchQuery.toLowerCase()),n="all"===e.currentSelectedZone||null===e.currentSelectedZone||t.zone===e.currentSelectedZone;return r&&o&&n},g=e=>{let t=e.width||1,r=e.height||1;return{width:50*t+(t-1)*10,height:50*r+(r-1)*10}},b=e=>{let t=e.x||0;return{top:60*(e.y||0),left:60*t}},A=(e,t,r)=>{if(!r)return{bg:"rgba(189, 189, 189, 0.4)",border:"grey.400",glow:"none",textColor:"grey.700"};if(t)return{bg:"rgba(33, 150, 243, 0.9)",border:"primary.main",glow:"0 0 15px rgba(33, 150, 243, 0.5)",textColor:"white"};switch(e){case"available":return{bg:"rgba(76, 175, 80, 0.7)",border:"success.main",glow:"none",textColor:"white"};case"booked":return{bg:"rgba(244, 67, 54, 0.7)",border:"error.main",glow:"none",textColor:"white"};case"maintenance":return{bg:"rgba(255, 152, 0, 0.7)",border:"warning.main",glow:"none",textColor:"white"};case"in-use":return{bg:"rgba(33, 150, 243, 0.7)",border:"primary.main",glow:"none",textColor:"white"};default:return{bg:"rgba(158, 158, 158, 0.7)",border:"text.secondary",glow:"none",textColor:"white"}}};return(0,n.useEffect)(()=>{let e=()=>{a&&i(!1)};return window.addEventListener("mouseup",e),()=>{window.removeEventListener("mouseup",e)}},[a]),(0,o.jsxs)(p.A,{className:"floor-plan-container",sx:{position:"relative",width:"100%",minHeight:"".concat(Math.max(200,s+100),"px"),border:"1px solid #ccc",borderRadius:1,overflow:"hidden",backgroundColor:"#f5f5f5",mb:3,p:2,cursor:a?"grabbing":"grab"},onMouseDown:t=>{let r=t.target;(r.classList.contains("floor-plan-container")||r.classList.contains("grid-background"))&&(e.onBackgroundClick&&e.onBackgroundClick(t),i(!0),c({x:t.clientX-d.x,y:t.clientY-d.y}))},onMouseMove:e=>{a&&x({x:e.clientX-l.x,y:e.clientY-l.y})},onMouseUp:()=>{i(!1)},ref:u,children:[(0,o.jsx)(p.A,{sx:{position:"absolute",top:10,right:10,backgroundColor:"rgba(255, 255, 255, 0.8)",padding:1,borderRadius:1,zIndex:10,fontSize:"0.75rem",boxShadow:1},children:(0,o.jsx)(m.A,{variant:"caption",display:"block",children:"• Click and drag to move the canvas"})}),(0,o.jsxs)(p.A,{sx:{position:"relative",width:"".concat(50*t+(t-1)*10,"px"),height:"".concat(s,"px"),margin:"0 auto",transform:"translate(".concat(d.x,"px, ").concat(d.y,"px)"),transformOrigin:"0 0",transition:a?"none":"transform 0.1s ease-out"},children:[(0,o.jsx)(p.A,{className:"grid-background",sx:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backgroundImage:"linear-gradient(#ddd 1px, transparent 1px), linear-gradient(90deg, #ddd 1px, transparent 1px)",backgroundSize:"".concat(50,"px ").concat(50,"px"),opacity:.5}}),e.resources.filter(t=>{var r;return"all"===e.currentSelectedFloor||(null==(r=t.floor)?void 0:r.toString())===e.currentSelectedFloor.toString()}).map(t=>{let r=g(t),n=b(t),s=void 0!==e.getActiveSession(t.id),a=h(t),i=A(t.status,s,a),l="T-".concat(t.id.toString().padStart(2,"0")),c=e.getActiveSession(t.id);return(0,o.jsxs)(p.A,{className:"resource-item ".concat(e.selectedResource===t.id?"selected":""),onClick:()=>{a&&(e.getActiveSession(t.id)||"available"===t.status?e.onResourceSelect(t.id):e.onShowInfoAlert("Resource Unavailable","Resource ".concat(t.name," is currently ").concat(t.status," and cannot be selected.")))},sx:{position:"absolute",top:n.top,left:n.left,width:r.width,height:r.height,backgroundColor:i.bg,border:"2px solid",borderColor:e.selectedResource===t.id&&a?"primary.dark":i.border,borderRadius:2,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",cursor:a?"pointer":"default",boxShadow:e.selectedResource===t.id&&a?i.glow:a?"2px 2px 5px rgba(0,0,0,0.1)":"none",transition:"all 0.2s ease",opacity:a?1:.6,"&:hover":a?{transform:"translateY(-2px)",boxShadow:"0 4px 12px ".concat("text.secondary"===i.border?"rgba(0,0,0,0.2)":i.border)}:{},p:1,textAlign:"center"},children:[(0,o.jsx)(m.A,{variant:"body2",sx:{color:i.textColor,fontWeight:"bold",fontSize:"0.8rem",mb:.5},children:l}),(0,o.jsxs)(p.A,{sx:{display:"flex",flexDirection:"column",alignItems:"center"},children:[r.width>=100&&r.height>=40&&(0,o.jsx)(m.A,{variant:"body2",sx:{color:i.textColor,textAlign:"center",fontSize:"0.7rem",mt:.25},children:t.name}),c&&a&&(0,o.jsx)(m.A,{component:"div",variant:"body2",sx:{color:i.textColor,textAlign:"center",fontSize:"0.7rem",mt:.5,fontWeight:"bold"},children:(0,o.jsx)(N,{resource_id:t.id})})]}),("available"!==t.status||s)&&a&&(0,o.jsx)(p.A,{sx:{position:"absolute",top:5,right:5,width:10,height:10,borderRadius:"50%",backgroundColor:s||"in-use"===t.status?"primary.main":"booked"===t.status?"error.main":"warning.main",boxShadow:s?"0 0 10px rgba(33, 150, 243, 0.7)":"0 0 5px rgba(0,0,0,0.3)",animation:s?"pulse 2s infinite":"none","@keyframes pulse":{"0%":{boxShadow:"0 0 0 0 rgba(33, 150, 243, 0.7)"},"70%":{boxShadow:"0 0 0 10px rgba(33, 150, 243, 0)"},"100%":{boxShadow:"0 0 0 0 rgba(33, 150, 243, 0)"}}}})]},t.id)})]})]})};var W=r(98648),I=r(36114),R=r(54492),D=r(99927),E=r(71977);let z=e=>{let{open:t,onClose:r,cart:n,cartTotal:s,getActiveSession:a,paymentMethod:i,setPaymentMethod:l,paymentAmount:c,setPaymentAmount:d,onCompleteTransaction:x}=e,u=s();return(0,o.jsxs)(I.A,{open:t,onClose:r,maxWidth:"xs",fullWidth:!0,children:[(0,o.jsx)(R.A,{children:"Complete Payment"}),(0,o.jsxs)(D.A,{children:[(0,o.jsxs)(p.A,{sx:{mb:3},children:[(0,o.jsx)(m.A,{variant:"body1",gutterBottom:!0,children:"Total Amount:"}),(0,o.jsxs)(p.A,{children:[(0,o.jsxs)(m.A,{variant:"h4",color:"primary",children:["$",u.toFixed(2)]}),(0,o.jsxs)(p.A,{sx:{mt:2,maxHeight:"200px",overflowY:"auto"},children:[" ",(0,o.jsx)(m.A,{variant:"subtitle2",color:"text.secondary",sx:{mb:1},children:"Session Details:"}),n.map(e=>{let t=a(e.id);if(t){let r=Math.round((Date.now()-new Date(t.start_time).getTime())/6e4),n=10*Math.ceil(r/10),s=n/60,a=s*e.price;return(0,o.jsxs)(p.A,{sx:{mb:2,fontSize:"0.875rem"},children:[" ",(0,o.jsx)(m.A,{variant:"body2",color:"text.secondary",sx:{fontWeight:"bold"},children:e.name}),(0,o.jsxs)(m.A,{variant:"caption",display:"block",color:"text.secondary",sx:{pl:2},children:["• Duration: ",r," min"]}),(0,o.jsxs)(m.A,{variant:"caption",display:"block",color:"text.secondary",sx:{pl:2},children:["• Billed: ",n," min (",s.toFixed(2)," hrs)"]}),(0,o.jsxs)(m.A,{variant:"caption",display:"block",color:"primary",sx:{pl:2,mb:.5},children:["Resource Total: $",a.toFixed(2)]}),t.services.length>0&&(0,o.jsxs)(p.A,{sx:{mt:.5},children:[(0,o.jsxs)(m.A,{variant:"caption",display:"block",color:"text.secondary",sx:{fontWeight:"bold",pl:2},children:["Services for ",e.name,":"]}),t.services.map(e=>{let t=Number(e.price),r=Number(e.quantity),n=t*r;return(0,o.jsxs)(m.A,{variant:"caption",display:"block",color:"text.secondary",sx:{pl:3},children:["• ",e.name,": $",isNaN(t)?"0.00":t.toFixed(2)," x ",isNaN(r)?1:r," = $",isNaN(n)?"0.00":n.toFixed(2)]},"service-".concat(e.id))})]}),t.products.length>0&&(0,o.jsxs)(p.A,{sx:{mt:.5},children:[(0,o.jsxs)(m.A,{variant:"caption",display:"block",color:"text.secondary",sx:{fontWeight:"bold",pl:2},children:["Products for ",e.name,":"]}),t.products.map(e=>{let t=Number(e.price),r=Number(e.quantity),n=t*r;return(0,o.jsxs)(m.A,{variant:"caption",display:"block",color:"text.secondary",sx:{pl:3},children:["• ",e.name,": $",isNaN(t)?"0.00":t.toFixed(2)," x ",isNaN(r)?1:r," = $",isNaN(n)?"0.00":n.toFixed(2)]},"product-".concat(e.id))})]})]},e.id)}return null})]})]})]}),(0,o.jsx)(m.A,{variant:"body1",gutterBottom:!0,children:"Payment Method:"}),(0,o.jsxs)(F.A,{direction:"row",spacing:1,sx:{mb:3},children:[(0,o.jsx)(b.A,{variant:"cash"===i?"contained":"outlined",onClick:()=>l("cash"),children:"Cash"}),(0,o.jsx)(b.A,{variant:"card"===i?"contained":"outlined",onClick:()=>l("card"),children:"Card"}),(0,o.jsx)(b.A,{variant:"mobile"===i?"contained":"outlined",onClick:()=>l("mobile"),children:"Mobile"})]}),"cash"===i&&(0,o.jsx)(f.A,{label:"Amount Received",type:"number",fullWidth:!0,value:c,onChange:e=>d(e.target.value),InputProps:{startAdornment:(0,o.jsx)(v.A,{position:"start",children:"$"}),inputProps:{min:0}},sx:{mb:2}}),"cash"===i&&parseFloat(c)>=u&&""!==c&&(0,o.jsx)(p.A,{sx:{mb:2},children:(0,o.jsxs)(m.A,{variant:"body2",children:["Change: $",(parseFloat(c)-u).toFixed(2)]})})]}),(0,o.jsxs)(E.A,{children:[(0,o.jsx)(b.A,{onClick:r,children:"Cancel"}),(0,o.jsx)(b.A,{onClick:x,variant:"contained",disabled:"cash"===i&&(parseFloat(c)<u||""===c),children:"Complete Transaction"})]})]})};var L=r(63148),B=r(22376),_=r(85222),$=r(42663),G=r(41101);let q=e=>{let{open:t,onClose:r,members:n,onSelectMember:s,onSelectWalkIn:i,currentMemberId:l}=e;return(0,o.jsxs)(I.A,{open:t,onClose:r,maxWidth:"lg",fullWidth:!0,children:[(0,o.jsx)(R.A,{children:"Select Member"}),(0,o.jsxs)(D.A,{children:[(0,o.jsx)(f.A,{autoFocus:!0,margin:"dense",label:"Search Members",type:"text",fullWidth:!0,variant:"outlined",InputProps:{startAdornment:(0,o.jsx)(v.A,{position:"start",children:(0,o.jsx)(a.A,{})})},sx:{mb:2}}),(0,o.jsxs)(L.A,{sx:{pt:0},children:[(0,o.jsx)(B.A,{onClick:i,selected:null===l,children:(0,o.jsx)(_.A,{primary:"No Member (Walk-in)"})}),n.map(e=>(0,o.jsxs)(B.A,{onClick:()=>s(e),selected:l===e.id,children:[(0,o.jsx)($.A,{sx:{mr:2,bgcolor:"primary.main"},children:e.name.charAt(0)}),(0,o.jsx)(_.A,{primary:e.name,secondary:e.email}),void 0!==e.visits&&e.visits>0&&(0,o.jsx)(G.A,{label:"Visits: ".concat(e.visits),size:"small",sx:{mr:1}})]},e.id))]})]}),(0,o.jsx)(E.A,{children:(0,o.jsx)(b.A,{onClick:r,children:"Cancel"})})]})};var O=r(6462);let Y=e=>{let{open:t,onClose:r,services:s,onSelectService:i,title:l="Select Service"}=e,[c,d]=(0,n.useState)(""),[x,g]=(0,n.useState)("all"),[A,j]=(0,n.useState)(1),k=(0,n.useMemo)(()=>s?Array.from(new Set(s.map(e=>e.category).filter(Boolean))).sort():[],[s]),F=(0,n.useMemo)(()=>s.filter(e=>{let t=e.name.toLowerCase().includes(c.toLowerCase())||e.description&&e.description.toLowerCase().includes(c.toLowerCase()),r="all"===x||e.category===x;return t&&r}),[s,c,x]),P=(0,n.useMemo)(()=>{let e=(A-1)*8;return F.slice(e,e+8)},[F,A]),T=Math.ceil(F.length/8),N=e=>{i(e),d(""),g("all"),j(1),r()},M=()=>{d(""),g("all"),j(1),r()};return n.useEffect(()=>{j(1)},[c,x]),(0,o.jsxs)(I.A,{open:t,onClose:M,maxWidth:"lg",fullWidth:!0,scroll:"paper",children:[(0,o.jsx)(R.A,{children:l}),(0,o.jsxs)(D.A,{sx:{pb:1,display:"flex",flexDirection:"column"},children:[(0,o.jsxs)(p.A,{sx:{display:"flex",gap:2,mb:2,position:"sticky",top:0,bgcolor:"background.paper",zIndex:1,pt:1,pb:1},children:[(0,o.jsx)(f.A,{autoFocus:!0,margin:"none",label:"Search services...",type:"text",fullWidth:!0,variant:"outlined",value:c,onChange:e=>d(e.target.value),InputProps:{startAdornment:(0,o.jsx)(v.A,{position:"start",children:(0,o.jsx)(a.A,{})})},sx:{flexGrow:2}}),(0,o.jsxs)(y.A,{sx:{minWidth:200,flexGrow:1},children:[(0,o.jsx)(C.A,{children:"Service Type"}),(0,o.jsxs)(w.A,{value:x,label:"Service Type",onChange:e=>g(e.target.value),children:[(0,o.jsx)(S.A,{value:"all",children:"All Types"}),k.map(e=>(0,o.jsx)(S.A,{value:e,children:e},e))]})]})]}),(0,o.jsx)(p.A,{sx:{flexGrow:1,overflowY:"auto",pb:1},children:P.length>0?(0,o.jsx)(p.A,{sx:{display:"flex",flexWrap:"wrap",gap:2,justifyContent:"flex-start",pt:1},children:P.map(e=>(0,o.jsx)(u.A,{sx:{width:200,cursor:"pointer",transition:"transform 0.2s","&:hover":{transform:"scale(1.03)",boxShadow:3}},onClick:()=>N(e),children:(0,o.jsxs)(h.A,{children:[(0,o.jsx)(m.A,{gutterBottom:!0,variant:"h6",component:"div",children:e.name}),(0,o.jsx)(m.A,{variant:"body2",color:"text.secondary",children:e.duration?"".concat(e.duration," minutes"):e.unit?"Per ".concat(e.unit):"Fixed price"}),(0,o.jsxs)(m.A,{variant:"h6",color:"primary",sx:{mt:1},children:["$",Number(e.price).toFixed(2)]})]})},e.id))}):(0,o.jsx)(m.A,{sx:{textAlign:"center",mt:2},children:c||"all"!==x?"No services match your search or filter.":"No services available."})})]}),(0,o.jsxs)(E.A,{sx:{justifyContent:"flex-end",alignItems:"center",pt:1,pb:1,gap:2},children:[T>1&&(0,o.jsx)(O.A,{count:T,page:A,onChange:(e,t)=>{j(t)},color:"primary",size:"small",sx:{mr:"auto"}}),(0,o.jsx)(b.A,{onClick:M,children:"Cancel"})]})]})};var Z=r(60785);let Q=e=>{let{open:t,onClose:r,products:s,onSelectProduct:i,title:l="Select Product"}=e,[c,d]=(0,n.useState)(""),[x,g]=(0,n.useState)("all"),[A,j]=(0,n.useState)(1),k=(0,n.useMemo)(()=>s?Array.from(new Set(s.map(e=>e.category))).sort():[],[s]),F=(0,n.useMemo)(()=>s.filter(e=>{let t=e.name.toLowerCase().includes(c.toLowerCase())||e.category.toLowerCase().includes(c.toLowerCase()),r="all"===x||e.category===x;return t&&r}),[s,c,x]),P=(0,n.useMemo)(()=>{let e=(A-1)*8;return F.slice(e,e+8)},[F,A]),T=Math.ceil(F.length/8),N=e=>{i(e),d(""),g("all"),j(1),r()},M=()=>{d(""),g("all"),j(1),r()};return n.useEffect(()=>{j(1)},[c,x]),(0,o.jsxs)(I.A,{open:t,onClose:M,maxWidth:"lg",fullWidth:!0,scroll:"paper",children:[(0,o.jsx)(R.A,{children:l}),(0,o.jsxs)(D.A,{sx:{pb:1,display:"flex",flexDirection:"column"},children:[(0,o.jsxs)(p.A,{sx:{display:"flex",gap:2,mb:2,position:"sticky",top:0,bgcolor:"background.paper",zIndex:1,pt:1,pb:1},children:[(0,o.jsx)(f.A,{autoFocus:!0,margin:"none",label:"Search products...",type:"text",fullWidth:!0,variant:"outlined",value:c,onChange:e=>d(e.target.value),InputProps:{startAdornment:(0,o.jsx)(v.A,{position:"start",children:(0,o.jsx)(a.A,{})})},sx:{flexGrow:2}}),(0,o.jsxs)(y.A,{sx:{minWidth:200,flexGrow:1},children:[(0,o.jsx)(C.A,{children:"Category"}),(0,o.jsxs)(w.A,{value:x,label:"Category",onChange:e=>g(e.target.value),children:[(0,o.jsx)(S.A,{value:"all",children:"All Categories"}),k.map(e=>(0,o.jsx)(S.A,{value:e,children:e},e))]})]})]}),(0,o.jsx)(p.A,{sx:{flexGrow:1,overflowY:"auto",pb:1},children:P.length>0?(0,o.jsx)(p.A,{sx:{display:"flex",flexWrap:"wrap",gap:2,justifyContent:"flex-start",pt:1},children:P.map(e=>(0,o.jsxs)(u.A,{sx:{width:200,cursor:"pointer",transition:"transform 0.2s","&:hover":{transform:"scale(1.03)",boxShadow:3}},onClick:()=>N(e),children:[e.image?(0,o.jsx)(Z.A,{component:"img",height:"140",image:e.image,alt:e.name}):(0,o.jsx)(p.A,{sx:{height:140,display:"flex",alignItems:"center",justifyContent:"center",bgcolor:"primary.light",color:"white"},children:(0,o.jsx)(m.A,{variant:"h6",align:"center",sx:{p:2},children:e.name})}),(0,o.jsxs)(h.A,{children:[(0,o.jsx)(m.A,{gutterBottom:!0,variant:"h6",component:"div",children:e.name}),(0,o.jsx)(m.A,{variant:"body2",color:"text.secondary",children:e.category}),(0,o.jsxs)(m.A,{variant:"h6",color:"primary",sx:{mt:1},children:["$",Number(e.price).toFixed(2)]})]})]},e.id))}):(0,o.jsx)(m.A,{sx:{textAlign:"center",mt:2},children:c||"all"!==x?"No products match your search or filter.":"No products available."})})]}),(0,o.jsxs)(E.A,{sx:{justifyContent:"flex-end",alignItems:"center",pt:1,pb:1,gap:2},children:[T>1&&(0,o.jsx)(O.A,{count:T,page:A,onChange:(e,t)=>{j(t)},color:"primary",size:"small",sx:{mr:"auto"}}),(0,o.jsx)(b.A,{onClick:M,children:"Cancel"})]})]})},U=n.memo(e=>{let{item:t,resourceDetails:r,session:n,formatCurrency:a,onRemoveItem:i,onAddServiceToSession:l,onAddProductToSession:c}=e;return(0,o.jsx)(u.A,{sx:{mb:2,bgcolor:"primary.light",color:"primary.contrastText"},children:(0,o.jsxs)(h.A,{sx:{px:1,pt:1,pb:2},children:[(0,o.jsxs)(p.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[(0,o.jsxs)(p.A,{sx:{flexGrow:1,mr:1},children:[(0,o.jsx)(m.A,{variant:"subtitle1",sx:{fontWeight:"bold"},children:t.name}),(0,o.jsxs)(m.A,{variant:"body2",sx:{color:"primary.contrastText"},children:["$",Number(t.price).toFixed(2),"/hr • ",null==r?void 0:r.floor," • ",null==r?void 0:r.zone]})]}),(0,o.jsx)(g.A,{edge:"end","aria-label":"delete",onClick:()=>i(t.id),sx:{color:"primary.contrastText",mr:.1},children:(0,o.jsx)(s.A,{})})]}),n&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(m.A,{variant:"body2",component:"span",sx:{display:"block",color:"primary.contrastText",opacity:.7},children:["Duration: ",Math.round((Date.now()-new Date(n.start_time).getTime())/6e4)," min"]}),(()=>{let e=10*Math.ceil(Math.round((Date.now()-new Date(n.start_time).getTime())/6e4)/10),r=e/60,s=r*t.price;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(m.A,{variant:"body2",component:"span",sx:{display:"block",color:"primary.contrastText",opacity:.7},children:["Billed: ",e," min (",r.toFixed(2)," hrs)"]}),(0,o.jsxs)(m.A,{variant:"body2",component:"span",sx:{display:"block",fontWeight:"bold",mt:.5},children:["Resource Total: $",s.toFixed(2)]})]})})()]}),(0,o.jsxs)(p.A,{sx:{mt:1,pt:+!!n,px:1,borderTop:n?"1px solid rgba(255,255,255,0.2)":"none"},children:[(0,o.jsxs)(p.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:n&&n.services.length>0?.5:0},children:[(0,o.jsx)(m.A,{variant:"subtitle2",sx:{color:"rgba(255,255,255,0.8)"},children:"Services:"}),(0,o.jsx)(b.A,{size:"small",variant:"text",onClick:()=>l(t.id),sx:{color:"primary.contrastText",textTransform:"none",p:0,minWidth:"auto","&:hover":{backgroundColor:"transparent",textDecoration:"underline",color:"secondary.light"}},children:"Add"})]}),n&&n.services.length>0?n.services.map((e,t)=>(0,o.jsxs)(p.A,{sx:{py:.5,px:0,display:"flex",justifyContent:"space-between"},children:[(0,o.jsxs)(m.A,{variant:"body2",sx:{color:"primary.contrastText"},children:[e.name," (x",e.quantity,")"]}),(0,o.jsx)(m.A,{variant:"body2",sx:{color:"primary.contrastText"},children:a(e.price*e.quantity)})]},"session-".concat(n.id,"-service-").concat(e.id,"-").concat(t))):(0,o.jsx)(m.A,{variant:"caption",sx:{fontStyle:"italic",color:"rgba(255,255,255,0.6)",display:"block",pl:0},children:"No services added to this session."})]}),(0,o.jsxs)(p.A,{sx:{mt:2,pt:1,px:1,borderTop:"1px solid rgba(255,255,255,0.2)"},children:[(0,o.jsxs)(p.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:n&&n.products.length>0?.5:0},children:[(0,o.jsx)(m.A,{variant:"subtitle2",sx:{color:"rgba(255,255,255,0.8)"},children:"Products:"}),(0,o.jsx)(b.A,{size:"small",variant:"text",onClick:()=>c(t.id),sx:{color:"primary.contrastText",textTransform:"none",p:0,minWidth:"auto","&:hover":{backgroundColor:"transparent",textDecoration:"underline",color:"secondary.light"}},children:"Add"})]}),n&&n.products.length>0?n.products.map((e,t)=>(0,o.jsxs)(p.A,{sx:{py:.5,px:0,display:"flex",justifyContent:"space-between"},children:[(0,o.jsxs)(m.A,{variant:"body2",sx:{color:"primary.contrastText"},children:[e.name," (x",e.quantity,")"]}),(0,o.jsx)(m.A,{variant:"body2",sx:{color:"primary.contrastText"},children:a(e.price*e.quantity)})]},"session-".concat(n.id,"-product-").concat(e.id,"-").concat(t))):(0,o.jsx)(m.A,{variant:"caption",sx:{fontStyle:"italic",color:"rgba(255,255,255,0.6)",display:"block",pl:0},children:"No products added to this session."})]})]})})});function H(){let[e,t]=(0,n.useState)(!1),[r,s]=(0,n.useState)(null),[u,h]=(0,n.useState)("all"),[g,N]=(0,n.useState)(""),[I,R]=(0,n.useState)(!1),[D,E]=(0,n.useState)(!1),[L,B]=(0,n.useState)(!1),[_,$]=(0,n.useState)(!1),[G,O]=(0,n.useState)("cash"),[Z,H]=(0,n.useState)(""),[K,X]=(0,n.useState)(1),[V,J]=(0,n.useState)(null),[ee,et]=(0,n.useState)(null),[er,eo]=(0,n.useState)(null),[en,es]=(0,n.useState)(!1),[ea,ei]=(0,n.useState)(""),[el,ec]=(0,n.useState)(""),{products:ed,services:ex,resources:eu,members:eh,cart:ep,addToCart:em,removeFromCart:eg,clearCart:eb,cartTotal:eA,selectedMember:ej,setSelectedMember:ef,updateResourceStatus:ev,startSession:ey,endSession:eC,getActiveSession:ew,addServiceToActiveSession:eS,addProductToActiveSession:ek,fetchActiveSessions:eF,fetchResources:eP,fetchProducts:eT,fetchServices:eN,authUser:eM}=(0,P.A)();(0,n.useEffect)(()=>{(async()=>{eM.token?(console.log("[PosPage] Auth token found. Fetching initial data..."),await eP(),console.log("[PosPage] Resources fetched. Now fetching active sessions..."),await eF(),console.log("[PosPage] Active sessions fetched.")):console.log("[PosPage] No auth token. Skipping initial data fetch.")})();let e=async()=>{"visible"===document.visibilityState&&eM.token&&(console.log("[PosPage] Page visible. Re-fetching active sessions..."),await eF(),console.log("[PosPage] Active sessions re-fetched on visibility change."))};return document.addEventListener("visibilitychange",e),()=>{document.removeEventListener("visibilitychange",e)}},[eM.token,eP,eF]),(0,n.useEffect)(()=>{_&&eM.token&&(console.log("[PosPage] Product dialog opened. Fetching products..."),eT())},[_,eT,eM.token]),(0,n.useEffect)(()=>{L&&eM.token&&(console.log("[PosPage] Service dialog opened. Fetching services..."),eN())},[L,eN,eM.token]);let eW=e=>{em({id:e.id,name:e.name,price:e.hourly_rate})},eI=()=>{R(!1)},eR=()=>{H(eA().toFixed(2)),E(!0)},eD=(0,n.useCallback)(()=>B(!0),[]),eE=(0,n.useCallback)(()=>{B(!1),eo(null)},[]),ez=async e=>{if(null!==er){if(!ew(er))try{await ey(er)}catch(e){console.error("[Session] Failed to create session for resource ".concat(er,":"),e),eG("Session Error","Failed to create a new session. Please try again."),eE();return}await eS(er,{id:e.id,name:e.name,price:e.price})}else console.log("[Cart] Added service to general cart:",e.name);eE()},eL=(0,n.useCallback)(()=>$(!0),[]),eB=(0,n.useCallback)(()=>{$(!1),eo(null)},[]),e_=async e=>{if(null!==er){if(!ew(er))try{await ey(er)}catch(e){console.error("[Session] Failed to create session for resource ".concat(er,":"),e),eG("Session Error","Failed to create a new session. Please try again."),eB();return}await ek(er,{id:e.id,name:e.name,price:e.price})}else console.log("[Cart] Added product to general cart:",e.name);eB()},e$=e=>"$".concat(e.toFixed(2)),eG=(0,n.useCallback)((e,t)=>{ei(e),ec(t),es(!0)},[]),eq=(0,n.useCallback)(()=>{es(!1),ei(""),ec("")},[]),eO=(0,n.useCallback)(e=>{eg(e),ev(e,"available")},[eg,ev]),eY=(0,n.useCallback)(e=>{eo(e),eD()},[eo,eD]),eZ=(0,n.useCallback)(e=>{eo(e),eL()},[eo,eL]);return(0,o.jsxs)(p.A,{sx:{flexGrow:1,display:"flex",flexDirection:"column",height:"calc(100vh - 64px)"},children:[(0,o.jsx)(p.A,{sx:{borderBottom:1,borderColor:"divider"},children:(0,o.jsxs)(A.A,{value:u,onChange:(e,t)=>{t&&(h(t),N(""))},"aria-label":"Resource Status Filter Tabs",sx:{bgcolor:"background.paper",color:"text.primary"},children:[(0,o.jsx)(j.A,{label:"All Resources",value:"all"}),(0,o.jsx)(j.A,{label:"Available",value:"available"}),(0,o.jsx)(j.A,{label:"In Use",value:"in-use"}),(0,o.jsx)(j.A,{label:"Booked",value:"booked"}),(0,o.jsx)(j.A,{label:"Maintenance",value:"maintenance"})]})}),(0,o.jsxs)(p.A,{sx:{flexGrow:1,display:"flex",overflow:"hidden"},children:[(0,o.jsx)(p.A,{sx:{width:"70%",height:"100%",overflow:"hidden",p:2,display:"flex",flexDirection:"column"},children:(0,o.jsxs)(p.A,{sx:{height:"100%",display:"flex",flexDirection:"column"},children:[(0,o.jsxs)(p.A,{sx:{mb:2,display:"flex",alignItems:"center",gap:2,flexWrap:"wrap"},children:[(0,o.jsx)(f.A,{placeholder:"Search Resources...",variant:"outlined",size:"small",value:g,onChange:e=>N(e.target.value),InputProps:{startAdornment:(0,o.jsx)(v.A,{position:"start",children:(0,o.jsx)(a.A,{})})},sx:{minWidth:"200px",flexGrow:1,maxWidth:"300px"}}),(0,o.jsxs)(y.A,{sx:{minWidth:120},size:"small",children:[(0,o.jsx)(C.A,{children:"Floor"}),(0,o.jsx)(w.A,{value:K,label:"Floor",onChange:e=>X(Number(e.target.value)),children:Array.from(new Set(eu.map(e=>e.floor))).sort((e,t)=>e-t).map(e=>(0,o.jsx)(S.A,{value:e,children:"Floor ".concat(e)},e))})]}),(0,o.jsxs)(y.A,{sx:{minWidth:120},size:"small",children:[(0,o.jsx)(C.A,{children:"Zone"}),(0,o.jsxs)(w.A,{value:V||"all",label:"Zone",onChange:e=>J("all"===e.target.value?null:e.target.value),children:[(0,o.jsx)(S.A,{value:"all",children:"All Zones"}),Array.from(new Set(eu.map(e=>e.zone).filter(Boolean))).sort().map(e=>(0,o.jsx)(S.A,{value:e,children:e},e))]})]})]}),(0,o.jsx)(p.A,{sx:{flexGrow:1,overflowY:"auto",p:0,border:"1px solid",borderColor:"divider",borderRadius:1,position:"relative"},children:(0,o.jsx)(M,{resources:eu,selectedResource:ee,getActiveSession:ew,currentResourceStatusFilter:u,currentSearchQuery:g,currentSelectedFloor:K,currentSelectedZone:V,onShowInfoAlert:eG,onResourceSelect:e=>{let t=eu.find(t=>t.id===e),r=ew(e),o=ep.some(t=>t.id===e);if(t){if(o)return;r||"available"===t.status?(et(e),eW(t)):console.log("[Resource] Resource not available:",{status:t.status})}},onBackgroundClick:()=>{et(null)}})})]})}),(0,o.jsxs)(p.A,{sx:{width:"30%",display:"flex",flexDirection:"column",borderLeft:1,borderColor:"divider"},children:[(0,o.jsxs)(k.A,{sx:{width:"100%",height:"100%",display:"flex",flexDirection:"column",bgcolor:"background.paper"},children:[(0,o.jsxs)(p.A,{sx:{p:2,borderBottom:1,borderColor:"divider"},children:[(0,o.jsx)(m.A,{variant:"h6",gutterBottom:!0,children:"Current Order"}),(0,o.jsx)(b.A,{variant:"outlined",startIcon:(0,o.jsx)(i.A,{}),onClick:()=>{R(!0)},fullWidth:!0,sx:{mb:1},children:ej?ej.name:"Select Member"})]}),(0,o.jsx)(p.A,{sx:{flex:1,overflow:"auto",p:2},children:0===ep.length?(0,o.jsxs)(p.A,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%"},children:[(0,o.jsx)(l.A,{sx:{fontSize:60,color:"text.disabled",mb:2}}),(0,o.jsx)(m.A,{variant:"body1",color:"text.secondary",children:"Cart is empty"}),(0,o.jsx)(m.A,{variant:"body2",color:"text.disabled",children:"Add products or services to get started"})]}):ep.map(e=>{let t=eu.find(t=>t.id===e.id),r=ew(e.id);return(0,o.jsx)(U,{item:e,resourceDetails:t,session:r||null,formatCurrency:e$,onRemoveItem:eO,onAddServiceToSession:eY,onAddProductToSession:eZ},"".concat(e.id))})}),(0,o.jsxs)(p.A,{sx:{p:2,borderTop:1,borderColor:"divider"},children:[(0,o.jsxs)(p.A,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,o.jsx)(m.A,{variant:"h6",children:"Total:"}),(0,o.jsx)(T,{})]}),(0,o.jsxs)(F.A,{direction:"row",spacing:1,sx:{mb:2},children:[(0,o.jsx)(b.A,{variant:"outlined",color:"error",startIcon:(0,o.jsx)(c.A,{}),onClick:()=>{let e=ep.filter(e=>ew(e.id));e.length>0?(eb(),e.forEach(e=>{em({id:e.id,name:e.name,price:e.price})})):(ep.forEach(e=>{ev(e.id,"available")}),eb())},disabled:0===ep.length,fullWidth:!0,children:"Clear"}),ep.length>0?(0,o.jsx)(b.A,{variant:"contained",color:"primary",onClick:()=>{ep.some(e=>ew(e.id))?(ep.forEach(e=>{let t=ew(e.id);t&&eC(t.id)}),eR()):(ep.forEach(e=>{ey(e.id),ev(e.id,"in-use")}),eb())},startIcon:ep.some(e=>ew(e.id))?(0,o.jsx)(d.A,{}):(0,o.jsx)(x.A,{}),fullWidth:!0,children:ep.some(e=>ew(e.id))?"End Session & Pay":"Start Sessions"}):(0,o.jsx)(b.A,{variant:"contained",startIcon:(0,o.jsx)(d.A,{}),onClick:eR,disabled:0===ep.length,fullWidth:!0,children:"Pay"})]})]})]}),(0,o.jsx)(q,{open:I,onClose:eI,members:eh,onSelectMember:e=>{ef(e),eI()},currentMemberId:ej?ej.id:null,onSelectWalkIn:()=>{ef(null),eI()}}),(0,o.jsx)(Y,{open:L,onClose:eE,services:ex,onSelectService:ez}),(0,o.jsx)(Q,{open:_,onClose:eB,products:ed,onSelectProduct:e_}),(0,o.jsx)(z,{open:D,onClose:()=>{E(!1)},cart:ep,cartTotal:eA,getActiveSession:ew,paymentMethod:G,setPaymentMethod:O,paymentAmount:Z,setPaymentAmount:H,onCompleteTransaction:()=>{let{cart:e,activeSessions:t,addTransaction:r,clearCart:o,selectedMember:n}=P.A.getState(),s=new Date().toISOString(),a=[],i=0;e.forEach(e=>{let r=t.find(t=>t.resource_id===e.id);if(r){let t=new Date(r.start_time).getTime(),o=Math.max(0,(new Date(s).getTime()-t)/6e4),n=(o>0?10*Math.ceil(o/10):0)/60*e.price;i+=n;let l=0;r.products.forEach(e=>{l+=e.price*e.quantity}),r.services.forEach(e=>{l+=e.price*e.quantity}),i+=l;let c={...r,end_time:s,status:"completed",products:r.products.map(e=>({...e})),services:r.services.map(e=>({...e}))};a.push(c)}}),a.length>0?r({memberId:n?n.id:null,memberName:n?n.name:"Walk-in",sessions:a,totalAmount:i,paymentMethod:G,status:"completed",createdAt:s}):console.log("[Transaction] No active resource sessions found in cart to complete a resource-based transaction."),o(),E(!1)}}),(0,o.jsx)(W.A,{open:e,title:"End Session",message:"Are you sure you want to end this session? This will finalize the resource usage and proceed to payment.",onConfirm:()=>{r&&(eC(r.id),s(null),eR()),t(!1)},onCancel:()=>{t(!1),s(null)}}),(0,o.jsx)(W.A,{open:en,title:ea,message:el,onConfirm:eq,onCancel:eq,infoMode:!0,confirmText:"OK"})]})]})]})}U.displayName="ResourceCartItem"},98648:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var o=r(95155);r(12115);var n=r(36114),s=r(54492),a=r(99927),i=r(700),l=r(71977),c=r(68534);let d=e=>{let{open:t,title:r,message:d,onConfirm:x,onCancel:u,infoMode:h=!1,confirmText:p,confirmButtonColor:m="primary"}=e;return(0,o.jsxs)(n.A,{open:t,onClose:u,children:[(0,o.jsx)(s.A,{children:r}),(0,o.jsx)(a.A,{children:(0,o.jsx)(i.A,{children:d})}),(0,o.jsxs)(l.A,{children:[!h&&(0,o.jsx)(c.A,{onClick:u,children:"Cancel"}),(0,o.jsx)(c.A,{onClick:x,variant:"contained",color:h?"primary":m,children:h?p||"OK":p||"Confirm"})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[319,692,317,687,257,700,221,13,922,420,730,441,684,358],()=>t(21073)),_N_E=e.O()}]);