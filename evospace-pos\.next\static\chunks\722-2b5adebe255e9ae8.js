(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[722],{6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let o=r(12115);function n(e,t){let r=(0,o.useRef)(null),n=(0,o.useRef)(null);return(0,o.useCallback)(o=>{if(null===o){let e=r.current;e&&(r.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(r.current=a(e,o)),t&&(n.current=a(t,o))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return v},useLinkStatus:function(){return g}});let o=r(6966),n=r(95155),a=o._(r(12115)),l=r(82757),s=r(95227),i=r(69818),c=r(6654),u=r(69991),d=r(85929);r(43230);let p=r(24930),f=r(92664),m=r(6634);function h(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function v(e){let t,r,o,[l,v]=(0,a.useOptimistic)(p.IDLE_LINK_STATUS),g=(0,a.useRef)(null),{href:A,as:b,children:x,prefetch:S=null,passHref:k,replace:C,shallow:M,scroll:w,onClick:j,onMouseEnter:P,onTouchStart:T,legacyBehavior:I=!1,onNavigate:E,ref:L,unstable_dynamicOnHover:O,...B}=e;t=x,I&&("string"==typeof t||"number"==typeof t)&&(t=(0,n.jsx)("a",{children:t}));let R=a.default.useContext(s.AppRouterContext),_=!1!==S,z=null===S?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:N,as:V}=a.default.useMemo(()=>{let e=h(A);return{href:e,as:b?h(b):e}},[A,b]);I&&(r=a.default.Children.only(t));let D=I?r&&"object"==typeof r&&r.ref:L,H=a.default.useCallback(e=>(null!==R&&(g.current=(0,p.mountLinkInstance)(e,N,R,z,_,v)),()=>{g.current&&((0,p.unmountLinkForCurrentNavigation)(g.current),g.current=null),(0,p.unmountPrefetchableInstance)(e)}),[_,N,R,z,v]),F={ref:(0,c.useMergedRef)(H,D),onClick(e){I||"function"!=typeof j||j(e),I&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),R&&(e.defaultPrevented||function(e,t,r,o,n,l,s){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){n&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,m.dispatchNavigateAction)(r||t,n?"replace":"push",null==l||l,o.current)})}}(e,N,V,g,C,w,E))},onMouseEnter(e){I||"function"!=typeof P||P(e),I&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),R&&_&&(0,p.onNavigationIntent)(e.currentTarget,!0===O)},onTouchStart:function(e){I||"function"!=typeof T||T(e),I&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),R&&_&&(0,p.onNavigationIntent)(e.currentTarget,!0===O)}};return(0,u.isAbsoluteUrl)(V)?F.href=V:I&&!k&&("a"!==r.type||"href"in r.props)||(F.href=(0,d.addBasePath)(V)),o=I?a.default.cloneElement(r,F):(0,n.jsx)("a",{...B,...F,children:t}),(0,n.jsx)(y.Provider,{value:l,children:o})}r(73180);let y=(0,a.createContext)(p.IDLE_LINK_STATUS),g=()=>(0,a.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8025:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3m-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3m0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5m8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5"}),"People")},11247:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6"}),"Settings")},12522:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M3 13h8V3H3zm0 8h8v-6H3zm10 0h8V11h-8zm0-18v6h8V3z"}),"Dashboard")},12939:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>R});var o=r(12115),n=r(52596),a=r(17472),l=r(32299),s=r(65207),i=r(31448),c=r(45292),u=r(9700),d=r(36863),p=r(16324),f=r(93789),m=r(34742),h=r(95155);function v(e,t,r){let o=function(e,t,r){let o,n=t.getBoundingClientRect(),a=r&&r.getBoundingClientRect(),l=(0,m.A)(t);if(t.fakeTransform)o=t.fakeTransform;else{let e=l.getComputedStyle(t);o=e.getPropertyValue("-webkit-transform")||e.getPropertyValue("transform")}let s=0,i=0;if(o&&"none"!==o&&"string"==typeof o){let e=o.split("(")[1].split(")")[0].split(",");s=parseInt(e[4],10),i=parseInt(e[5],10)}return"left"===e?a?"translateX(".concat(a.right+s-n.left,"px)"):"translateX(".concat(l.innerWidth+s-n.left,"px)"):"right"===e?a?"translateX(-".concat(n.right-a.left-s,"px)"):"translateX(-".concat(n.left+n.width-s,"px)"):"up"===e?a?"translateY(".concat(a.bottom+i-n.top,"px)"):"translateY(".concat(l.innerHeight+i-n.top,"px)"):a?"translateY(-".concat(n.top-a.top+n.height-i,"px)"):"translateY(-".concat(n.top+n.height-i,"px)")}(e,t,"function"==typeof r?r():r);o&&(t.style.webkitTransform=o,t.style.transform=o)}let y=o.forwardRef(function(e,t){let r=(0,p.A)(),n={enter:r.transitions.easing.easeOut,exit:r.transitions.easing.sharp},a={enter:r.transitions.duration.enteringScreen,exit:r.transitions.duration.leavingScreen},{addEndListener:l,appear:s=!0,children:y,container:g,direction:A="down",easing:b=n,in:x,onEnter:S,onEntered:k,onEntering:C,onExit:M,onExited:w,onExiting:j,style:P,timeout:T=a,TransitionComponent:I=i.Ay,...E}=e,L=o.useRef(null),O=(0,d.A)((0,c.A)(y),L,t),B=e=>t=>{e&&(void 0===t?e(L.current):e(L.current,t))},R=B((e,t)=>{v(A,e,g),(0,f.q)(e),S&&S(e,t)}),_=B((e,t)=>{let o=(0,f.c)({timeout:T,style:P,easing:b},{mode:"enter"});e.style.webkitTransition=r.transitions.create("-webkit-transform",{...o}),e.style.transition=r.transitions.create("transform",{...o}),e.style.webkitTransform="none",e.style.transform="none",C&&C(e,t)}),z=B(k),N=B(j),V=B(e=>{let t=(0,f.c)({timeout:T,style:P,easing:b},{mode:"exit"});e.style.webkitTransition=r.transitions.create("-webkit-transform",t),e.style.transition=r.transitions.create("transform",t),v(A,e,g),M&&M(e)}),D=B(e=>{e.style.webkitTransition="",e.style.transition="",w&&w(e)}),H=o.useCallback(()=>{L.current&&v(A,L.current,g)},[A,g]);return o.useEffect(()=>{if(x||"down"===A||"right"===A)return;let e=(0,u.A)(()=>{L.current&&v(A,L.current,g)}),t=(0,m.A)(L.current);return t.addEventListener("resize",e),()=>{e.clear(),t.removeEventListener("resize",e)}},[A,x,g]),o.useEffect(()=>{x||H()},[x,H]),(0,h.jsx)(I,{nodeRef:L,onEnter:R,onEntered:z,onEntering:_,onExit:V,onExited:D,onExiting:N,addEndListener:e=>{l&&l(L.current,e)},appear:s,in:x,timeout:T,...E,children:(e,t)=>{let{ownerState:r,...n}=t;return o.cloneElement(y,{ref:O,style:{visibility:"exited"!==e||x?void 0:"hidden",...P,...y.props.style},...n})}})});var g=r(18407),A=r(13209),b=r(36437),x=r(75955),S=r(40680),k=r(10186),C=r(55170),M=r(90870);function w(e){return(0,M.Ay)("MuiDrawer",e)}(0,C.A)("MuiDrawer",["root","docked","paper","anchorLeft","anchorRight","anchorTop","anchorBottom","paperAnchorLeft","paperAnchorRight","paperAnchorTop","paperAnchorBottom","paperAnchorDockedLeft","paperAnchorDockedRight","paperAnchorDockedTop","paperAnchorDockedBottom","modal"]);var j=r(47798),P=r(59421);let T=(e,t)=>{let{ownerState:r}=e;return[t.root,("permanent"===r.variant||"persistent"===r.variant)&&t.docked,t.modal]},I=e=>{let{classes:t,anchor:r,variant:o}=e,n={root:["root","anchor".concat((0,A.A)(r))],docked:[("permanent"===o||"persistent"===o)&&"docked"],modal:["modal"],paper:["paper","paperAnchor".concat((0,A.A)(r)),"temporary"!==o&&"paperAnchorDocked".concat((0,A.A)(r))]};return(0,a.A)(n,w,t)},E=(0,x.Ay)(s.A,{name:"MuiDrawer",slot:"Root",overridesResolver:T})((0,S.A)(e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.drawer}})),L=(0,x.Ay)("div",{shouldForwardProp:b.A,name:"MuiDrawer",slot:"Docked",skipVariantsResolver:!1,overridesResolver:T})({flex:"0 0 auto"}),O=(0,x.Ay)(g.A,{name:"MuiDrawer",slot:"Paper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.paper,t["paperAnchor".concat((0,A.A)(r.anchor))],"temporary"!==r.variant&&t["paperAnchorDocked".concat((0,A.A)(r.anchor))]]}})((0,S.A)(e=>{let{theme:t}=e;return{overflowY:"auto",display:"flex",flexDirection:"column",height:"100%",flex:"1 0 auto",zIndex:(t.vars||t).zIndex.drawer,WebkitOverflowScrolling:"touch",position:"fixed",top:0,outline:0,variants:[{props:{anchor:"left"},style:{left:0}},{props:{anchor:"top"},style:{top:0,left:0,right:0,height:"auto",maxHeight:"100%"}},{props:{anchor:"right"},style:{right:0}},{props:{anchor:"bottom"},style:{top:"auto",left:0,bottom:0,right:0,height:"auto",maxHeight:"100%"}},{props:e=>{let{ownerState:t}=e;return"left"===t.anchor&&"temporary"!==t.variant},style:{borderRight:"1px solid ".concat((t.vars||t).palette.divider)}},{props:e=>{let{ownerState:t}=e;return"top"===t.anchor&&"temporary"!==t.variant},style:{borderBottom:"1px solid ".concat((t.vars||t).palette.divider)}},{props:e=>{let{ownerState:t}=e;return"right"===t.anchor&&"temporary"!==t.variant},style:{borderLeft:"1px solid ".concat((t.vars||t).palette.divider)}},{props:e=>{let{ownerState:t}=e;return"bottom"===t.anchor&&"temporary"!==t.variant},style:{borderTop:"1px solid ".concat((t.vars||t).palette.divider)}}]}})),B={left:"right",right:"left",top:"down",bottom:"up"},R=o.forwardRef(function(e,t){let r=(0,k.b)({props:e,name:"MuiDrawer"}),a=(0,p.A)(),s=(0,l.I)(),i={enter:a.transitions.duration.enteringScreen,exit:a.transitions.duration.leavingScreen},{anchor:c="left",BackdropProps:u,children:d,className:f,elevation:m=16,hideBackdrop:v=!1,ModalProps:{BackdropProps:g,...A}={},onClose:b,open:x=!1,PaperProps:S={},SlideProps:C,TransitionComponent:M,transitionDuration:w=i,variant:T="temporary",slots:R={},slotProps:_={},...z}=r,N=o.useRef(!1);o.useEffect(()=>{N.current=!0},[]);let V=function(e,t){let{direction:r}=e;return"rtl"===r&&["left","right"].includes(t)?B[t]:t}({direction:s?"rtl":"ltr"},c),D={...r,anchor:c,elevation:m,open:x,variant:T,...z},H=I(D),F={slots:{transition:M,...R},slotProps:{paper:S,transition:C,..._,backdrop:(0,P.A)(_.backdrop||{...u,...g},{transitionDuration:w})}},[$,K]=(0,j.A)("root",{ref:t,elementType:E,className:(0,n.A)(H.root,H.modal,f),shouldForwardComponentProp:!0,ownerState:D,externalForwardedProps:{...F,...z,...A},additionalProps:{open:x,onClose:b,hideBackdrop:v,slots:{backdrop:F.slots.backdrop},slotProps:{backdrop:F.slotProps.backdrop}}}),[U,W]=(0,j.A)("paper",{elementType:O,shouldForwardComponentProp:!0,className:(0,n.A)(H.paper,S.className),ownerState:D,externalForwardedProps:F,additionalProps:{elevation:"temporary"===T?m:0,square:!0}}),[G,X]=(0,j.A)("docked",{elementType:L,ref:t,className:(0,n.A)(H.root,H.docked,f),ownerState:D,externalForwardedProps:F,additionalProps:z}),[q,Y]=(0,j.A)("transition",{elementType:y,ownerState:D,externalForwardedProps:F,additionalProps:{in:x,direction:B[V],timeout:w,appear:N.current}}),Q=(0,h.jsx)(U,{...W,children:d});if("permanent"===T)return(0,h.jsx)(G,{...X,children:Q});let Z=(0,h.jsx)(q,{...Y,children:Q});return"persistent"===T?(0,h.jsx)(G,{...X,children:Z}):(0,h.jsx)($,{...K,children:Z})})},13895:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2M6 4h5v8l-2.5-1.5L6 12z"}),"Book")},15808:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z"}),"Logout")},22376:(e,t,r)=>{"use strict";r.d(t,{A:()=>A});var o=r(12115),n=r(52596),a=r(17472),l=r(14391),s=r(75955),i=r(40680),c=r(10186),u=r(36437),d=r(25466),p=r(21329),f=r(36863),m=r(99801),h=r(21888),v=r(95155);let y=e=>{let{alignItems:t,classes:r,dense:o,disabled:n,disableGutters:l,divider:s,selected:i}=e,c=(0,a.A)({root:["root",o&&"dense",!l&&"gutters",s&&"divider",n&&"disabled","flex-start"===t&&"alignItemsFlexStart",i&&"selected"]},h.Y,r);return{...r,...c}},g=(0,s.Ay)(d.A,{shouldForwardProp:e=>(0,u.A)(e)||"classes"===e,name:"MuiListItemButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dense&&t.dense,"flex-start"===r.alignItems&&t.alignItemsFlexStart,r.divider&&t.divider,!r.disableGutters&&t.gutters]}})((0,i.A)(e=>{let{theme:t}=e;return{display:"flex",flexGrow:1,justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minWidth:0,boxSizing:"border-box",textAlign:"left",paddingTop:8,paddingBottom:8,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},["&.".concat(h.A.selected)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):(0,l.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),["&.".concat(h.A.focusVisible)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.focusOpacity,"))"):(0,l.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},["&.".concat(h.A.selected,":hover")]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.hoverOpacity,"))"):(0,l.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):(0,l.X4)(t.palette.primary.main,t.palette.action.selectedOpacity)}},["&.".concat(h.A.focusVisible)]:{backgroundColor:(t.vars||t).palette.action.focus},["&.".concat(h.A.disabled)]:{opacity:(t.vars||t).palette.action.disabledOpacity},variants:[{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:"1px solid ".concat((t.vars||t).palette.divider),backgroundClip:"padding-box"}},{props:{alignItems:"flex-start"},style:{alignItems:"flex-start"}},{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.dense},style:{paddingTop:4,paddingBottom:4}}]}})),A=o.forwardRef(function(e,t){let r=(0,c.b)({props:e,name:"MuiListItemButton"}),{alignItems:a="center",autoFocus:l=!1,component:s="div",children:i,dense:u=!1,disableGutters:d=!1,divider:h=!1,focusVisibleClassName:A,selected:b=!1,className:x,...S}=r,k=o.useContext(m.A),C=o.useMemo(()=>({dense:u||k.dense||!1,alignItems:a,disableGutters:d}),[a,k.dense,u,d]),M=o.useRef(null);(0,p.A)(()=>{l&&M.current&&M.current.focus()},[l]);let w={...r,alignItems:a,dense:C.dense,disableGutters:d,divider:h,selected:b},j=y(w),P=(0,f.A)(M,t);return(0,v.jsx)(m.A.Provider,{value:C,children:(0,v.jsx)(g,{ref:P,href:S.href||S.to,component:(S.href||S.to)&&"div"===s?"button":s,focusVisibleClassName:(0,n.A)(j.focusVisible,A),ownerState:w,className:(0,n.A)(j.root,x),...S,classes:j,children:i})})})},22548:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>m});var o=r(12115),n=r(17452),a=r(10186),l=r(95155);let s="function"==typeof(0,n.Dp)({}),i=(e,t)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...t&&!e.vars&&{colorScheme:e.palette.mode}}),c=e=>({color:(e.vars||e).palette.text.primary,...e.typography.body1,backgroundColor:(e.vars||e).palette.background.default,"@media print":{backgroundColor:(e.vars||e).palette.common.white}}),u=function(e){var t,r;let o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n={};o&&e.colorSchemes&&"function"==typeof e.getColorSchemeSelector&&Object.entries(e.colorSchemes).forEach(t=>{var r,o;let[a,l]=t,s=e.getColorSchemeSelector(a);s.startsWith("@")?n[s]={":root":{colorScheme:null==(r=l.palette)?void 0:r.mode}}:n[s.replace(/\s*&/,"")]={colorScheme:null==(o=l.palette)?void 0:o.mode}});let a={html:i(e,o),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:e.typography.fontWeightBold},body:{margin:0,...c(e),"&::backdrop":{backgroundColor:(e.vars||e).palette.background.default}},...n},l=null==(r=e.components)||null==(t=r.MuiCssBaseline)?void 0:t.styleOverrides;return l&&(a=[a,l]),a},d="mui-ecs",p=e=>{let t=u(e,!1),r=Array.isArray(t)?t[0]:t;return!e.vars&&r&&(r.html[":root:has(".concat(d,")")]={colorScheme:e.palette.mode}),e.colorSchemes&&Object.entries(e.colorSchemes).forEach(t=>{var o,n;let[a,l]=t,s=e.getColorSchemeSelector(a);s.startsWith("@")?r[s]={[":root:not(:has(.".concat(d,"))")]:{colorScheme:null==(o=l.palette)?void 0:o.mode}}:r[s.replace(/\s*&/,"")]={["&:not(:has(.".concat(d,"))")]:{colorScheme:null==(n=l.palette)?void 0:n.mode}}}),t},f=(0,n.Dp)(s?e=>{let{theme:t,enableColorScheme:r}=e;return u(t,r)}:e=>{let{theme:t}=e;return p(t)}),m=function(e){let{children:t,enableColorScheme:r=!1}=(0,a.b)({props:e,name:"MuiCssBaseline"});return(0,l.jsxs)(o.Fragment,{children:[s&&(0,l.jsx)(f,{enableColorScheme:r}),!s&&!r&&(0,l.jsx)("span",{className:d,style:{display:"none"}}),t]})}},27371:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M5 16h3v3h2v-5H5zm3-8H5v2h5V5H8zm6 11h2v-3h3v-2h-5zm2-11V5h-2v5h5V8z"}),"FullscreenExit")},27735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},30529:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var o=r(12115),n=r(52596),a=r(17472),l=r(75955),s=r(40680),i=r(10186),c=r(55170),u=r(90870);function d(e){return(0,u.Ay)("MuiToolbar",e)}(0,c.A)("MuiToolbar",["root","gutters","regular","dense"]);var p=r(95155);let f=e=>{let{classes:t,disableGutters:r,variant:o}=e;return(0,a.A)({root:["root",!r&&"gutters",o]},d,t)},m=(0,l.Ay)("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.disableGutters&&t.gutters,t[r.variant]]}})((0,s.A)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",variants:[{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:t.spacing(2),paddingRight:t.spacing(2),[t.breakpoints.up("sm")]:{paddingLeft:t.spacing(3),paddingRight:t.spacing(3)}}},{props:{variant:"dense"},style:{minHeight:48}},{props:{variant:"regular"},style:t.mixins.toolbar}]}})),h=o.forwardRef(function(e,t){let r=(0,i.b)({props:e,name:"MuiToolbar"}),{className:o,component:a="div",disableGutters:l=!1,variant:s="regular",...c}=r,u={...r,component:a,disableGutters:l,variant:s},d=f(u);return(0,p.jsx)(m,{as:a,className:(0,n.A)(d.root,o),ref:t,ownerState:u,...c})})},30660:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 17H7v-7h2zm4 0h-2V7h2zm4 0h-2v-4h2z"}),"Assessment")},32922:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M20 2H4c-1 0-2 .9-2 2v3.01c0 .72.43 1.34 1 1.69V20c0 1.1 1.1 2 2 2h14c.9 0 2-.9 2-2V8.7c.57-.35 1-.97 1-1.69V4c0-1.1-1-2-2-2m-5 12H9v-2h6zm5-7H4V4l16-.02z"}),"Inventory")},33196:(e,t,r)=>{"use strict";r.d(t,{A:()=>B});var o=r(12115);let n=o.createContext(null);function a(){return o.useContext(n)}let l="function"==typeof Symbol&&Symbol.for?Symbol.for("mui.nested"):"__THEME_NESTED__";var s=r(95155);let i=function(e){let{children:t,theme:r}=e,i=a(),c=o.useMemo(()=>{var e,t;let o=null===i?{...r}:(e=i,"function"==typeof(t=r)?t(e):{...e,...t});return null!=o&&(o[l]=null!==i),o},[r,i]);return(0,s.jsx)(n.Provider,{value:c,children:t})};var c=r(64453),u=r(10340),d=r(32299),p=r(70194);let f={};function m(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return o.useMemo(()=>{let o=e&&t[e]||t;if("function"==typeof r){let a=r(o),l=e?{...t,[e]:a}:a;return n?()=>l:l}return e?{...t,[e]:r}:{...t,...r}},[e,t,r,n])}let h=function(e){let{children:t,theme:r,themeId:o}=e,n=(0,u.A)(f),l=a()||f,h=m(o,n,r),v=m(o,l,r,!0),y="rtl"===(o?h[o]:h).direction;return(0,s.jsx)(i,{theme:v,children:(0,s.jsx)(c.T.Provider,{value:h,children:(0,s.jsx)(d.A,{value:y,children:(0,s.jsx)(p.A,{value:o?h[o].components:h.components,children:t})})})})};var v=r(54107);function y(e){let{theme:t,...r}=e,o=v.A in t?t[v.A]:void 0;return(0,s.jsx)(h,{...r,themeId:o?v.A:void 0,theme:o||t})}var g=r(13184),A=r(39051),b=r(43430);let x="mode",S="color-scheme";function k(){}let C=({key:e,storageWindow:t})=>(t||"undefined"==typeof window||(t=window),{get(r){let o;if("undefined"!=typeof window){if(!t)return r;try{o=t.localStorage.getItem(e)}catch{}return o||r}},set:r=>{if(t)try{t.localStorage.setItem(e,r)}catch{}},subscribe:r=>{if(!t)return k;let o=t=>{let o=t.newValue;t.key===e&&r(o)};return t.addEventListener("storage",o),()=>{t.removeEventListener("storage",o)}}});function M(){}function w(e){if("undefined"!=typeof window&&"function"==typeof window.matchMedia&&"system"===e)return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function j(e,t){return"light"===e.mode||"system"===e.mode&&"light"===e.systemMode?t("light"):"dark"===e.mode||"system"===e.mode&&"dark"===e.systemMode?t("dark"):void 0}var P=r(29839),T=r(53373);let I={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:E,useColorScheme:L,getInitColorSchemeScript:O}=function(e){let{themeId:t,theme:r={},modeStorageKey:n=x,colorSchemeStorageKey:l=S,disableTransitionOnChange:i=!1,defaultColorScheme:c,resolveTheme:u}=e,d={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},p=o.createContext(void 0),f={},m={},v="string"==typeof c?c:c.light,y="string"==typeof c?c:c.dark;return{CssVarsProvider:function(e){var d,v,y,g;let{children:k,theme:P,modeStorageKey:T=n,colorSchemeStorageKey:I=l,disableTransitionOnChange:E=i,storageManager:L,storageWindow:O="undefined"==typeof window?void 0:window,documentNode:B="undefined"==typeof document?void 0:document,colorSchemeNode:R="undefined"==typeof document?void 0:document.documentElement,disableNestedContext:_=!1,disableStyleSheetGeneration:z=!1,defaultMode:N="system",forceThemeRerender:V=!1,noSsr:D}=e,H=o.useRef(!1),F=a(),$=o.useContext(p),K=!!$&&!_,U=o.useMemo(()=>P||("function"==typeof r?r():r),[P]),W=U[t],G=W||U,{colorSchemes:X=f,components:q=m,cssVarPrefix:Y}=G,Q=Object.keys(X).filter(e=>!!X[e]).join(","),Z=o.useMemo(()=>Q.split(","),[Q]),J="string"==typeof c?c:c.light,ee="string"==typeof c?c:c.dark,et=X[J]&&X[ee]?N:(null==(v=X[G.defaultColorScheme])||null==(d=v.palette)?void 0:d.mode)||(null==(y=G.palette)?void 0:y.mode),{mode:er,setMode:eo,systemMode:en,lightColorScheme:ea,darkColorScheme:el,colorScheme:es,setColorScheme:ei}=function(e){let{defaultMode:t="light",defaultLightColorScheme:r,defaultDarkColorScheme:n,supportedColorSchemes:a=[],modeStorageKey:l=x,colorSchemeStorageKey:s=S,storageWindow:i="undefined"==typeof window?void 0:window,storageManager:c=C,noSsr:u=!1}=e,d=a.join(","),p=a.length>1,f=o.useMemo(()=>null==c?void 0:c({key:l,storageWindow:i}),[c,l,i]),m=o.useMemo(()=>null==c?void 0:c({key:"".concat(s,"-light"),storageWindow:i}),[c,s,i]),h=o.useMemo(()=>null==c?void 0:c({key:"".concat(s,"-dark"),storageWindow:i}),[c,s,i]),[v,y]=o.useState(()=>{let e=(null==f?void 0:f.get(t))||t,o=(null==m?void 0:m.get(r))||r,a=(null==h?void 0:h.get(n))||n;return{mode:e,systemMode:w(e),lightColorScheme:o,darkColorScheme:a}}),[g,A]=o.useState(u||!p);o.useEffect(()=>{A(!0)},[]);let b=j(v,e=>"light"===e?v.lightColorScheme:"dark"===e?v.darkColorScheme:void 0),k=o.useCallback(e=>{y(r=>{if(e===r.mode)return r;let o=null!=e?e:t;return null==f||f.set(o),{...r,mode:o,systemMode:w(o)}})},[f,t]),P=o.useCallback(e=>{e?"string"==typeof e?e&&!d.includes(e)?console.error("`".concat(e,"` does not exist in `theme.colorSchemes`.")):y(t=>{let r={...t};return j(t,t=>{"light"===t&&(null==m||m.set(e),r.lightColorScheme=e),"dark"===t&&(null==h||h.set(e),r.darkColorScheme=e)}),r}):y(t=>{let o={...t},a=null===e.light?r:e.light,l=null===e.dark?n:e.dark;return a&&(d.includes(a)?(o.lightColorScheme=a,null==m||m.set(a)):console.error("`".concat(a,"` does not exist in `theme.colorSchemes`."))),l&&(d.includes(l)?(o.darkColorScheme=l,null==h||h.set(l)):console.error("`".concat(l,"` does not exist in `theme.colorSchemes`."))),o}):y(e=>(null==m||m.set(r),null==h||h.set(n),{...e,lightColorScheme:r,darkColorScheme:n}))},[d,m,h,r,n]),T=o.useCallback(e=>{"system"===v.mode&&y(t=>{let r=(null==e?void 0:e.matches)?"dark":"light";return t.systemMode===r?t:{...t,systemMode:r}})},[v.mode]),I=o.useRef(T);return I.current=T,o.useEffect(()=>{if("function"!=typeof window.matchMedia||!p)return;let e=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return I.current(...t)},t=window.matchMedia("(prefers-color-scheme: dark)");return t.addListener(e),e(t),()=>{t.removeListener(e)}},[p]),o.useEffect(()=>{if(p){let e=(null==f?void 0:f.subscribe(e=>{(!e||["light","dark","system"].includes(e))&&k(e||t)}))||M,r=(null==m?void 0:m.subscribe(e=>{(!e||d.match(e))&&P({light:e})}))||M,o=(null==h?void 0:h.subscribe(e=>{(!e||d.match(e))&&P({dark:e})}))||M;return()=>{e(),r(),o()}}},[P,k,d,t,i,p,f,m,h]),{...v,mode:g?v.mode:void 0,systemMode:g?v.systemMode:void 0,colorScheme:g?b:void 0,setMode:k,setColorScheme:P}}({supportedColorSchemes:Z,defaultLightColorScheme:J,defaultDarkColorScheme:ee,modeStorageKey:T,colorSchemeStorageKey:I,defaultMode:et,storageManager:L,storageWindow:O,noSsr:D}),ec=er,eu=es;K&&(ec=$.mode,eu=$.colorScheme);let ed=eu||G.defaultColorScheme;G.vars&&!V&&(ed=G.defaultColorScheme);let ep=o.useMemo(()=>{var e;let t=(null==(e=G.generateThemeVars)?void 0:e.call(G))||G.vars,r={...G,components:q,colorSchemes:X,cssVarPrefix:Y,vars:t};if("function"==typeof r.generateSpacing&&(r.spacing=r.generateSpacing()),ed){let e=X[ed];e&&"object"==typeof e&&Object.keys(e).forEach(t=>{e[t]&&"object"==typeof e[t]?r[t]={...r[t],...e[t]}:r[t]=e[t]})}return u?u(r):r},[G,ed,q,X,Y]),ef=G.colorSchemeSelector;(0,b.A)(()=>{if(eu&&R&&ef&&"media"!==ef){let e=ef;if("class"===ef&&(e=".%s"),"data"===ef&&(e="[data-%s]"),(null==ef?void 0:ef.startsWith("data-"))&&!ef.includes("%s")&&(e="[".concat(ef,'="%s"]')),e.startsWith("."))R.classList.remove(...Z.map(t=>e.substring(1).replace("%s",t))),R.classList.add(e.substring(1).replace("%s",eu));else{let t=e.replace("%s",eu).match(/\[([^\]]+)\]/);if(t){let[e,r]=t[1].split("=");r||Z.forEach(t=>{R.removeAttribute(e.replace(eu,t))}),R.setAttribute(e,r?r.replace(/"|'/g,""):"")}else R.setAttribute(e,eu)}}},[eu,ef,R,Z]),o.useEffect(()=>{let e;if(E&&H.current&&B){let t=B.createElement("style");t.appendChild(B.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),B.head.appendChild(t),window.getComputedStyle(B.body),e=setTimeout(()=>{B.head.removeChild(t)},1)}return()=>{clearTimeout(e)}},[eu,E,B]),o.useEffect(()=>(H.current=!0,()=>{H.current=!1}),[]);let em=o.useMemo(()=>({allColorSchemes:Z,colorScheme:eu,darkColorScheme:el,lightColorScheme:ea,mode:ec,setColorScheme:ei,setMode:eo,systemMode:en}),[Z,eu,el,ea,ec,ei,eo,en,ep.colorSchemeSelector]),eh=!0;(z||!1===G.cssVariables||K&&(null==F?void 0:F.cssVarPrefix)===Y)&&(eh=!1);let ev=(0,s.jsxs)(o.Fragment,{children:[(0,s.jsx)(h,{themeId:W?t:void 0,theme:ep,children:k}),eh&&(0,s.jsx)(A.A,{styles:(null==(g=ep.generateStyleSheets)?void 0:g.call(ep))||[]})]});return K?ev:(0,s.jsx)(p.Provider,{value:em,children:ev})},useColorScheme:()=>o.useContext(p)||d,getInitColorSchemeScript:e=>(function(e){let{defaultMode:t="system",defaultLightColorScheme:r="light",defaultDarkColorScheme:o="dark",modeStorageKey:n=x,colorSchemeStorageKey:a=S,attribute:l="data-color-scheme",colorSchemeNode:i="document.documentElement",nonce:c}=e||{},u="",d=l;if("class"===l&&(d=".%s"),"data"===l&&(d="[data-%s]"),d.startsWith(".")){let e=d.substring(1);u+=`${i}.classList.remove('${e}'.replace('%s', light), '${e}'.replace('%s', dark));
      ${i}.classList.add('${e}'.replace('%s', colorScheme));`}let p=d.match(/\[([^\]]+)\]/);if(p){let[e,t]=p[1].split("=");t||(u+=`${i}.removeAttribute('${e}'.replace('%s', light));
      ${i}.removeAttribute('${e}'.replace('%s', dark));`),u+=`
      ${i}.setAttribute('${e}'.replace('%s', colorScheme), ${t?`${t}.replace('%s', colorScheme)`:'""'});`}else u+=`${i}.setAttribute('${d}', colorScheme);`;return(0,s.jsx)("script",{suppressHydrationWarning:!0,nonce:"undefined"==typeof window?c:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${n}') || '${t}';
  const dark = localStorage.getItem('${a}-dark') || '${o}';
  const light = localStorage.getItem('${a}-light') || '${r}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${u}
  }
} catch(e){}})();`}},"mui-color-scheme-init")})({colorSchemeStorageKey:l,defaultLightColorScheme:v,defaultDarkColorScheme:y,modeStorageKey:n,...e})}}({themeId:v.A,theme:()=>(0,P.A)({cssVariables:!0}),colorSchemeStorageKey:I.colorSchemeStorageKey,modeStorageKey:I.modeStorageKey,defaultColorScheme:{light:I.defaultLightColorScheme,dark:I.defaultDarkColorScheme},resolveTheme:e=>{let t={...e,typography:(0,T.A)(e.palette,e.typography)};return t.unstable_sx=function(e){return(0,g.A)({sx:e,theme:this})},t}});function B(e){let{theme:t,...r}=e,n=o.useMemo(()=>{if("function"==typeof t)return t;let e=v.A in t?t[v.A]:t;return"colorSchemes"in e?null:"vars"in e?t:{...t,vars:null}},[t]);return n?(0,s.jsx)(y,{theme:n,...r}):(0,s.jsx)(E,{theme:t,...r})}},35695:(e,t,r)=>{"use strict";var o=r(18999);r.o(o,"usePathname")&&r.d(t,{usePathname:function(){return o.usePathname}}),r.o(o,"useRouter")&&r.d(t,{useRouter:function(){return o.useRouter}}),r.o(o,"useServerInsertedHTML")&&r.d(t,{useServerInsertedHTML:function(){return o.useServerInsertedHTML}})},39489:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var o=r(12115),n=r(52596),a=r(17472),l=r(75955),s=r(40680),i=r(10186),c=r(72562),u=r(99801),d=r(95155);let p=e=>{let{alignItems:t,classes:r}=e;return(0,a.A)({root:["root","flex-start"===t&&"alignItemsFlexStart"]},c.f,r)},f=(0,l.Ay)("div",{name:"MuiListItemIcon",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,"flex-start"===r.alignItems&&t.alignItemsFlexStart]}})((0,s.A)(e=>{let{theme:t}=e;return{minWidth:56,color:(t.vars||t).palette.action.active,flexShrink:0,display:"inline-flex",variants:[{props:{alignItems:"flex-start"},style:{marginTop:8}}]}})),m=o.forwardRef(function(e,t){let r=(0,i.b)({props:e,name:"MuiListItemIcon"}),{className:a,...l}=r,s=o.useContext(u.A),c={...r,alignItems:s.alignItems},m=p(c);return(0,d.jsx)(f,{className:(0,n.A)(m.root,a),ownerState:c,ref:t,...l})})},44389:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M3 18h18v-2H3zm0-5h18v-2H3zm0-7v2h18V6z"}),"Menu")},54581:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var o=r(12115),n=r(52596),a=r(8302),l=r(13184),s=r(5300),i=r(64330),c=r(95155),u=r(34084),d=r(29839),p=r(54107);let f=(0,r(55170).A)("MuiBox",["root"]),m=(0,d.A)(),h=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{themeId:t,defaultTheme:r,defaultClassName:u="MuiBox-root",generateClassName:d}=e,p=(0,a.Ay)("div",{shouldForwardProp:e=>"theme"!==e&&"sx"!==e&&"as"!==e})(l.A);return o.forwardRef(function(e,o){let a=(0,i.A)(r),{className:l,component:f="div",...m}=(0,s.A)(e);return(0,c.jsx)(p,{as:f,ref:o,className:(0,n.A)(l,d?d(u):u),theme:t&&a[t]||a,...m})})}({themeId:p.A,defaultTheme:m,defaultClassName:f.root,generateClassName:u.A.generate})},60807:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"}),"Person")},62093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},63289:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5"}),"Room")},65453:(e,t,r)=>{"use strict";r.d(t,{v:()=>i});var o=r(12115);let n=e=>{let t,r=new Set,o=(e,o)=>{let n="function"==typeof e?e(t):e;if(!Object.is(n,t)){let e=t;t=(null!=o?o:"object"!=typeof n||null===n)?n:Object.assign({},t,n),r.forEach(r=>r(t,e))}},n=()=>t,a={setState:o,getState:n,getInitialState:()=>l,subscribe:e=>(r.add(e),()=>r.delete(e))},l=t=e(o,n,a);return a},a=e=>e?n(e):n,l=e=>e,s=e=>{let t=a(e),r=e=>(function(e,t=l){let r=o.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return o.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},i=e=>e?s(e):s},66027:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M7 14H5v5h5v-2H7zm-2-4h2V7h3V5H5zm12 7h-3v2h5v-5h-2zM14 5v2h3v3h2V5z"}),"Fullscreen")},69991:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return g},MissingStaticPage:function(){return y},NormalizeError:function(){return h},PageNotFoundError:function(){return v},SP:function(){return p},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return o},getDisplayName:function(){return i},getLocationOrigin:function(){return l},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return A}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,r=!1;return function(){for(var o=arguments.length,n=Array(o),a=0;a<o;a++)n[a]=arguments[a];return r||(r=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>n.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=l();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let o=await e.getInitialProps(t);if(r&&c(r))return o;if(!o)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+o+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o}let p="undefined"!=typeof performance,f=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class g extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function A(e){return JSON.stringify({message:e.message,stack:e.stack})}},71169:(e,t,r)=>{"use strict";r.d(t,{A:()=>A});var o=r(12115),n=r(52596),a=r(17472),l=r(75955),s=r(40680),i=r(10186),c=r(13209),u=r(98963),d=r(18407),p=r(55170),f=r(90870);function m(e){return(0,f.Ay)("MuiAppBar",e)}(0,p.A)("MuiAppBar",["root","positionFixed","positionAbsolute","positionSticky","positionStatic","positionRelative","colorDefault","colorPrimary","colorSecondary","colorInherit","colorTransparent","colorError","colorInfo","colorSuccess","colorWarning"]);var h=r(95155);let v=e=>{let{color:t,position:r,classes:o}=e,n={root:["root","color".concat((0,c.A)(t)),"position".concat((0,c.A)(r))]};return(0,a.A)(n,m,o)},y=(e,t)=>e?"".concat(null==e?void 0:e.replace(")",""),", ").concat(t,")"):t,g=(0,l.Ay)(d.A,{name:"MuiAppBar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t["position".concat((0,c.A)(r.position))],t["color".concat((0,c.A)(r.color))]]}})((0,s.A)(e=>{let{theme:t}=e;return{display:"flex",flexDirection:"column",width:"100%",boxSizing:"border-box",flexShrink:0,variants:[{props:{position:"fixed"},style:{position:"fixed",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0,"@media print":{position:"absolute"}}},{props:{position:"absolute"},style:{position:"absolute",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"sticky"},style:{position:"sticky",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"static"},style:{position:"static"}},{props:{position:"relative"},style:{position:"relative"}},{props:{color:"inherit"},style:{"--AppBar-color":"inherit"}},{props:{color:"default"},style:{"--AppBar-background":t.vars?t.vars.palette.AppBar.defaultBg:t.palette.grey[100],"--AppBar-color":t.vars?t.vars.palette.text.primary:t.palette.getContrastText(t.palette.grey[100]),...t.applyStyles("dark",{"--AppBar-background":t.vars?t.vars.palette.AppBar.defaultBg:t.palette.grey[900],"--AppBar-color":t.vars?t.vars.palette.text.primary:t.palette.getContrastText(t.palette.grey[900])})}},...Object.entries(t.palette).filter((0,u.A)(["contrastText"])).map(e=>{var r,o;let[n]=e;return{props:{color:n},style:{"--AppBar-background":(null!=(r=t.vars)?r:t).palette[n].main,"--AppBar-color":(null!=(o=t.vars)?o:t).palette[n].contrastText}}}),{props:e=>!0===e.enableColorOnDark&&!["inherit","transparent"].includes(e.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)"}},{props:e=>!1===e.enableColorOnDark&&!["inherit","transparent"].includes(e.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...t.applyStyles("dark",{backgroundColor:t.vars?y(t.vars.palette.AppBar.darkBg,"var(--AppBar-background)"):null,color:t.vars?y(t.vars.palette.AppBar.darkColor,"var(--AppBar-color)"):null})}},{props:{color:"transparent"},style:{"--AppBar-background":"transparent","--AppBar-color":"inherit",backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...t.applyStyles("dark",{backgroundImage:"none"})}}]}})),A=o.forwardRef(function(e,t){let r=(0,i.b)({props:e,name:"MuiAppBar"}),{className:o,color:a="primary",enableColorOnDark:l=!1,position:s="fixed",...c}=r,u={...r,color:a,position:s,enableColorOnDark:l},d=v(u);return(0,h.jsx)(g,{square:!0,component:"header",ownerState:u,elevation:4,className:(0,n.A)(d.root,o,"fixed"===s&&"mui-fixed"),ref:t,...c})})},73180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},74739:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=r(82370).A},75512:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)([(0,n.jsx)("path",{d:"M8.55 12c-1.07-.71-2.25-1.27-3.53-1.61 1.28.34 2.46.9 3.53 1.61m10.43-1.61c-1.29.34-2.49.91-3.57 1.64 1.08-.73 2.28-1.3 3.57-1.64"},"0"),(0,n.jsx)("path",{d:"M15.49 9.63c-.18-2.79-1.31-5.51-3.43-7.63-2.14 2.14-3.32 4.86-3.55 7.63 1.28.68 2.46 1.56 3.49 2.63 1.03-1.06 2.21-1.94 3.49-2.63m-6.5 2.65c-.14-.1-.3-.19-.45-.29.15.11.31.19.45.29m6.42-.25c-.13.09-.27.16-.4.26.13-.1.27-.17.4-.26M12 15.45C9.85 12.17 6.18 10 2 10c0 5.32 3.36 9.82 8.03 11.49.63.23 1.29.4 1.97.51.68-.12 1.33-.29 1.97-.51C18.64 19.82 22 15.32 22 10c-4.18 0-7.85 2.17-10 5.45"},"1")],"Spa")},77934:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zM12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6m0-10c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4"}),"Brightness7")},78859:(e,t)=>{"use strict";function r(e){let t={};for(let[r,o]of e.entries()){let e=t[r];void 0===e?t[r]=o:Array.isArray(e)?e.push(o):t[r]=[e,o]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,o(e));else t.set(r,o(n));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,o]of t.entries())e.append(r,o)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return n}})},82757:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return s},urlObjectKeys:function(){return l}});let o=r(6966)._(r(78859)),n=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",l=e.pathname||"",s=e.hash||"",i=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:r&&(c=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(c+=":"+e.port)),i&&"object"==typeof i&&(i=String(o.urlQueryToSearchParams(i)));let u=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||n.test(a))&&!1!==c?(c="//"+(c||""),l&&"/"!==l[0]&&(l="/"+l)):c||(c=""),s&&"#"!==s[0]&&(s="#"+s),u&&"?"!==u[0]&&(u="?"+u),""+a+c+(l=l.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+s}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return a(e)}},85222:(e,t,r)=>{"use strict";r.d(t,{A:()=>v});var o=r(12115),n=r(52596),a=r(17472),l=r(31628),s=r(700),i=r(99801),c=r(75955),u=r(10186),d=r(9546),p=r(47798),f=r(95155);let m=e=>{let{classes:t,inset:r,primary:o,secondary:n,dense:l}=e;return(0,a.A)({root:["root",r&&"inset",l&&"dense",o&&n&&"multiline"],primary:["primary"],secondary:["secondary"]},d.b,t)},h=(0,c.Ay)("div",{name:"MuiListItemText",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{["& .".concat(d.A.primary)]:t.primary},{["& .".concat(d.A.secondary)]:t.secondary},t.root,r.inset&&t.inset,r.primary&&r.secondary&&t.multiline,r.dense&&t.dense]}})({flex:"1 1 auto",minWidth:0,marginTop:4,marginBottom:4,[".".concat(l.A.root,":where(& .").concat(d.A.primary,")")]:{display:"block"},[".".concat(l.A.root,":where(& .").concat(d.A.secondary,")")]:{display:"block"},variants:[{props:e=>{let{ownerState:t}=e;return t.primary&&t.secondary},style:{marginTop:6,marginBottom:6}},{props:e=>{let{ownerState:t}=e;return t.inset},style:{paddingLeft:56}}]}),v=o.forwardRef(function(e,t){let r=(0,u.b)({props:e,name:"MuiListItemText"}),{children:a,className:l,disableTypography:c=!1,inset:d=!1,primary:v,primaryTypographyProps:y,secondary:g,secondaryTypographyProps:A,slots:b={},slotProps:x={},...S}=r,{dense:k}=o.useContext(i.A),C=null!=v?v:a,M=g,w={...r,disableTypography:c,inset:d,primary:!!C,secondary:!!M,dense:k},j=m(w),P={slots:b,slotProps:{primary:y,secondary:A,...x}},[T,I]=(0,p.A)("root",{className:(0,n.A)(j.root,l),elementType:h,externalForwardedProps:{...P,...S},ownerState:w,ref:t}),[E,L]=(0,p.A)("primary",{className:j.primary,elementType:s.A,externalForwardedProps:P,ownerState:w}),[O,B]=(0,p.A)("secondary",{className:j.secondary,elementType:s.A,externalForwardedProps:P,ownerState:w});return null==C||C.type===s.A||c||(C=(0,f.jsx)(E,{variant:k?"body2":"body1",component:(null==L?void 0:L.variant)?void 0:"span",...L,children:C})),null==M||M.type===s.A||c||(M=(0,f.jsx)(O,{variant:"body2",color:"textSecondary",...B,children:M})),(0,f.jsxs)(T,{...I,children:[C,M]})})},92664:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let o=r(69991),n=r(87102);function a(e){if(!(0,o.isAbsoluteUrl)(e))return!0;try{let t=(0,o.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,n.hasBasePath)(r.pathname)}catch(e){return!1}}},98387:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(57515),n=r(95155);let a=(0,o.A)((0,n.jsx)("path",{d:"M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zM12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6c3.31 0 6 2.69 6 6s-2.69 6-6 6"}),"Brightness4")}}]);