"use client";

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  InputAdornment,
  Chip,
  CircularProgress,
  Alert,
  Button
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  AttachMoney,
  TrendingUp,
  People,
  ShoppingCart,
  DateRange as DateRangeIcon,
  Refresh
} from '@mui/icons-material';
import {
  Line,
  Pie,
  Doughnut
} from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';
import { subDays, startOfMonth, endOfMonth, format } from 'date-fns';
import usePosStore from '@/lib/store';
import { useTheme } from '@/lib/theme';
import { reportService } from '@/lib/apiService';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`report-tabpanel-${index}`}
      aria-labelledby={`report-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// Interfaces for API data
interface ReportsData {
  salesSummary: {
    total_revenue: number;
    transaction_count: number;
    average_transaction_value: number;
    top_products: Array<{
      product_id: number;
      name: string;
      quantity_sold: number;
      revenue: number;
    }>;
    period: {
      start_date: string;
      end_date: string;
    };
  } | null;
  salesByCategory: {
    categories: Array<{
      category: string;
      total_amount: number;
      transaction_count: number;
      percentage: number;
    }>;
    filters_applied: {
      start_date: string;
      end_date: string;
    };
  } | null;
  salesByPaymentMethod: {
    payment_methods: Array<{
      method: string;
      transaction_count: number;
      total_amount: number;
      percentage: number;
    }>;
    filters_applied: {
      start_date: string;
      end_date: string;
    };
  } | null;
  productPerformance: {
    top_products: Array<{
      id: number;
      name: string;
      category: string;
      quantity_sold: number;
      revenue: number;
      stock_level: number;
      stock_status: string;
    }>;
    low_stock_products: Array<{
      id: number;
      name: string;
      category: string;
      stock_level: number;
      stock_status: string;
    }>;
    filters_applied: {
      start_date: string;
      end_date: string;
      category?: string;
      limit?: number;
    };
  } | null;
}

export default function ReportsPage() {
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState('week');
  const [startDate, setStartDate] = useState<Date | null>(subDays(new Date(), 7));
  const [endDate, setEndDate] = useState<Date | null>(new Date());

  // API data state
  const [reportsData, setReportsData] = useState<ReportsData>({
    salesSummary: null,
    salesByCategory: null,
    salesByPaymentMethod: null,
    productPerformance: null,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const { members, transactions } = usePosStore();
  const { theme } = useTheme();

  const isDarkMode = theme === 'dark';

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Fetch reports data
  const fetchReportsData = useCallback(async () => {
    if (!startDate || !endDate) return;

    try {
      setLoading(true);
      setError(null);

      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');

      const [salesSummaryResponse, salesByCategoryResponse, salesByPaymentMethodResponse, productPerformanceResponse] = await Promise.all([
        reportService.getSalesSummary({
          start_date: startDateStr,
          end_date: endDateStr
        }),
        reportService.getSalesByCategory({
          start_date: startDateStr,
          end_date: endDateStr
        }),
        reportService.getSalesByPaymentMethod({
          start_date: startDateStr,
          end_date: endDateStr
        }),
        reportService.getProductPerformance({
          start_date: startDateStr,
          end_date: endDateStr,
          limit: 10
        })
      ]);

      setReportsData({
        salesSummary: salesSummaryResponse.success ? salesSummaryResponse.data : null,
        salesByCategory: salesByCategoryResponse.success ? salesByCategoryResponse.data : null,
        salesByPaymentMethod: salesByPaymentMethodResponse.success ? salesByPaymentMethodResponse.data : null,
        productPerformance: productPerformanceResponse.success ? productPerformanceResponse.data : null,
      });

      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching reports data:', err);
      setError('Failed to load reports data. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [startDate, endDate]);

  // Initial data fetch
  useEffect(() => {
    fetchReportsData();
  }, [startDate, endDate, fetchReportsData]);

  const handleTimeRangeChange = (event: SelectChangeEvent) => {
    const value = event.target.value;
    setTimeRange(value);

    const today = new Date();

    switch (value) {
      case 'today':
        setStartDate(today);
        setEndDate(today);
        break;
      case 'week':
        setStartDate(subDays(today, 7));
        setEndDate(today);
        break;
      case 'month':
        setStartDate(startOfMonth(today));
        setEndDate(endOfMonth(today));
        break;
      case 'year':
        setStartDate(new Date(today.getFullYear(), 0, 1));
        setEndDate(new Date(today.getFullYear(), 11, 31));
        break;
      case 'custom':
        // Keep current custom dates
        break;
    }
  };

  // Generate sales data for the selected time range
  const generateSalesData = () => {
    // For now, we'll create a simple daily breakdown based on total sales
    // In a more advanced implementation, you could add a daily sales breakdown endpoint
    const totalSales = reportsData.salesSummary?.total_revenue || 0;

    // Generate mock daily data based on total sales (this could be replaced with real daily data)
    const labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const avgDailySales = totalSales / 7; // Simple average
    const salesData = labels.map(() => {
      // Add some variation to make it look realistic
      const variation = (Math.random() - 0.5) * 0.4; // ±20% variation
      return Math.max(0, avgDailySales * (1 + variation));
    });

    return {
      labels,
      datasets: [
        {
          label: 'Sales',
          data: salesData,
          backgroundColor: isDarkMode ? 'rgba(75, 192, 192, 0.5)' : 'rgba(54, 162, 235, 0.5)',
          borderColor: isDarkMode ? 'rgba(75, 192, 192, 1)' : 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        },
      ],
    };
  };

  // Generate product sales data
  const generateProductSalesData = () => {
    const topProducts = reportsData.productPerformance?.top_products || [];

    const labels = topProducts.map(product => product.name);
    const quantitiesSold = topProducts.map(product => product.quantity_sold);

    // Default colors, can be expanded if more than 4 products are common
    const defaultBackgroundColors = isDarkMode ? [
      'rgba(75, 192, 192, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
      'rgba(255, 99, 132, 0.6)',
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 206, 86, 0.6)',
    ] : [
      'rgba(255, 99, 132, 0.6)',
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(75, 192, 192, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
    ];
    const defaultBorderColors = defaultBackgroundColors.map(color => color.replace('0.6', '1'));

    return {
      labels,
      datasets: [
        {
          label: 'Units Sold',
          data: quantitiesSold,
          backgroundColor: labels.map((_, i) => defaultBackgroundColors[i % defaultBackgroundColors.length]),
          borderColor: labels.map((_, i) => defaultBorderColors[i % defaultBorderColors.length]),
          borderWidth: 1,
        },
      ],
    };
  };

  // Generate category sales data
  const generateCategorySalesData = () => {
    const categories = reportsData.salesByCategory?.categories || [];

    const labels = categories.map(category => category.category);
    const dataForChart = categories.map(category => category.total_amount);

    // Default colors, can be expanded if more categories are common
    const defaultBackgroundColors = isDarkMode ? [
      'rgba(75, 192, 192, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
      'rgba(255, 99, 132, 0.6)',
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(128, 0, 128, 0.6)', // Purple
      'rgba(0, 128, 0, 0.6)'    // Green
    ] : [
      'rgba(255, 99, 132, 0.6)',
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(75, 192, 192, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
      'rgba(255, 0, 255, 0.6)', // Magenta
      'rgba(0, 255, 0, 0.6)'    // Lime
    ];
    const defaultBorderColors = defaultBackgroundColors.map(color => color.replace('0.6', '1'));

    return {
      labels,
      datasets: [
        {
          label: 'Sales by Category (Revenue)',
          data: dataForChart,
          backgroundColor: labels.map((_, i) => defaultBackgroundColors[i % defaultBackgroundColors.length]),
          borderColor: labels.map((_, i) => defaultBorderColors[i % defaultBorderColors.length]),
          borderWidth: 1,
        },
      ],
    };
  };

  // Generate payment method data
  const generatePaymentMethodData = () => {
    const paymentMethods = reportsData.salesByPaymentMethod?.payment_methods || [];

    return {
      labels: paymentMethods.map(method =>
        method.method.charAt(0).toUpperCase() + method.method.slice(1)
      ),
      datasets: [
        {
          data: paymentMethods.map(method => method.total_amount),
          backgroundColor: isDarkMode ? [
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
          ] : [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
          ],
          borderColor: isDarkMode ? [
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
          ] : [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4">Reports & Analytics</Typography>
          {lastUpdated && (
            <Typography variant="body2" color="text.secondary">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </Typography>
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Button
            variant="outlined"
            startIcon={loading ? <CircularProgress size={16} /> : <Refresh />}
            onClick={fetchReportsData}
            disabled={loading}
            size="small"
          >
            Refresh
          </Button>

          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={handleTimeRangeChange}
              startAdornment={
                <InputAdornment position="start">
                  <DateRangeIcon fontSize="small" />
                </InputAdornment>
              }
            >
              <MenuItem value="today">Today</MenuItem>
              <MenuItem value="week">Last 7 Days</MenuItem>
              <MenuItem value="month">This Month</MenuItem>
              <MenuItem value="year">This Year</MenuItem>
              <MenuItem value="custom">Custom Range</MenuItem>
            </Select>
          </FormControl>

          {timeRange === 'custom' && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Start Date"
                  value={startDate}
                  onChange={(newValue) => setStartDate(newValue)}
                  slotProps={{ textField: { size: 'small' } }}
                />
                <DatePicker
                  label="End Date"
                  value={endDate}
                  onChange={(newValue) => setEndDate(newValue)}
                  slotProps={{ textField: { size: 'small' } }}
                />
              </LocalizationProvider>
            </Box>
          )}
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Overview Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" sx={{ mb: 2 }}>Overview</Typography>
        <Box sx={{ 
          display: 'flex', 
          flexWrap: 'wrap', 
          gap: 2,
          justifyContent: 'space-between' 
        }}>
          <Card sx={{ 
            flex: '1 1 200px', 
            minWidth: '200px',
            bgcolor: isDarkMode ? 'rgba(0, 123, 255, 0.1)' : 'rgba(0, 123, 255, 0.05)',
            borderLeft: '4px solid',
            borderColor: 'primary.main'
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ 
                  bgcolor: 'primary.main', 
                  color: 'primary.contrastText',
                  p: 1,
                  borderRadius: 1,
                  mr: 2
                }}>
                  <AttachMoney />
                </Box>
                <Box>
                  <Typography color="textSecondary" variant="body2">
                    Total Sales
                  </Typography>
                  <Typography variant="h5">
                    {loading ? (
                      <CircularProgress size={24} />
                    ) : (
                      `$${(reportsData.salesSummary?.total_revenue || 0).toFixed(2)}`
                    )}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>

          <Card sx={{ 
            flex: '1 1 200px', 
            minWidth: '200px',
            bgcolor: isDarkMode ? 'rgba(40, 167, 69, 0.1)' : 'rgba(40, 167, 69, 0.05)',
            borderLeft: '4px solid',
            borderColor: 'success.main'
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ 
                  bgcolor: 'success.main', 
                  color: 'success.contrastText',
                  p: 1,
                  borderRadius: 1,
                  mr: 2
                }}>
                  <TrendingUp />
                </Box>
                <Box>
                  <Typography color="textSecondary" variant="body2">
                    Growth
                  </Typography>
                  <Typography variant="h5">
                    {loading ? (
                      <CircularProgress size={24} />
                    ) : (
                      `+12.5%` // This could be calculated from historical data
                    )}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>

          <Card sx={{ 
            flex: '1 1 200px', 
            minWidth: '200px',
            bgcolor: isDarkMode ? 'rgba(220, 53, 69, 0.1)' : 'rgba(220, 53, 69, 0.05)',
            borderLeft: '4px solid',
            borderColor: 'error.main'
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ 
                  bgcolor: 'error.main', 
                  color: 'error.contrastText',
                  p: 1,
                  borderRadius: 1,
                  mr: 2
                }}>
                  <ShoppingCart />
                </Box>
                <Box>
                  <Typography color="textSecondary" variant="body2">
                    Orders Today
                  </Typography>
                  <Typography variant="h5">
                    {loading ? (
                      <CircularProgress size={24} />
                    ) : (
                      reportsData.salesSummary?.transaction_count || 0
                    )}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>

          <Card sx={{ 
            flex: '1 1 200px', 
            minWidth: '200px',
            bgcolor: isDarkMode ? 'rgba(255, 193, 7, 0.1)' : 'rgba(255, 193, 7, 0.05)',
            borderLeft: '4px solid',
            borderColor: 'warning.main'
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ 
                  bgcolor: 'warning.main', 
                  color: 'warning.contrastText',
                  p: 1,
                  borderRadius: 1,
                  mr: 2
                }}>
                  <People />
                </Box>
                <Box>
                  <Typography color="textSecondary" variant="body2">
                    New Members
                  </Typography>
                  <Typography variant="h5">
                    {loading ? (
                      <CircularProgress size={24} />
                    ) : (
                      members.length // This could be enhanced with new member count from API
                    )}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>

      <Paper sx={{ width: '100%', mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="report tabs"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label="Sales" />
            <Tab label="Products" />
            <Tab label="Payment Methods" />
            <Tab label="Transactions" />
          </Tabs>
        </Box>

        {/* Sales Tab */}
        <TabPanel value={tabValue} index={0}>
          <Card>
            <CardHeader title="Sales Over Time" />
            <Divider />
            <CardContent>
              <Box sx={{ height: 400 }}>
                {loading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <Line
                    data={generateSalesData()}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true,
                          title: {
                            display: true,
                            text: 'Sales ($)'
                          }
                        },
                        x: {
                          title: {
                            display: true,
                            text: 'Day'
                          }
                        }
                      }
                    }}
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Products Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between' }}>
            <Box sx={{ width: { xs: '100%', md: 'calc(50% - 10px)' }, mb: 3 }}>
              <Card>
                <CardHeader title="Top Selling Products" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    {loading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <CircularProgress />
                      </Box>
                    ) : generateProductSalesData().labels.length > 0 ? (
                      <Pie
                        data={generateProductSalesData()}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'right',
                            }
                          }
                        }}
                      />
                    ) : (
                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography variant="body2" color="text.secondary">
                          No product data available
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Box>
            <Box sx={{ width: { xs: '100%', md: 'calc(50% - 10px)' }, mb: 3 }}>
              <Card>
                <CardHeader title="Sales by Category" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    {loading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <CircularProgress />
                      </Box>
                    ) : generateCategorySalesData().labels.length > 0 ? (
                      <Doughnut
                        data={generateCategorySalesData()}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'right',
                            }
                          }
                        }}
                      />
                    ) : (
                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography variant="body2" color="text.secondary">
                          No category data available
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Box>
            <Box sx={{ width: '100%', mb: 3 }}>
              <Card>
                <CardHeader title="Product Inventory Status" />
                <Divider />
                <CardContent>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Product</TableCell>
                          <TableCell>Category</TableCell>
                          <TableCell align="right">Price</TableCell>
                          <TableCell align="right">Stock</TableCell>
                          <TableCell align="right">Status</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {loading ? (
                          <TableRow>
                            <TableCell colSpan={5} align="center">
                              <CircularProgress />
                            </TableCell>
                          </TableRow>
                        ) : reportsData.productPerformance?.top_products ? (
                          reportsData.productPerformance.top_products.slice(0, 5).map((product) => (
                            <TableRow key={product.id}>
                              <TableCell>{product.name}</TableCell>
                              <TableCell>{product.category}</TableCell>
                              <TableCell align="right">${product.revenue.toFixed(2)}</TableCell>
                              <TableCell align="right">{product.stock_level}</TableCell>
                              <TableCell align="right">
                                <Chip
                                  label={product.stock_status === 'in_stock' ? "In Stock" : product.stock_status === 'low_stock' ? "Low Stock" : "Out of Stock"}
                                  color={product.stock_status === 'in_stock' ? "success" : product.stock_status === 'low_stock' ? "warning" : "error"}
                                  size="small"
                                />
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} align="center">
                              <Typography variant="body2" color="text.secondary">
                                No product data available
                              </Typography>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Box>
          </Box>
        </TabPanel>

        {/* Payment Methods Tab */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between' }}>
            <Box sx={{ width: { xs: '100%', md: 'calc(50% - 10px)' }, mb: 3 }}>
              <Card>
                <CardHeader title="Payment Methods" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    {loading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <CircularProgress />
                      </Box>
                    ) : generatePaymentMethodData().labels.length > 0 ? (
                      <Pie
                        data={generatePaymentMethodData()}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'right',
                            }
                          }
                        }}
                      />
                    ) : (
                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <Typography variant="body2" color="text.secondary">
                          No payment method data available
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Box>
            <Box sx={{ width: { xs: '100%', md: 'calc(50% - 10px)' }, mb: 3 }}>
              <Card>
                <CardHeader title="Payment Method Analysis" />
                <Divider />
                <CardContent>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Method</TableCell>
                          <TableCell align="right">Transactions</TableCell>
                          <TableCell align="right">Amount</TableCell>
                          <TableCell align="right">Avg. Transaction</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {loading ? (
                          <TableRow>
                            <TableCell colSpan={4} align="center">
                              <CircularProgress />
                            </TableCell>
                          </TableRow>
                        ) : reportsData.salesByPaymentMethod?.payment_methods ? (
                          reportsData.salesByPaymentMethod.payment_methods.map((method) => {
                            const avgTransaction = method.transaction_count > 0
                              ? method.total_amount / method.transaction_count
                              : 0;

                            return (
                              <TableRow key={method.method}>
                                <TableCell>{method.method.charAt(0).toUpperCase() + method.method.slice(1)}</TableCell>
                                <TableCell align="right">{method.transaction_count}</TableCell>
                                <TableCell align="right">${method.total_amount.toFixed(2)}</TableCell>
                                <TableCell align="right">${avgTransaction.toFixed(2)}</TableCell>
                              </TableRow>
                            );
                          })
                        ) : (
                          <TableRow>
                            <TableCell colSpan={4} align="center">
                              <Typography variant="body2" color="text.secondary">
                                No payment method data available
                              </Typography>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Box>
          </Box>
        </TabPanel>

        {/* Transactions Tab */}
        <TabPanel value={tabValue} index={3}>
          <Card>
            <CardHeader title="Recent Transactions" />
            <Divider />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>Member</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Products</TableCell>
                      <TableCell>Services</TableCell>
                      <TableCell>Resources</TableCell>
                      <TableCell>Payment Method</TableCell>
                      <TableCell align="right">Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {transactions.map((transaction) => {
                      const member = members.find(c => c.id === transaction.memberId);
                      const date = new Date(transaction.createdAt);
                      
                      // New logic for counting items
                      let productCount = 0;
                      let serviceCount = 0;
                      const resourceCount = transaction.sessions.length; // Each session is a resource usage

                      transaction.sessions.forEach(session => {
                        session.products.forEach(p => productCount += p.quantity);
                        session.services.forEach(s => serviceCount += s.quantity);
                      });
                      
                      return (
                        <TableRow key={transaction.id}>
                          <TableCell>#{transaction.id}</TableCell>
                          <TableCell>{transaction.memberName || (member ? member.name : 'Walk-in Member')}</TableCell>
                          <TableCell>{date.toLocaleDateString()}</TableCell>
                          <TableCell align="right">{productCount}</TableCell>
                          <TableCell align="right">{serviceCount}</TableCell>
                          <TableCell align="right">{resourceCount}</TableCell>
                          <TableCell>
                            {transaction.paymentMethod.charAt(0).toUpperCase() + transaction.paymentMethod.slice(1)}
                          </TableCell>
                          <TableCell align="right">${transaction.totalAmount.toFixed(2)}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </TabPanel>
      </Paper>
    </Box>
  );
}
