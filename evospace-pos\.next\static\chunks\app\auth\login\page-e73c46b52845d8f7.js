(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{22439:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(95155),n=s(12115),i=s(35695),a=s(2730),o=s(88242),l=s(14426),c=s(14962),d=s(78449),u=s(33989),h=s(76380),x=s(57515);let f=(0,x.A)((0,r.jsx)("path",{d:"M12 5.9c1.16 0 2.1.94 2.1 2.1s-.94 2.1-2.1 2.1S9.9 9.16 9.9 8s.94-2.1 2.1-2.1m0 9c2.97 0 6.1 1.46 6.1 2.1v1.1H5.9V17c0-.64 3.13-2.1 6.1-2.1M12 4C9.79 4 8 5.79 8 8s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4m0 9c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4"}),"PersonOutline"),m=(0,x.A)((0,r.jsx)("path",{d:"M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2M9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9zm9 14H6V10h12zm-6-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2"}),"LockOutlined");var g=s(55099);let v=(0,x.A)((0,r.jsx)("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20z"}),"ArrowBack");function p(){let[e,t]=(0,n.useState)(""),[s,x]=(0,n.useState)(""),[p,j]=(0,n.useState)(""),[b,y]=(0,n.useState)(""),[S,A]=(0,n.useState)(!1),[N,w]=(0,n.useState)(!1),[P,k]=(0,n.useState)([]),L=(0,i.useRouter)(),{login:H,loadAuthToken:C,authUser:M}=(0,a.A)(),z=async t=>{t.preventDefault(),A(!0),y("");try{let t=await o.Ay.auth.getUserStores({username:e,password:s});if(!t.success){y("Login failed. Please check your credentials."),A(!1);return}let r=t.data.items;k(r),1===r.length?await H(e,s,r[0].id)?L.push("/"):y("Login failed. Please check your credentials."):r.length>1?(j(r[0].id),w(!0)):y("No stores available for this user.")}catch(e){console.error("Login error:",e),y("An unexpected error occurred. Please try again.")}finally{A(!1)}},E=async()=>{if(!p)return void y("Please select a store.");A(!0),y("");try{await H(e,s,Number(p))?L.push("/"):y("Login failed. Please check your credentials and store selection.")}catch(e){console.error("Login error:",e),y("An unexpected error occurred. Please try again.")}finally{A(!1)}};return((0,n.useEffect)(()=>{C()},[C]),(0,n.useEffect)(()=>{M.token&&L.push("/")},[M.token,L]),M.token)?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-900 to-slate-700",children:(0,r.jsx)(l.A,{color:"inherit"})}):(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-900 to-slate-700 p-4",children:(0,r.jsx)(c.A,{in:!0,timeout:700,children:(0,r.jsxs)("div",{className:"w-full max-w-md p-8 space-y-6 bg-white rounded-xl shadow-2xl",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-center text-slate-700",children:"EvoSpace POS Login"}),(0,r.jsx)("form",{onSubmit:z,className:"space-y-6",children:N?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 text-center mb-4",children:["Logged in as: ",(0,r.jsx)("span",{className:"font-medium",children:e})]}),(0,r.jsx)("div",{children:(0,r.jsx)(d.A,{fullWidth:!0,select:!0,id:"store",label:"Select Store",variant:"outlined",value:p,onChange:e=>j(Number(e.target.value)),required:!0,InputProps:{startAdornment:(0,r.jsx)(u.A,{position:"start",children:(0,r.jsx)(g.A,{})})},helperText:"Choose your store location",children:P.map(e=>(0,r.jsx)(h.A,{value:e.id,children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),e.address&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.address})]})},e.id))})}),b&&(0,r.jsx)("p",{className:"text-sm text-red-600 text-center",children:b}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"button",onClick:E,disabled:S||!p,className:"w-full px-4 py-3 font-semibold text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-60 transition-colors duration-300 ease-in-out",children:S?(0,r.jsx)(l.A,{size:24,color:"inherit"}):"Login to Store"})}),(0,r.jsx)("div",{className:"flex justify-center mt-4",children:(0,r.jsxs)("button",{type:"button",onClick:()=>{w(!1),j(""),k([]),y("")},className:"px-4 py-2 font-medium text-indigo-600 bg-white border border-indigo-600 rounded-lg hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-300 ease-in-out flex items-center space-x-2",children:[(0,r.jsx)(v,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Back"})]})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{children:(0,r.jsx)(d.A,{fullWidth:!0,id:"username",label:"Username",variant:"outlined",value:e,onChange:e=>t(e.target.value),required:!0,placeholder:"admin",InputProps:{startAdornment:(0,r.jsx)(u.A,{position:"start",children:(0,r.jsx)(f,{})})}})}),(0,r.jsx)("div",{children:(0,r.jsx)(d.A,{fullWidth:!0,type:"password",id:"password",label:"Password",variant:"outlined",value:s,onChange:e=>x(e.target.value),required:!0,placeholder:"123456",InputProps:{startAdornment:(0,r.jsx)(u.A,{position:"start",children:(0,r.jsx)(m,{})})}})}),b&&(0,r.jsx)("p",{className:"text-sm text-red-600 text-center",children:b}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:S,className:"w-full px-4 py-3 font-semibold text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-60 transition-colors duration-300 ease-in-out",children:S?(0,r.jsx)(l.A,{size:24,color:"inherit"}):"Continue"})})]})})]})})})}},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useServerInsertedHTML")&&s.d(t,{useServerInsertedHTML:function(){return r.useServerInsertedHTML}})},55099:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(57515),n=s(95155);let i=(0,r.A)((0,n.jsx)("path",{d:"M20 4H4v2h16zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6zm-9 4H6v-4h6z"}),"Store")},62518:(e,t,s)=>{Promise.resolve().then(s.bind(s,22439))},65453:(e,t,s)=>{"use strict";s.d(t,{v:()=>l});var r=s(12115);let n=e=>{let t,s=new Set,r=(e,r)=>{let n="function"==typeof e?e(t):e;if(!Object.is(n,t)){let e=t;t=(null!=r?r:"object"!=typeof n||null===n)?n:Object.assign({},t,n),s.forEach(s=>s(t,e))}},n=()=>t,i={setState:r,getState:n,getInitialState:()=>a,subscribe:e=>(s.add(e),()=>s.delete(e))},a=t=e(r,n,i);return i},i=e=>e?n(e):n,a=e=>e,o=e=>{let t=i(e),s=e=>(function(e,t=a){let s=r.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return r.useDebugValue(s),s})(t,e);return Object.assign(s,t),s},l=e=>e?o(e):o}},e=>{var t=t=>e(e.s=t);e.O(0,[319,692,317,730,441,684,358],()=>t(62518)),_N_E=e.O()}]);