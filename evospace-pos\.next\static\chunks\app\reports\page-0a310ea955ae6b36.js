(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[22],{35884:(e,s,r)=>{"use strict";r.d(s,{D:()=>l}),r(95155);var a=r(12115);let t=(0,a.createContext)({mode:"light",toggleMode:()=>{},theme:"light",toggleTheme:()=>{}}),l=()=>(0,a.useContext)(t)},57004:(e,s,r)=>{Promise.resolve().then(r.bind(r,79680))},79680:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>H});var a=r(95155),t=r(12115),l=r(54581),n=r(700),i=r(68534),d=r(14426),o=r(27088),c=r(17348),x=r(68104),h=r(33989),j=r(76380),A=r(92302),g=r(41218),b=r(16632),m=r(18407),u=r(49994),p=r(18096),y=r(65221),v=r(74964),f=r(44296),S=r(17976),_=r(56033),C=r(90404),w=r(64263),P=r(99776),k=r(41101),M=r(49614),T=r(61090),I=r(86662),D=r(37857),R=r(6643),F=r(63645),W=r(20250),B=r(98028),L=r(8025),N=r(64065),z=r(32502),E=r(9107),$=r(86335),O=r(53231),q=r(32944),G=r(2730),U=r(35884),Y=r(88242);function Z(e){let{children:s,value:r,index:t,...n}=e;return(0,a.jsx)("div",{role:"tabpanel",hidden:r!==t,id:"report-tabpanel-".concat(t),"aria-labelledby":"report-tab-".concat(t),...n,children:r===t&&(0,a.jsx)(l.A,{sx:{pt:3},children:s})})}function H(){var e,s,r,z;let[H,J]=(0,t.useState)(0),[K,Q]=(0,t.useState)("week"),[V,X]=(0,t.useState)((0,E.e)(new Date,7)),[ee,es]=(0,t.useState)(new Date),[er,ea]=(0,t.useState)({salesSummary:null,salesByCategory:null,salesByPaymentMethod:null,productPerformance:null}),[et,el]=(0,t.useState)(!0),[en,ei]=(0,t.useState)(null),[ed,eo]=(0,t.useState)(null),{members:ec}=(0,G.A)(),{theme:ex}=(0,U.D)(),eh="dark"===ex,ej=async()=>{if(V&&ee)try{el(!0),ei(null);let e=(0,$.GP)(V,"yyyy-MM-dd"),s=(0,$.GP)(ee,"yyyy-MM-dd"),[r,a,t,l]=await Promise.all([Y.SL.getSalesSummary({start_date:e,end_date:s}),Y.SL.getSalesByCategory({start_date:e,end_date:s}),Y.SL.getSalesByPaymentMethod({start_date:e,end_date:s}),Y.SL.getProductPerformance({start_date:e,end_date:s,limit:10})]);ea({salesSummary:r.success?r.data:null,salesByCategory:a.success?a.data:null,salesByPaymentMethod:t.success?t.data:null,productPerformance:l.success?l.data:null}),eo(new Date)}catch(e){console.error("Error fetching reports data:",e),ei("Failed to load reports data. Please try again.")}finally{el(!1)}};(0,t.useEffect)(()=>{ej()},[V,ee,ej]);let eA=()=>{var e;let s=(null==(e=er.productPerformance)?void 0:e.top_products)||[],r=s.map(e=>e.name),a=s.map(e=>e.quantity_sold),t=eh?["rgba(75, 192, 192, 0.6)","rgba(153, 102, 255, 0.6)","rgba(255, 159, 64, 0.6)","rgba(255, 99, 132, 0.6)","rgba(54, 162, 235, 0.6)","rgba(255, 206, 86, 0.6)"]:["rgba(255, 99, 132, 0.6)","rgba(54, 162, 235, 0.6)","rgba(255, 206, 86, 0.6)","rgba(75, 192, 192, 0.6)","rgba(153, 102, 255, 0.6)","rgba(255, 159, 64, 0.6)"],l=t.map(e=>e.replace("0.6","1"));return{labels:r,datasets:[{label:"Units Sold",data:a,backgroundColor:r.map((e,s)=>t[s%t.length]),borderColor:r.map((e,s)=>l[s%l.length]),borderWidth:1}]}},eg=()=>{var e;let s=(null==(e=er.salesByCategory)?void 0:e.categories)||[],r=s.map(e=>e.category),a=s.map(e=>e.total_amount),t=eh?["rgba(75, 192, 192, 0.6)","rgba(153, 102, 255, 0.6)","rgba(255, 159, 64, 0.6)","rgba(255, 99, 132, 0.6)","rgba(54, 162, 235, 0.6)","rgba(255, 206, 86, 0.6)","rgba(128, 0, 128, 0.6)","rgba(0, 128, 0, 0.6)"]:["rgba(255, 99, 132, 0.6)","rgba(54, 162, 235, 0.6)","rgba(255, 206, 86, 0.6)","rgba(75, 192, 192, 0.6)","rgba(153, 102, 255, 0.6)","rgba(255, 159, 64, 0.6)","rgba(255, 0, 255, 0.6)","rgba(0, 255, 0, 0.6)"],l=t.map(e=>e.replace("0.6","1"));return{labels:r,datasets:[{label:"Sales by Category (Revenue)",data:a,backgroundColor:r.map((e,s)=>t[s%t.length]),borderColor:r.map((e,s)=>l[s%l.length]),borderWidth:1}]}},eb=()=>{var e;let s=(null==(e=er.salesByPaymentMethod)?void 0:e.payment_methods)||[];return{labels:s.map(e=>e.method.charAt(0).toUpperCase()+e.method.slice(1)),datasets:[{data:s.map(e=>e.total_amount),backgroundColor:eh?["rgba(75, 192, 192, 0.6)","rgba(153, 102, 255, 0.6)","rgba(255, 159, 64, 0.6)","rgba(255, 99, 132, 0.6)","rgba(54, 162, 235, 0.6)"]:["rgba(255, 99, 132, 0.6)","rgba(54, 162, 235, 0.6)","rgba(255, 206, 86, 0.6)","rgba(75, 192, 192, 0.6)","rgba(153, 102, 255, 0.6)"],borderColor:eh?["rgba(75, 192, 192, 1)","rgba(153, 102, 255, 1)","rgba(255, 159, 64, 1)","rgba(255, 99, 132, 1)","rgba(54, 162, 235, 1)"]:["rgba(255, 99, 132, 1)","rgba(54, 162, 235, 1)","rgba(255, 206, 86, 1)","rgba(75, 192, 192, 1)","rgba(153, 102, 255, 1)"],borderWidth:1}]}};return(0,a.jsxs)(l.A,{sx:{flexGrow:1},children:[(0,a.jsxs)(l.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,a.jsxs)(l.A,{children:[(0,a.jsx)(n.A,{variant:"h4",children:"Reports & Analytics"}),ed&&(0,a.jsxs)(n.A,{variant:"body2",color:"text.secondary",children:["Last updated: ",ed.toLocaleTimeString()]})]}),(0,a.jsxs)(l.A,{sx:{display:"flex",gap:2,alignItems:"center"},children:[(0,a.jsx)(i.A,{variant:"outlined",startIcon:et?(0,a.jsx)(d.A,{size:16}):(0,a.jsx)(D.A,{}),onClick:ej,disabled:et,size:"small",children:"Refresh"}),(0,a.jsxs)(o.A,{size:"small",sx:{minWidth:150},children:[(0,a.jsx)(c.A,{children:"Time Range"}),(0,a.jsxs)(x.A,{value:K,label:"Time Range",onChange:e=>{let s=e.target.value;Q(s);let r=new Date;switch(s){case"today":X(r),es(r);break;case"week":X((0,E.e)(r,7)),es(r);break;case"month":X((0,O.w)(r)),es((0,q.p)(r));break;case"year":X(new Date(r.getFullYear(),0,1)),es(new Date(r.getFullYear(),11,31))}},startAdornment:(0,a.jsx)(h.A,{position:"start",children:(0,a.jsx)(R.A,{fontSize:"small"})}),children:[(0,a.jsx)(j.A,{value:"today",children:"Today"}),(0,a.jsx)(j.A,{value:"week",children:"Last 7 Days"}),(0,a.jsx)(j.A,{value:"month",children:"This Month"}),(0,a.jsx)(j.A,{value:"year",children:"This Year"}),(0,a.jsx)(j.A,{value:"custom",children:"Custom Range"})]})]}),"custom"===K&&(0,a.jsx)(l.A,{sx:{display:"flex",gap:1},children:(0,a.jsxs)(T.$,{dateAdapter:I.h,children:[(0,a.jsx)(M.l,{label:"Start Date",value:V,onChange:e=>X(e),slotProps:{textField:{size:"small"}}}),(0,a.jsx)(M.l,{label:"End Date",value:ee,onChange:e=>es(e),slotProps:{textField:{size:"small"}}})]})})]})]}),en&&(0,a.jsx)(A.A,{severity:"error",sx:{mb:3},onClose:()=>ei(null),children:en}),(0,a.jsxs)(l.A,{sx:{mb:4},children:[(0,a.jsx)(n.A,{variant:"h5",sx:{mb:2},children:"Overview"}),(0,a.jsxs)(l.A,{sx:{display:"flex",flexWrap:"wrap",gap:2,justifyContent:"space-between"},children:[(0,a.jsx)(g.A,{sx:{flex:"1 1 200px",minWidth:"200px",bgcolor:eh?"rgba(0, 123, 255, 0.1)":"rgba(0, 123, 255, 0.05)",borderLeft:"4px solid",borderColor:"primary.main"},children:(0,a.jsx)(b.A,{children:(0,a.jsxs)(l.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(l.A,{sx:{bgcolor:"primary.main",color:"primary.contrastText",p:1,borderRadius:1,mr:2},children:(0,a.jsx)(F.A,{})}),(0,a.jsxs)(l.A,{children:[(0,a.jsx)(n.A,{color:"textSecondary",variant:"body2",children:"Total Sales"}),(0,a.jsx)(n.A,{variant:"h5",children:et?(0,a.jsx)(d.A,{size:24}):"$".concat(((null==(e=er.salesSummary)?void 0:e.total_sales_revenue)||0).toFixed(2))})]})]})})}),(0,a.jsx)(g.A,{sx:{flex:"1 1 200px",minWidth:"200px",bgcolor:eh?"rgba(40, 167, 69, 0.1)":"rgba(40, 167, 69, 0.05)",borderLeft:"4px solid",borderColor:"success.main"},children:(0,a.jsx)(b.A,{children:(0,a.jsxs)(l.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(l.A,{sx:{bgcolor:"success.main",color:"success.contrastText",p:1,borderRadius:1,mr:2},children:(0,a.jsx)(W.A,{})}),(0,a.jsxs)(l.A,{children:[(0,a.jsx)(n.A,{color:"textSecondary",variant:"body2",children:"Growth"}),(0,a.jsx)(n.A,{variant:"h5",children:et?(0,a.jsx)(d.A,{size:24}):"+12.5%"})]})]})})}),(0,a.jsx)(g.A,{sx:{flex:"1 1 200px",minWidth:"200px",bgcolor:eh?"rgba(220, 53, 69, 0.1)":"rgba(220, 53, 69, 0.05)",borderLeft:"4px solid",borderColor:"error.main"},children:(0,a.jsx)(b.A,{children:(0,a.jsxs)(l.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(l.A,{sx:{bgcolor:"error.main",color:"error.contrastText",p:1,borderRadius:1,mr:2},children:(0,a.jsx)(B.A,{})}),(0,a.jsxs)(l.A,{children:[(0,a.jsx)(n.A,{color:"textSecondary",variant:"body2",children:"Orders Today"}),(0,a.jsx)(n.A,{variant:"h5",children:et?(0,a.jsx)(d.A,{size:24}):(null==(s=er.salesSummary)?void 0:s.number_of_transactions)||0})]})]})})}),(0,a.jsx)(g.A,{sx:{flex:"1 1 200px",minWidth:"200px",bgcolor:eh?"rgba(255, 193, 7, 0.1)":"rgba(255, 193, 7, 0.05)",borderLeft:"4px solid",borderColor:"warning.main"},children:(0,a.jsx)(b.A,{children:(0,a.jsxs)(l.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(l.A,{sx:{bgcolor:"warning.main",color:"warning.contrastText",p:1,borderRadius:1,mr:2},children:(0,a.jsx)(L.A,{})}),(0,a.jsxs)(l.A,{children:[(0,a.jsx)(n.A,{color:"textSecondary",variant:"body2",children:"New Members"}),(0,a.jsx)(n.A,{variant:"h5",children:et?(0,a.jsx)(d.A,{size:24}):ec.length})]})]})})})]})]}),(0,a.jsxs)(m.A,{sx:{width:"100%",mb:3},children:[(0,a.jsx)(l.A,{sx:{borderBottom:1,borderColor:"divider"},children:(0,a.jsxs)(u.A,{value:H,onChange:(e,s)=>{J(s)},"aria-label":"report tabs",variant:"scrollable",scrollButtons:"auto",children:[(0,a.jsx)(p.A,{label:"Sales"}),(0,a.jsx)(p.A,{label:"Products"}),(0,a.jsx)(p.A,{label:"Payment Methods"}),(0,a.jsx)(p.A,{label:"Transactions"})]})}),(0,a.jsx)(Z,{value:H,index:0,children:(0,a.jsxs)(g.A,{children:[(0,a.jsx)(y.A,{title:"Sales Over Time"}),(0,a.jsx)(v.A,{}),(0,a.jsx)(b.A,{children:(0,a.jsx)(l.A,{sx:{height:400},children:et?(0,a.jsx)(l.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,a.jsx)(d.A,{})}):(0,a.jsx)(N.N1,{data:(()=>{var e,s;let r=(null==(e=er.salesSummary)?void 0:e.total_sales_revenue)||0;null==(s=er.salesSummary)||s.number_of_transactions;let a=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],t=r/7,l=a.map((e,s)=>Math.max(0,t*(1+(Math.random()-.5)*.4)));return{labels:a,datasets:[{label:"Sales",data:l,backgroundColor:eh?"rgba(75, 192, 192, 0.5)":"rgba(54, 162, 235, 0.5)",borderColor:eh?"rgba(75, 192, 192, 1)":"rgba(54, 162, 235, 1)",borderWidth:1}]}})(),options:{responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0,title:{display:!0,text:"Sales ($)"}},x:{title:{display:!0,text:"Day"}}}}})})})]})}),(0,a.jsx)(Z,{value:H,index:1,children:(0,a.jsxs)(l.A,{sx:{display:"flex",flexWrap:"wrap",justifyContent:"space-between"},children:[(0,a.jsx)(l.A,{sx:{width:{xs:"100%",md:"calc(50% - 10px)"},mb:3},children:(0,a.jsxs)(g.A,{children:[(0,a.jsx)(y.A,{title:"Top Selling Products"}),(0,a.jsx)(v.A,{}),(0,a.jsx)(b.A,{children:(0,a.jsx)(l.A,{sx:{height:300},children:et?(0,a.jsx)(l.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,a.jsx)(d.A,{})}):eA().labels.length>0?(0,a.jsx)(N.Fq,{data:eA(),options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"}}}}):(0,a.jsx)(l.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,a.jsx)(n.A,{variant:"body2",color:"text.secondary",children:"No product data available"})})})})]})}),(0,a.jsx)(l.A,{sx:{width:{xs:"100%",md:"calc(50% - 10px)"},mb:3},children:(0,a.jsxs)(g.A,{children:[(0,a.jsx)(y.A,{title:"Sales by Category"}),(0,a.jsx)(v.A,{}),(0,a.jsx)(b.A,{children:(0,a.jsx)(l.A,{sx:{height:300},children:et?(0,a.jsx)(l.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,a.jsx)(d.A,{})}):eg().labels.length>0?(0,a.jsx)(N.nu,{data:eg(),options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"}}}}):(0,a.jsx)(l.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,a.jsx)(n.A,{variant:"body2",color:"text.secondary",children:"No category data available"})})})})]})}),(0,a.jsx)(l.A,{sx:{width:"100%",mb:3},children:(0,a.jsxs)(g.A,{children:[(0,a.jsx)(y.A,{title:"Product Inventory Status"}),(0,a.jsx)(v.A,{}),(0,a.jsx)(b.A,{children:(0,a.jsx)(f.A,{children:(0,a.jsxs)(S.A,{children:[(0,a.jsx)(_.A,{children:(0,a.jsxs)(C.A,{children:[(0,a.jsx)(w.A,{children:"Product"}),(0,a.jsx)(w.A,{children:"Category"}),(0,a.jsx)(w.A,{align:"right",children:"Price"}),(0,a.jsx)(w.A,{align:"right",children:"Stock"}),(0,a.jsx)(w.A,{align:"right",children:"Status"})]})}),(0,a.jsx)(P.A,{children:et?(0,a.jsx)(C.A,{children:(0,a.jsx)(w.A,{colSpan:5,align:"center",children:(0,a.jsx)(d.A,{})})}):(null==(r=er.productPerformance)?void 0:r.top_products)?er.productPerformance.top_products.slice(0,5).map(e=>(0,a.jsxs)(C.A,{children:[(0,a.jsx)(w.A,{children:e.name}),(0,a.jsx)(w.A,{children:e.category}),(0,a.jsxs)(w.A,{align:"right",children:["$",e.revenue.toFixed(2)]}),(0,a.jsx)(w.A,{align:"right",children:e.stock_level}),(0,a.jsx)(w.A,{align:"right",children:(0,a.jsx)(k.A,{label:"in_stock"===e.stock_status?"In Stock":"low_stock"===e.stock_status?"Low Stock":"Out of Stock",color:"in_stock"===e.stock_status?"success":"low_stock"===e.stock_status?"warning":"error",size:"small"})})]},e.id)):(0,a.jsx)(C.A,{children:(0,a.jsx)(w.A,{colSpan:5,align:"center",children:(0,a.jsx)(n.A,{variant:"body2",color:"text.secondary",children:"No product data available"})})})})]})})})]})})]})}),(0,a.jsx)(Z,{value:H,index:2,children:(0,a.jsxs)(l.A,{sx:{display:"flex",flexWrap:"wrap",justifyContent:"space-between"},children:[(0,a.jsx)(l.A,{sx:{width:{xs:"100%",md:"calc(50% - 10px)"},mb:3},children:(0,a.jsxs)(g.A,{children:[(0,a.jsx)(y.A,{title:"Payment Methods"}),(0,a.jsx)(v.A,{}),(0,a.jsx)(b.A,{children:(0,a.jsx)(l.A,{sx:{height:300},children:et?(0,a.jsx)(l.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,a.jsx)(d.A,{})}):eb().labels.length>0?(0,a.jsx)(N.Fq,{data:eb(),options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"}}}}):(0,a.jsx)(l.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:(0,a.jsx)(n.A,{variant:"body2",color:"text.secondary",children:"No payment method data available"})})})})]})}),(0,a.jsx)(l.A,{sx:{width:{xs:"100%",md:"calc(50% - 10px)"},mb:3},children:(0,a.jsxs)(g.A,{children:[(0,a.jsx)(y.A,{title:"Payment Method Analysis"}),(0,a.jsx)(v.A,{}),(0,a.jsx)(b.A,{children:(0,a.jsx)(f.A,{children:(0,a.jsxs)(S.A,{children:[(0,a.jsx)(_.A,{children:(0,a.jsxs)(C.A,{children:[(0,a.jsx)(w.A,{children:"Method"}),(0,a.jsx)(w.A,{align:"right",children:"Transactions"}),(0,a.jsx)(w.A,{align:"right",children:"Amount"}),(0,a.jsx)(w.A,{align:"right",children:"Avg. Transaction"})]})}),(0,a.jsx)(P.A,{children:et?(0,a.jsx)(C.A,{children:(0,a.jsx)(w.A,{colSpan:4,align:"center",children:(0,a.jsx)(d.A,{})})}):(null==(z=er.salesByPaymentMethod)?void 0:z.payment_methods)?er.salesByPaymentMethod.payment_methods.map(e=>{let s=e.transaction_count>0?e.total_amount/e.transaction_count:0;return(0,a.jsxs)(C.A,{children:[(0,a.jsx)(w.A,{children:e.method.charAt(0).toUpperCase()+e.method.slice(1)}),(0,a.jsx)(w.A,{align:"right",children:e.transaction_count}),(0,a.jsxs)(w.A,{align:"right",children:["$",e.total_amount.toFixed(2)]}),(0,a.jsxs)(w.A,{align:"right",children:["$",s.toFixed(2)]})]},e.method)}):(0,a.jsx)(C.A,{children:(0,a.jsx)(w.A,{colSpan:4,align:"center",children:(0,a.jsx)(n.A,{variant:"body2",color:"text.secondary",children:"No payment method data available"})})})})]})})})]})})]})}),(0,a.jsx)(Z,{value:H,index:3,children:(0,a.jsxs)(g.A,{children:[(0,a.jsx)(y.A,{title:"Recent Transactions"}),(0,a.jsx)(v.A,{}),(0,a.jsx)(b.A,{children:(0,a.jsx)(f.A,{children:(0,a.jsxs)(S.A,{children:[(0,a.jsx)(_.A,{children:(0,a.jsxs)(C.A,{children:[(0,a.jsx)(w.A,{children:"ID"}),(0,a.jsx)(w.A,{children:"Member"}),(0,a.jsx)(w.A,{children:"Date"}),(0,a.jsx)(w.A,{children:"Products"}),(0,a.jsx)(w.A,{children:"Services"}),(0,a.jsx)(w.A,{children:"Resources"}),(0,a.jsx)(w.A,{children:"Payment Method"}),(0,a.jsx)(w.A,{align:"right",children:"Total"})]})}),(0,a.jsx)(P.A,{children:transactions.map(e=>{let s=ec.find(s=>s.id===e.memberId),r=new Date(e.createdAt),t=0,l=0,n=e.sessions.length;return e.sessions.forEach(e=>{e.products.forEach(e=>t+=e.quantity),e.services.forEach(e=>l+=e.quantity)}),(0,a.jsxs)(C.A,{children:[(0,a.jsxs)(w.A,{children:["#",e.id]}),(0,a.jsx)(w.A,{children:e.memberName||(s?s.name:"Walk-in Member")}),(0,a.jsx)(w.A,{children:r.toLocaleDateString()}),(0,a.jsx)(w.A,{align:"right",children:t}),(0,a.jsx)(w.A,{align:"right",children:l}),(0,a.jsx)(w.A,{align:"right",children:n}),(0,a.jsx)(w.A,{children:e.paymentMethod.charAt(0).toUpperCase()+e.paymentMethod.slice(1)}),(0,a.jsxs)(w.A,{align:"right",children:["$",e.totalAmount.toFixed(2)]})]},e.id)})})]})})})]})})]})]})}z.t1.register(z.PP,z.kc,z.FN,z.No,z.E8,z.hE,z.m_,z.s$,z.Bs)}},e=>{var s=s=>e(e.s=s);e.O(0,[647,319,692,317,687,257,700,13,159,216,622,425,370,730,441,684,358],()=>s(57004)),_N_E=e.O()}]);