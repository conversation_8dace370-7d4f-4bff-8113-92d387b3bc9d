(()=>{var e={};e.id=752,e.ids=[752],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3297:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(23428),s=r(60687);let o=(0,a.A)((0,s.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"Add")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16400:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eC});var a=r(60687),s=r(43210),o=r(88931),n=r(41434),l=r(12362),i=r(24296),d=r(90764),c=r(23789),u=r(52260),p=r(41629),h=r(47651),x=r(27674),g=r(16184),m=r(59985),b=r(23428);let v=(0,b.A)((0,a.jsx)("path",{d:"M14 6v15H3v-2h2V3h9v1h5v15h2v2h-4V6zm-4 5v2h2v-2z"}),"MeetingRoom"),A=(0,b.A)((0,a.jsx)("path",{d:"M20 3H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h6v2H8v2h8v-2h-2v-2h6c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2"}),"DesktopWindows"),f=(0,b.A)((0,a.jsx)("path",{d:"M4 6h18V4H4c-1.1 0-2 .9-2 2v11H0v3h14v-3H4zm19 2h-6c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h6c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1m-1 9h-4v-7h4z"}),"Devices"),y=(0,b.A)((0,a.jsx)("path",{d:"M10 10.02h5V21h-5zM17 21h3c1.1 0 2-.9 2-2v-9h-5zm3-18H5c-1.1 0-2 .9-2 2v3h19V5c0-1.1-.9-2-2-2M3 19c0 1.1.9 2 2 2h3V10H3z"}),"TableChart");var j=r(28840),C=r(37860),w=r(21360),S=r(54534),k=r(87088),R=r(51067),$=r(16393),z=r(51711),M=r(49384),T=r(99282),B=r(13555),I=r(45258),D=r(84754),W=r(61543),F=r(4144),P=r(82816);function V(e){return(0,P.Ay)("MuiToggleButtonGroup",e)}let O=(0,F.A)("MuiToggleButtonGroup",["root","selected","horizontal","vertical","disabled","grouped","groupedHorizontal","groupedVertical","fullWidth","firstButton","lastButton","middleButton"]),H=s.createContext({}),_=s.createContext(void 0);function E(e){return(0,P.Ay)("MuiToggleButton",e)}let L=(0,F.A)("MuiToggleButton",["root","disabled","selected","standard","primary","secondary","sizeSmall","sizeMedium","sizeLarge","fullWidth"]),N=e=>{let{classes:t,orientation:r,fullWidth:a,disabled:s}=e,o={root:["root",r,a&&"fullWidth"],grouped:["grouped",`grouped${(0,W.A)(r)}`,s&&"disabled"],firstButton:["firstButton"],lastButton:["lastButton"],middleButton:["middleButton"]};return(0,T.A)(o,V,t)},q=(0,B.Ay)("div",{name:"MuiToggleButtonGroup",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{[`& .${O.grouped}`]:t.grouped},{[`& .${O.grouped}`]:t[`grouped${(0,W.A)(r.orientation)}`]},{[`& .${O.firstButton}`]:t.firstButton},{[`& .${O.lastButton}`]:t.lastButton},{[`& .${O.middleButton}`]:t.middleButton},t.root,"vertical"===r.orientation&&t.vertical,r.fullWidth&&t.fullWidth]}})((0,I.A)(({theme:e})=>({display:"inline-flex",borderRadius:(e.vars||e).shape.borderRadius,variants:[{props:{orientation:"vertical"},style:{flexDirection:"column",[`& .${O.grouped}`]:{[`&.${O.selected} + .${O.grouped}.${O.selected}`]:{borderTop:0,marginTop:0}},[`& .${O.firstButton},& .${O.middleButton}`]:{borderBottomLeftRadius:0,borderBottomRightRadius:0},[`& .${O.lastButton},& .${O.middleButton}`]:{marginTop:-1,borderTop:"1px solid transparent",borderTopLeftRadius:0,borderTopRightRadius:0},[`& .${O.lastButton}.${L.disabled},& .${O.middleButton}.${L.disabled}`]:{borderTop:"1px solid transparent"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{orientation:"horizontal"},style:{[`& .${O.grouped}`]:{[`&.${O.selected} + .${O.grouped}.${O.selected}`]:{borderLeft:0,marginLeft:0}},[`& .${O.firstButton},& .${O.middleButton}`]:{borderTopRightRadius:0,borderBottomRightRadius:0},[`& .${O.lastButton},& .${O.middleButton}`]:{marginLeft:-1,borderLeft:"1px solid transparent",borderTopLeftRadius:0,borderBottomLeftRadius:0},[`& .${O.lastButton}.${L.disabled},& .${O.middleButton}.${L.disabled}`]:{borderLeft:"1px solid transparent"}}}]}))),G=s.forwardRef(function(e,t){let r=(0,D.b)({props:e,name:"MuiToggleButtonGroup"}),{children:o,className:n,color:l="standard",disabled:i=!1,exclusive:d=!1,fullWidth:c=!1,onChange:u,orientation:p="horizontal",size:h="medium",value:x,...g}=r,m={...r,disabled:i,fullWidth:c,orientation:p,size:h},b=N(m),v=s.useCallback((e,t)=>{let r;if(!u)return;let a=x&&x.indexOf(t);x&&a>=0?(r=x.slice()).splice(a,1):r=x?x.concat(t):[t],u(e,r)},[u,x]),A=s.useCallback((e,t)=>{u&&u(e,x===t?null:t)},[u,x]),f=s.useMemo(()=>({className:b.grouped,onChange:d?A:v,value:x,size:h,fullWidth:c,color:l,disabled:i}),[b.grouped,d,A,v,x,h,c,l,i]),y=s.Children.toArray(o).filter(e=>s.isValidElement(e)),j=y.length,C=e=>{let t=0===e,r=e===j-1;return t&&r?"":t?b.firstButton:r?b.lastButton:b.middleButton};return(0,a.jsx)(q,{role:"group",className:(0,M.A)(b.root,n),ref:t,ownerState:m,...g,children:(0,a.jsx)(H.Provider,{value:f,children:y.map((e,t)=>(0,a.jsx)(_.Provider,{value:C(t),children:e},t))})})});var U=r(72814),X=r(2899),Z=r(30748),Y=r(48285);let K=e=>{let{classes:t,fullWidth:r,selected:a,disabled:s,size:o,color:n}=e,l={root:["root",a&&"selected",s&&"disabled",r&&"fullWidth",`size${(0,W.A)(o)}`,n]};return(0,T.A)(l,E,t)},Q=(0,B.Ay)(Z.A,{name:"MuiToggleButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[`size${(0,W.A)(r.size)}`]]}})((0,I.A)(({theme:e})=>({...e.typography.button,borderRadius:(e.vars||e).shape.borderRadius,padding:11,border:`1px solid ${(e.vars||e).palette.divider}`,color:(e.vars||e).palette.action.active,[`&.${L.disabled}`]:{color:(e.vars||e).palette.action.disabled,border:`1px solid ${(e.vars||e).palette.action.disabledBackground}`},"&:hover":{textDecoration:"none",backgroundColor:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,X.X4)(e.palette.text.primary,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[{props:{color:"standard"},style:{[`&.${L.selected}`]:{color:(e.vars||e).palette.text.primary,backgroundColor:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,X.X4)(e.palette.text.primary,e.palette.action.selectedOpacity),"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:(0,X.X4)(e.palette.text.primary,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,X.X4)(e.palette.text.primary,e.palette.action.selectedOpacity)}}}}},...Object.entries(e.palette).filter((0,Y.A)()).map(([t])=>({props:{color:t},style:{[`&.${L.selected}`]:{color:(e.vars||e).palette[t].main,backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,X.X4)(e.palette[t].main,e.palette.action.selectedOpacity),"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:(0,X.X4)(e.palette[t].main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,X.X4)(e.palette[t].main,e.palette.action.selectedOpacity)}}}}})),{props:{fullWidth:!0},style:{width:"100%"}},{props:{size:"small"},style:{padding:7,fontSize:e.typography.pxToRem(13)}},{props:{size:"large"},style:{padding:15,fontSize:e.typography.pxToRem(15)}}]}))),J=s.forwardRef(function(e,t){var r;let{value:o,...n}=s.useContext(H),l=s.useContext(_),i=(0,U.A)({...n,selected:(r=e.value,void 0!==o&&void 0!==r&&(Array.isArray(o)?o.includes(r):r===o))},e),d=(0,D.b)({props:i,name:"MuiToggleButton"}),{children:c,className:u,color:p="standard",disabled:h=!1,disableFocusRipple:x=!1,fullWidth:g=!1,onChange:m,onClick:b,selected:v,size:A="medium",value:f,...y}=d,j={...d,color:p,disabled:h,disableFocusRipple:x,fullWidth:g,size:A},C=K(j);return(0,a.jsx)(Q,{className:(0,M.A)(n.className,C.root,u,l||""),disabled:h,focusRipple:!x,ref:t,onClick:e=>{b&&(b(e,f),e.defaultPrevented)||m&&m(e,f)},onChange:m,value:f,ownerState:j,"aria-pressed":v,...y,children:c})});var ee=r(43291),et=r(62014),er=r(3297),ea=r(41896);let es=(0,b.A)((0,a.jsx)("path",{d:"M3 14h4v-4H3zm0 5h4v-4H3zM3 9h4V5H3zm5 5h13v-4H8zm0 5h13v-4H8zM8 5v4h13V5z"}),"ViewList"),eo=(0,b.A)((0,a.jsx)("path",{d:"M14.67 5v6.5H9.33V5zm1 6.5H21V5h-5.33zm-1 7.5v-6.5H9.33V19zm1-6.5V19H21v-6.5zm-7.34 0H3V19h5.33zm0-1V5H3v6.5z"}),"ViewModule"),en=(0,b.A)((0,a.jsx)("path",{d:"m20.5 3-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .*********.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5M15 19l-6-2.11V5l6 2.11z"}),"Map"),el=({onAddResource:e,searchQuery:t,onSearchChange:r,viewMode:s,onViewModeChange:n,resourceTypes:l,tabValue:i,onTabChange:d})=>(0,a.jsxs)(o.A,{children:[(0,a.jsxs)(o.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,a.jsx)(k.A,{variant:"h4",component:"h1",gutterBottom:!0,children:"Resource Management"}),(0,a.jsx)(g.A,{variant:"contained",color:"primary",startIcon:(0,a.jsx)(er.A,{}),onClick:e,children:"Add Resource"})]}),(0,a.jsxs)(R.A,{elevation:2,sx:{display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:2,mb:3,p:2,borderRadius:2},children:[(0,a.jsx)($.A,{placeholder:"Search Resources...",value:t,onChange:r,InputProps:{startAdornment:(0,a.jsx)(z.A,{position:"start",children:(0,a.jsx)(ea.A,{})})},sx:{flexGrow:1}}),(0,a.jsxs)(G,{value:s,exclusive:!0,onChange:(e,t)=>{null!==t&&n(t)},"aria-label":"View mode",children:[(0,a.jsxs)(J,{value:"list","aria-label":"List view",children:[(0,a.jsx)(es,{sx:{mr:.5}}),"List"]}),(0,a.jsxs)(J,{value:"grid","aria-label":"Grid view",children:[(0,a.jsx)(eo,{sx:{mr:.5}}),"Grid"]}),(0,a.jsxs)(J,{value:"floorPlan","aria-label":"Floor plan view",children:[(0,a.jsx)(en,{sx:{mr:.5}}),"Map"]})]})]}),(0,a.jsx)(o.A,{sx:{borderBottom:1,borderColor:"divider",mb:2},children:(0,a.jsx)(ee.A,{value:i,onChange:d,"aria-label":"resource type tabs",variant:"scrollable",scrollButtons:"auto",children:l.map((e,t)=>(0,a.jsx)(et.A,{label:e.charAt(0).toUpperCase()+e.slice(1),id:`resource-tab-${t}`,"aria-controls":`resource-tabpanel-${t}`},t))})})]});var ei=r(11830);let ed=({open:e,editMode:t,formData:r,onClose:s,onInputChange:m,onSelectChange:b,onSubmit:v,error:A,loading:f,formSubmitted:y})=>(0,a.jsxs)(l.A,{open:e,onClose:s,maxWidth:"md",fullWidth:!0,"aria-labelledby":"resource-dialog-title",children:[(0,a.jsx)(i.A,{id:"resource-dialog-title",children:t?"Edit Resource":"Add New Resource"}),(0,a.jsxs)(d.A,{dividers:!0,children:[A&&(0,a.jsx)(n.A,{severity:"error",sx:{mb:2},children:A}),(0,a.jsxs)(ei.A,{direction:{xs:"column",md:"row"},spacing:3,children:[(0,a.jsxs)(o.A,{sx:{width:{xs:"100%",md:"50%"}},children:[(0,a.jsx)($.A,{autoFocus:!0,margin:"dense",name:"name",label:"Resource Name",type:"text",fullWidth:!0,value:r.name,onChange:m,required:!0,error:y&&!r.name,helperText:y&&!r.name?"Name is required":"",sx:{mb:2}}),(0,a.jsxs)(c.A,{fullWidth:!0,margin:"dense",sx:{mb:2},children:[(0,a.jsx)(u.A,{id:"type-label",children:"Type"}),(0,a.jsxs)(p.A,{labelId:"type-label",name:"type",value:r.type,onChange:b,label:"Type",children:[(0,a.jsx)(h.A,{value:"table",children:"Table"}),(0,a.jsx)(h.A,{value:"room",children:"Room"}),(0,a.jsx)(h.A,{value:"desk",children:"Desk"}),(0,a.jsx)(h.A,{value:"equipment",children:"Equipment"})]})]}),(0,a.jsxs)(c.A,{fullWidth:!0,margin:"dense",sx:{mb:2},children:[(0,a.jsx)(u.A,{id:"status-label",children:"Status"}),(0,a.jsxs)(p.A,{labelId:"status-label",name:"status",value:r.status,onChange:b,label:"Status",children:[(0,a.jsx)(h.A,{value:"available",children:"Available"}),(0,a.jsx)(h.A,{value:"booked",children:"Booked"}),(0,a.jsx)(h.A,{value:"maintenance",children:"Maintenance"}),(0,a.jsx)(h.A,{value:"in-use",children:"In Use"})]})]}),(0,a.jsx)($.A,{margin:"dense",name:"hourly_rate",label:"Hourly Rate",type:"number",fullWidth:!0,value:r.hourly_rate,onChange:m,sx:{mb:2}}),"room"===r.type&&(0,a.jsx)($.A,{margin:"dense",name:"capacity",label:"Capacity",type:"number",fullWidth:!0,value:r.capacity||"",onChange:m})]}),(0,a.jsxs)(o.A,{sx:{width:{xs:"100%",md:"50%"}},children:[(0,a.jsxs)(c.A,{fullWidth:!0,margin:"dense",sx:{mb:2},children:[(0,a.jsx)(u.A,{id:"floor-label",children:"Floor"}),(0,a.jsxs)(p.A,{labelId:"floor-label",name:"floor",value:r.floor||"1",onChange:b,label:"Floor",children:[(0,a.jsx)(h.A,{value:"1",children:"Floor 1"}),(0,a.jsx)(h.A,{value:"2",children:"Floor 2"}),(0,a.jsx)(h.A,{value:"3",children:"Floor 3"})]})]}),(0,a.jsx)($.A,{margin:"dense",name:"zone",label:"Zone (Optional)",type:"text",fullWidth:!0,value:r.zone||"",onChange:m,sx:{mb:2}}),(0,a.jsx)(k.A,{variant:"subtitle2",sx:{mt:2,mb:1},children:"Floor Plan Details (Optional)"}),(0,a.jsxs)(ei.A,{direction:"row",spacing:2,children:[(0,a.jsx)($.A,{name:"x",label:"X",type:"number",value:r.x,onChange:m,sx:{width:"50%"}}),(0,a.jsx)($.A,{name:"y",label:"Y",type:"number",value:r.y,onChange:m,sx:{width:"50%"}})]}),(0,a.jsxs)(ei.A,{direction:"row",spacing:2,sx:{mt:1},children:[(0,a.jsx)($.A,{name:"width",label:"Width",type:"number",value:r.width,onChange:m,sx:{width:"50%"}}),(0,a.jsx)($.A,{name:"height",label:"Height",type:"number",value:r.height,onChange:m,sx:{width:"50%"}})]})]})]})]}),(0,a.jsxs)(x.A,{sx:{px:3,py:2},children:[(0,a.jsx)(g.A,{onClick:s,color:"inherit",disabled:f,children:"Cancel"}),(0,a.jsx)(g.A,{onClick:v,variant:"contained",color:"primary",disabled:f,children:f?"Saving...":t?"Save Changes":"Add Resource"})]})]}),ec=({selectedFloor:e,onFloorChange:t,floors:r,selectedZone:s,onZoneChange:n,zones:l})=>(0,a.jsxs)(R.A,{sx:{p:2,mb:3,mt:-1},children:[" ",(0,a.jsxs)(o.A,{sx:{display:"flex",flexWrap:"wrap",gap:2},children:[(0,a.jsx)(o.A,{sx:{flexBasis:{xs:"100%",sm:"calc(50% - 8px)",md:"calc(33.333% - 11px)"},minWidth:"150px"},children:(0,a.jsxs)(c.A,{fullWidth:!0,size:"small",children:[(0,a.jsx)(u.A,{id:"floor-filter-label",children:"Filter by Floor"}),(0,a.jsxs)(p.A,{labelId:"floor-filter-label",value:null===e?"":e.toString(),onChange:t,label:"Filter by Floor",children:[(0,a.jsx)(h.A,{value:"",children:(0,a.jsx)("em",{children:"All Floors"})}),r.map(e=>(0,a.jsxs)(h.A,{value:e.toString(),children:["Floor ",e]},e))]})]})}),(0,a.jsx)(o.A,{sx:{flexBasis:{xs:"100%",sm:"calc(50% - 8px)",md:"calc(33.333% - 11px)"},minWidth:"150px"},children:(0,a.jsxs)(c.A,{fullWidth:!0,size:"small",children:[(0,a.jsx)(u.A,{id:"zone-filter-label",children:"Filter by Zone"}),(0,a.jsxs)(p.A,{labelId:"zone-filter-label",value:s,onChange:n,label:"Filter by Zone",children:[(0,a.jsx)(h.A,{value:"all",children:(0,a.jsx)("em",{children:"All Zones"})}),l.map(e=>(0,a.jsx)(h.A,{value:e,children:e},e))]})]})})]})]});var eu=r(80986),ep=r(86862),eh=r(60042),ex=r(76533),eg=r(82681),em=r(46380),eb=r(43323);let ev=({resource:e,onEdit:t,onDelete:r,onStatusClick:s,isDarkMode:n,getResourceIcon:l,getStatusChipColor:i})=>(0,a.jsxs)(eu.A,{elevation:3,sx:{display:"flex",flexDirection:"column",height:"100%",border:"1px solid",borderColor:"divider",transition:"box-shadow 0.3s ease-in-out, transform 0.2s ease-in-out","&:hover":{boxShadow:n?"0 8px 16px rgba(0,0,0,0.5)":"0 8px 16px rgba(0,0,0,0.2)",transform:"translateY(-4px)"}},children:[(0,a.jsxs)(ep.A,{sx:{flexGrow:1,pb:1},children:[" ",(0,a.jsxs)(o.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:1.5},children:[(0,a.jsxs)(o.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(o.A,{sx:{mr:1.5,p:.5,borderRadius:"50%",backgroundColor:n?"grey.700":"grey.200",display:"flex",alignItems:"center",justifyContent:"center"},children:l(e.type)}),(0,a.jsx)(o.A,{sx:{maxWidth:140,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:(0,a.jsx)(eh.A,{title:e.name,placement:"top-start",children:(0,a.jsx)(k.A,{variant:"h6",component:"div",sx:{fontWeight:"medium",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.name})})})]}),(0,a.jsx)(ex.A,{label:e.status.charAt(0).toUpperCase()+e.status.slice(1),color:i(e.status),size:"small",onClick:()=>s(e.id,e.status),sx:{cursor:"pointer",fontWeight:"medium"}})]}),(0,a.jsxs)(o.A,{sx:{mb:1.5,flex:1},children:[" ",(0,a.jsxs)(ei.A,{spacing:.5,children:[" ",(0,a.jsxs)(o.A,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,a.jsx)(k.A,{variant:"body2",color:"text.secondary",children:"Rate:"}),(0,a.jsxs)(k.A,{variant:"body2",sx:{fontWeight:"medium"},children:["$",e.hourly_rate,"/hr"]})]}),("room"===e.type||"desk"===e.type)&&e.capacity&&(0,a.jsxs)(o.A,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,a.jsx)(k.A,{variant:"body2",color:"text.secondary",children:"Capacity:"}),(0,a.jsx)(k.A,{variant:"body2",sx:{fontWeight:"medium"},children:e.capacity})]}),e.floor&&(0,a.jsxs)(o.A,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,a.jsx)(k.A,{variant:"body2",color:"text.secondary",children:"Floor:"}),(0,a.jsx)(k.A,{variant:"body2",sx:{fontWeight:"medium"},children:e.floor})]}),e.zone&&(0,a.jsxs)(o.A,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,a.jsx)(k.A,{variant:"body2",color:"text.secondary",children:"Zone:"}),(0,a.jsx)(k.A,{variant:"body2",sx:{fontWeight:"medium"},children:e.zone})]})]})]})]}),(0,a.jsxs)(eg.A,{sx:{justifyContent:"flex-end",pt:0,pb:1.5,px:2},children:[" ",(0,a.jsx)(g.A,{size:"small",startIcon:(0,a.jsx)(em.A,{}),onClick:()=>t(e),color:"primary",children:"Edit"}),(0,a.jsx)(g.A,{size:"small",startIcon:(0,a.jsx)(eb.A,{}),onClick:()=>r(e.id),color:"error",children:"Delete"})]})]}),eA=({resources:e,...t})=>0===e.length?(0,a.jsx)(o.A,{sx:{width:"100%",p:4,textAlign:"center"},children:(0,a.jsxs)(R.A,{sx:{p:4,borderRadius:2,backgroundColor:t.isDarkMode?"grey.800":"grey.100"},children:[(0,a.jsx)(k.A,{variant:"h6",color:"text.secondary",children:"No resources found."}),(0,a.jsx)(k.A,{variant:"body1",color:"text.secondary",children:"Try adjusting your search or filters."})]})}):(0,a.jsx)(o.A,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",sm:"repeat(2, 1fr)",md:"repeat(3, 1fr)",lg:"repeat(4, 1fr)"},gap:3},children:e.map(e=>(0,a.jsx)(ev,{resource:e,...t},e.id))}),ef=({resources:e,onEdit:t,onDelete:r,onStatusClick:s,getResourceIcon:n,getStatusChipColor:l})=>0===e.length?(0,a.jsx)(o.A,{sx:{width:"100%",textAlign:"center",py:5},children:(0,a.jsx)(k.A,{variant:"subtitle1",color:"textSecondary",children:"No resources to display."})}):(0,a.jsx)(ei.A,{spacing:2,children:e.map(e=>(0,a.jsxs)(eu.A,{elevation:2,sx:{display:"flex",alignItems:"center",p:2},children:[(0,a.jsx)(o.A,{sx:{mr:2,display:"flex",alignItems:"center"},children:n(e.type)}),(0,a.jsxs)(o.A,{sx:{flexGrow:1},children:[(0,a.jsx)(k.A,{variant:"h6",children:e.name}),(0,a.jsxs)(k.A,{variant:"body2",color:"text.secondary",children:["Type: ",e.type," | Rate: $",e.hourly_rate,"/hr",e.capacity&&` | Capacity: ${e.capacity}`,e.floor&&` | Floor: ${e.floor}`,e.zone&&` | Zone: ${e.zone}`]})]}),(0,a.jsxs)(ei.A,{direction:"row",spacing:1,alignItems:"center",children:[(0,a.jsx)(ex.A,{label:e.status.charAt(0).toUpperCase()+e.status.slice(1),color:l(e.status),size:"small",onClick:()=>s(e.id,e.status),sx:{cursor:"pointer"}}),(0,a.jsx)(g.A,{size:"small",startIcon:(0,a.jsx)(em.A,{}),onClick:()=>t(e),children:"Edit"}),(0,a.jsx)(g.A,{size:"small",startIcon:(0,a.jsx)(eb.A,{}),onClick:()=>r(e.id),color:"error",children:"Delete"})]})]},e.id))}),ey=({resources:e,onEdit:t,onStatusClick:r,onResourcePositionUpdate:s,selectedFloor:n,isDarkMode:l,getResourceColor:i,getStatusChipColor:d})=>{let c=(0,w.A)(),u=e.filter(e=>e.floor===n),p=Math.max(...u.map(e=>(e.x??0)+(e.width??1)),12),h=Math.max(...u.map(e=>(e.y??0)+(e.height??1)),8),x=Math.max(12,p+2),g=Math.max(8,h+2),m=64*g,b=e=>{let t=e.width||1,r=e.height||1;return{width:60*t+(t-1)*4,height:60*r+(r-1)*4}},v=e=>({left:64*(e.x??0),top:(e.y??0)*64});return null===n?(0,a.jsx)(k.A,{children:"Please select a floor to view the plan."}):(0,a.jsxs)(o.A,{sx:{position:"relative",width:"100%",height:`${m+100}px`,border:`1px solid ${l?"rgba(255, 255, 255, 0.12)":"rgba(0, 0, 0, 0.12)"}`,borderRadius:1,overflow:"auto",p:2,bgcolor:l?"rgba(0, 0, 0, 0.05)":"rgba(0, 0, 0, 0.02)"},children:[(0,a.jsxs)(o.A,{sx:{display:"flex",justifyContent:"center",flexWrap:"wrap",gap:2,mb:2,p:1},children:[(0,a.jsxs)(o.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(o.A,{sx:{width:16,height:16,backgroundColor:"rgba(76, 175, 80, 0.7)",borderRadius:.5,mr:.5}}),(0,a.jsx)(k.A,{variant:"caption",children:"Available"})]}),(0,a.jsxs)(o.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(o.A,{sx:{width:16,height:16,backgroundColor:"rgba(244, 67, 54, 0.7)",borderRadius:.5,mr:.5}}),(0,a.jsx)(k.A,{variant:"caption",children:"Booked"})]}),(0,a.jsxs)(o.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(o.A,{sx:{width:16,height:16,backgroundColor:"rgba(255, 152, 0, 0.7)",borderRadius:.5,mr:.5}}),(0,a.jsx)(k.A,{variant:"caption",children:"Maintenance"})]}),["in-use"].map(e=>{if("in-use"===e){let t=i(e);return(0,a.jsxs)(o.A,{sx:{display:"flex",alignItems:"center"},children:[(0,a.jsx)(o.A,{sx:{width:16,height:16,backgroundColor:t.bg,borderRadius:.5,mr:.5}}),(0,a.jsx)(k.A,{variant:"caption",children:e.charAt(0).toUpperCase()+e.slice(1)})]},e)}return null})]}),(0,a.jsxs)(o.A,{className:"floor-plan-grid",sx:{position:"relative",width:`${64*x}px`,height:`${m}px`,margin:"0 auto",background:l?"linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px)":"linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px)",backgroundSize:"60px 60px"},onDragOver:e=>{e.preventDefault()},children:[(0,a.jsx)(o.A,{sx:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backgroundImage:"linear-gradient(#ddd 1px, transparent 1px), linear-gradient(90deg, #ddd 1px, transparent 1px)",backgroundSize:"60px 60px",opacity:.5}}),0===u.length&&(0,a.jsx)(o.A,{sx:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",position:"absolute"},children:(0,a.jsxs)(k.A,{variant:"body1",color:"text.secondary",children:["No resources found on Floor ",n,". Try adjusting your filters or adding resources with position data."]})}),u.map(e=>{let n=b(e),l=v(e),u=i(e.status),p=`T-${e.id.toString().padStart(2,"0")}`;return(0,a.jsxs)(o.A,{"data-resource-id":e.id,sx:{position:"absolute",left:`${l.left}px`,top:`${l.top}px`,width:`${n.width}px`,height:`${n.height}px`,backgroundColor:u.bg,border:"2px solid",borderColor:u.border,borderRadius:2,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:1,cursor:"move",transition:"all 0.2s ease","&:hover":{boxShadow:c.shadows[3],transform:"scale(1.02)"},userSelect:"none",touchAction:"none"},draggable:!0,onDragStart:t=>{t.currentTarget.setAttribute("dragging","true"),t.dataTransfer.setData("text/plain",e.id.toString()),t.dataTransfer.effectAllowed="move";let r=t.currentTarget.getBoundingClientRect(),a=t.clientX-r.left,s=t.clientY-r.top;t.currentTarget.setAttribute("data-drag-offset-x",a.toString()),t.currentTarget.setAttribute("data-drag-offset-y",s.toString());let o=t.currentTarget.cloneNode(!0);o.style.position="absolute",o.style.left="-9999px",o.style.width=`${t.currentTarget.offsetWidth}px`,o.style.height=`${t.currentTarget.offsetHeight}px`,o.style.opacity="0.7",o.style.zIndex="10000",document.body.appendChild(o),t.dataTransfer.setDragImage(o,a,s),setTimeout(()=>{document.body.contains(o)&&document.body.removeChild(o)},0)},onDrag:e=>{e.currentTarget.hasAttribute("dragging")||e.currentTarget.setAttribute("dragging","true")},onDragEnd:t=>{if(!t.currentTarget.hasAttribute("dragging"))return;t.currentTarget.removeAttribute("dragging");let r=t.currentTarget.closest(".floor-plan-grid");if(!r)return;let a=r.getBoundingClientRect(),o=parseInt(t.currentTarget.getAttribute("data-drag-offset-x")||"0"),n=parseInt(t.currentTarget.getAttribute("data-drag-offset-y")||"0"),l=t.clientX-a.left-o,i=t.clientY-a.top-n,d=Math.round(l/64),c=Math.round(i/64),u=e.width||1,p=e.height||1;d=Math.max(0,Math.min(d,x-u)),c=Math.max(0,Math.min(c,g-p)),(d!==(e.x??0)||c!==(e.y??0))&&s(e.id,d,c),t.currentTarget.removeAttribute("data-drag-offset-x"),t.currentTarget.removeAttribute("data-drag-offset-y")},onClick:r=>{r.currentTarget.hasAttribute("dragging")||t(e)},children:[(0,a.jsx)(k.A,{variant:"subtitle2",sx:{fontWeight:"bold",color:c.palette.getContrastText(u.bg),textAlign:"center",fontSize:"0.8rem"},children:p}),n.width>=120&&(0,a.jsx)(k.A,{variant:"body2",sx:{color:c.palette.getContrastText(u.bg),textAlign:"center",fontSize:"0.7rem",mt:.5},children:e.name}),"available"!==e.status&&(0,a.jsx)(ex.A,{label:e.status.charAt(0).toUpperCase()+e.status.slice(1),size:"small",color:d(e.status),sx:{fontSize:"0.6rem",height:"20px",mt:.5,"& .MuiChip-label":{padding:"0 6px"}},onClick:t=>{t.stopPropagation(),r(e.id,e.status)}})]},e.id)})]})]})},ej=({viewMode:e,resources:t,onEditResource:r,onDeleteResource:s,onStatusClick:n,onResourcePositionUpdate:l,selectedFloor:i,isDarkMode:d,getResourceIcon:c,getStatusChipColor:u,getResourceColor:p,loading:h,error:x})=>{if(h)return(0,a.jsx)(o.A,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"200px"},children:(0,a.jsx)(k.A,{children:"Loading resources..."})});if(0===t.length&&!h&&!x)return(0,a.jsx)(o.A,{sx:{width:"100%",p:4,textAlign:"center"},children:(0,a.jsxs)(R.A,{sx:{p:4,borderRadius:2,backgroundColor:d?"grey.800":"grey.100"},children:[(0,a.jsx)(k.A,{variant:"h6",color:"text.secondary",children:"No Resources Found"}),(0,a.jsx)(k.A,{variant:"body1",color:"text.secondary",children:"There are no resources matching your current filters. Try adjusting your search or filter criteria."})]})});switch(e){case"list":return(0,a.jsx)(ef,{resources:t,onEdit:r,onDelete:s,onStatusClick:n,getResourceIcon:c,getStatusChipColor:u});case"grid":return(0,a.jsx)(eA,{resources:t,onEdit:r,onDelete:s,onStatusClick:n,isDarkMode:d,getResourceIcon:c,getStatusChipColor:u});case"floorPlan":return(0,a.jsx)(ey,{resources:t,onEdit:r,onDelete:s,onStatusClick:n,onResourcePositionUpdate:l,selectedFloor:i,isDarkMode:d,getResourceColor:p,getStatusChipColor:u});default:return(0,a.jsx)(k.A,{children:"Unknown view mode."})}};function eC(){let{resources:e,addResource:t,updateResource:r,deleteResource:b,updateResourceStatus:k,fetchResources:R,setResources:$,authUser:z}=(0,j.A)(),[M,T]=(0,s.useState)(""),[B,I]=(0,s.useState)(!1),[D,W]=(0,s.useState)(!1),[F,P]=(0,s.useState)(!1),[V,O]=(0,s.useState)(null),H=(0,s.useMemo)(()=>({id:0,name:"",type:"table",status:"available",hourly_rate:"",capacity:"",floor:"1",zone:"",x:1,y:1,width:1,height:1}),[]),[_,E]=(0,s.useState)(H),[L,N]=(0,s.useState)(0),[q,G]=(0,s.useState)(!1),[U,X]=(0,s.useState)(null),[Z,Y]=(0,s.useState)("available"),[K,Q]=(0,s.useState)("grid"),[J,ee]=(0,s.useState)(1),[et,er]=(0,s.useState)("all"),[ea,es]=(0,s.useState)(!1),[eo,en]=(0,s.useState)(""),[ei,eu]=(0,s.useState)("success"),[ep,eh]=(0,s.useState)([]),[ex,eg]=(0,s.useState)(!1),{mode:em}=(0,C.D)(),eb=(0,w.A)(),ev="dark"===em,[eA,ef]=(0,s.useState)(!1),[ey,eC]=(0,s.useState)(null),[ew,eS]=(0,s.useState)(null),ek=(0,s.useMemo)(()=>Array.from(new Set((Array.isArray(e)?e:[]).map(e=>e.floor).filter(Boolean).map(e=>parseInt(e.toString(),10)))).sort((e,t)=>e-t),[e]),eR=(0,s.useMemo)(()=>Array.from(new Set((Array.isArray(e)?e:[]).map(e=>e.zone).filter(Boolean))),[e]),e$=(0,s.useMemo)(()=>["all","room","desk","equipment","table"],[]),ez=e=>{let t=e.target.value;ee(""===t?null:Number(t))},eM=e=>{er(e.target.value)},eT=(0,s.useCallback)(t=>{if(t)return{...H,...t,floor:t.floor?.toString()||"1",hourly_rate:t.hourly_rate?.toString()||"",capacity:t.capacity?.toString()||"",x:t.x??H.x,y:t.y??H.y,width:t.width??H.width,height:t.height??H.height};let r=e&&e.length>0?Math.max(...e.map(e=>e.id))+1:1;return{...H,id:r}},[e,H]),eB=(0,s.useCallback)((e=!1,t)=>{W(e),E(eT(t)),I(!0),eS(null),eg(!1)},[eT]),eI=()=>{I(!1),eS(null),E(H)},eD=async()=>{if(eg(!0),!_.name){eS("Resource name is required."),en("Resource name is required."),eu("error"),es(!0);return}ef(!0),eS(null);let e={name:_.name,type:_.type,status:_.status,hourly_rate:parseFloat(_.hourly_rate.toString())||0,capacity:_.capacity?parseInt(_.capacity.toString()):0,floor:parseInt(_.floor.toString()),zone:_.zone?_.zone.toString():"",x:parseInt(_.x.toString()),y:parseInt(_.y.toString()),width:parseInt(_.width.toString()),height:parseInt(_.height.toString())};try{D&&_.id?(await r(_.id,e),en("Resource updated successfully")):(await t(e),en("Resource added successfully")),eu("success"),eI(),R()}catch(t){let e=t instanceof Error?t.message:"An unexpected error occurred.";eS(e),en(e),eu("error")}finally{ef(!1),es(!0)}},eW=()=>{G(!1),X(null)},eF=async()=>{if(null!==U){ef(!0);try{await k(U,Z),en("Resource status updated."),eu("success"),eW(),R()}catch(e){en(e instanceof Error?e.message:"Failed to update status."),eu("error")}finally{ef(!1),es(!0)}}},eP=async()=>{if(null!==V){ef(!0);try{await b(V),en("Resource deleted."),eu("success"),R()}catch(e){en(e instanceof Error?e.message:"Failed to delete resource."),eu("error")}finally{ef(!1),P(!1),O(null),es(!0)}}},eV=(0,s.useCallback)(e=>{switch(e){case"room":default:return(0,a.jsx)(v,{});case"desk":return(0,a.jsx)(A,{});case"equipment":return(0,a.jsx)(f,{});case"table":return(0,a.jsx)(y,{})}},[]),eO=(0,s.useCallback)(e=>({available:"success",booked:"error",maintenance:"warning","in-use":"info"})[e]||"default",[]),eH=(0,s.useCallback)(e=>{if(!eb)return{bg:"rgba(158, 158, 158, 0.7)",border:"#9e9e9e"};let t={available:{bg:ev?"rgba(76, 175, 80, 0.8)":"rgba(76, 175, 80, 0.7)",border:eb.palette.success.main},booked:{bg:ev?"rgba(244, 67, 54, 0.8)":"rgba(244, 67, 54, 0.7)",border:eb.palette.error.main},maintenance:{bg:ev?"rgba(255, 152, 0, 0.8)":"rgba(255, 152, 0, 0.7)",border:eb.palette.warning.main},"in-use":{bg:ev?"rgba(33, 150, 243, 0.8)":"rgba(33, 150, 243, 0.7)",border:eb.palette.info.main}},r={bg:ev?"rgba(158, 158, 158, 0.8)":"rgba(158, 158, 158, 0.7)",border:eb.palette.grey[500]};return t[e]||r},[ev,eb]),e_=async(t,a,s)=>{let o=e.find(e=>e.id===t);if(!o)return;let n={...o,x:a,y:s},l=[...e];$(e.map(e=>e.id===t?n:e));try{await r(t,{x:a,y:s}),en(`Position updated for '${o.name}'.`),eu("success")}catch(e){$(l),en(`Failed to update position for '${o.name}'. ${e instanceof Error?e.message:""}`),eu("error")}finally{es(!0)}},eE=(e,t)=>{"clickaway"!==t&&es(!1)};return(0,a.jsxs)(o.A,{sx:{flexGrow:1},children:[(0,a.jsx)(el,{onAddResource:()=>eB(!1),searchQuery:M,onSearchChange:e=>{T(e.target.value)},viewMode:K,onViewModeChange:e=>{Q(e)},selectedFloor:J,onFloorChange:ez,floors:ek,selectedZone:et,onZoneChange:eM,zones:eR,resourceTypes:e$,tabValue:L,onTabChange:(e,t)=>{N(t)}}),ey&&(0,a.jsx)(n.A,{severity:"error",sx:{my:2},children:ey}),(0,a.jsx)(ec,{selectedFloor:J,onFloorChange:ez,floors:ek,selectedZone:et,onZoneChange:eM,zones:eR,viewMode:K}),(0,a.jsx)(o.A,{sx:{mt:"floorPlan"===K&&ek.length>0?0:2},children:(0,a.jsx)(ej,{viewMode:K,resources:ep,onEditResource:e=>eB(!0,e),onDeleteResource:e=>{O(e),P(!0)},onStatusClick:(e,t)=>{X(e),Y(t),G(!0)},onResourcePositionUpdate:e_,selectedFloor:J,isDarkMode:ev,getResourceIcon:eV,getStatusChipColor:eO,getResourceColor:eH,loading:eA&&0===ep.length&&!ey,error:ey&&0===ep.length?ey:null})}),(0,a.jsx)(ed,{open:B,editMode:D,formData:_,onClose:eI,onInputChange:e=>{let{name:t,value:r}=e.target,a=["hourly_rate","capacity","x","y","width","height"];E(e=>({...e,[t]:a.includes(t)&&""!==r?parseFloat(r):r}))},onSelectChange:e=>{let{name:t,value:r}=e.target;E(e=>({...e,[t]:r}))},onSubmit:eD,error:ew,loading:eA,formSubmitted:ex}),(0,a.jsx)(S.A,{open:F,title:"Confirm Delete",message:"Are you sure you want to delete this resource? This action cannot be undone.",onConfirm:eP,onCancel:()=>{P(!1),O(null)},confirmButtonColor:"error"}),(0,a.jsxs)(l.A,{open:q,onClose:eW,children:[(0,a.jsx)(i.A,{children:"Update Resource Status"}),(0,a.jsx)(d.A,{children:(0,a.jsxs)(c.A,{fullWidth:!0,sx:{mt:1},children:[(0,a.jsx)(u.A,{id:"new-status-label",children:"New Status"}),(0,a.jsxs)(p.A,{labelId:"new-status-label",value:Z,onChange:e=>{Y(e.target.value)},label:"New Status",children:[(0,a.jsx)(h.A,{value:"available",children:"Available"}),(0,a.jsx)(h.A,{value:"booked",children:"Booked"}),(0,a.jsx)(h.A,{value:"maintenance",children:"Maintenance"}),(0,a.jsx)(h.A,{value:"in-use",children:"In Use"})]})]})}),(0,a.jsxs)(x.A,{children:[(0,a.jsx)(g.A,{onClick:eW,children:"Cancel"}),(0,a.jsx)(g.A,{onClick:eF,variant:"contained",disabled:eA,children:"Update Status"})]})]}),(0,a.jsx)(m.A,{open:ea,autoHideDuration:4e3,onClose:eE,anchorOrigin:{vertical:"bottom",horizontal:"center"},children:(0,a.jsx)(n.A,{onClose:eE,severity:ei,sx:{width:"100%"},variant:"filled",children:eo})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19378:(e,t,r)=>{Promise.resolve().then(r.bind(r,16400))},24296:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var a=r(43210),s=r(49384),o=r(99282),n=r(87088),l=r(13555),i=r(84754),d=r(79222),c=r(44791),u=r(60687);let p=e=>{let{classes:t}=e;return(0,o.A)({root:["root"]},d.t,t)},h=(0,l.Ay)(n.A,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),x=a.forwardRef(function(e,t){let r=(0,i.b)({props:e,name:"MuiDialogTitle"}),{className:o,id:n,...l}=r,d=p(r),{titleId:x=n}=a.useContext(c.A);return(0,u.jsx)(h,{component:"h2",className:(0,s.A)(d.root,o),ownerState:r,ref:t,variant:"h6",id:n??x,...l})})},25344:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=r(65239),s=r(48088),o=r(88170),n=r.n(o),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["resources",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,73434)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\resources\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\yunsell\\evospace\\evospace-pos\\src\\app\\resources\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/resources/page",pathname:"/resources",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37860:(e,t,r)=>{"use strict";r.d(t,{D:()=>o}),r(60687);var a=r(43210);let s=(0,a.createContext)({mode:"light",toggleMode:()=>{},theme:"light",toggleTheme:()=>{}}),o=()=>(0,a.useContext)(s)},41896:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(23428),s=r(60687);let o=(0,a.A)((0,s.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14"}),"Search")},43323:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(23428),s=r(60687);let o=(0,a.A)((0,s.jsx)("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"}),"Delete")},46380:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(23428),s=r(60687);let o=(0,a.A)((0,s.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit")},54534:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var a=r(60687);r(43210);var s=r(12362),o=r(24296),n=r(90764),l=r(87088),i=r(27674),d=r(16184);let c=({open:e,title:t,message:r,onConfirm:c,onCancel:u,infoMode:p=!1,confirmText:h,confirmButtonColor:x="primary"})=>(0,a.jsxs)(s.A,{open:e,onClose:u,children:[(0,a.jsx)(o.A,{children:t}),(0,a.jsx)(n.A,{children:(0,a.jsx)(l.A,{children:r})}),(0,a.jsxs)(i.A,{children:[!p&&(0,a.jsx)(d.A,{onClick:u,children:"Cancel"}),(0,a.jsx)(d.A,{onClick:c,variant:"contained",color:p?"primary":x,children:p?h||"OK":h||"Confirm"})]})]})},56330:(e,t,r)=>{Promise.resolve().then(r.bind(r,73434))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73434:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\yunsell\\\\evospace\\\\evospace-pos\\\\src\\\\app\\\\resources\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\yunsell\\evospace\\evospace-pos\\src\\app\\resources\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},82681:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var a=r(43210),s=r(49384),o=r(99282),n=r(13555),l=r(84754),i=r(4144),d=r(82816);function c(e){return(0,d.Ay)("MuiCardActions",e)}(0,i.A)("MuiCardActions",["root","spacing"]);var u=r(60687);let p=e=>{let{classes:t,disableSpacing:r}=e;return(0,o.A)({root:["root",!r&&"spacing"]},c,t)},h=(0,n.Ay)("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,variants:[{props:{disableSpacing:!1},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),x=a.forwardRef(function(e,t){let r=(0,l.b)({props:e,name:"MuiCardActions"}),{disableSpacing:a=!1,className:o,...n}=r,i={...r,disableSpacing:a},d=p(i);return(0,u.jsx)(h,{className:(0,s.A)(d.root,o),ownerState:i,ref:t,...n})})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,991,619,111,117,575,154,790,634,943,985,79],()=>r(25344));module.exports=a})();