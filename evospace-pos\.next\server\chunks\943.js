"use strict";exports.id=943,exports.ids=[943],exports.modules={11830:(e,t,o)=>{o.d(t,{A:()=>x});var r=o(43210),p=o(49384),n=o(43648),a=o(82816),i=o(99282);let l=(0,o(88316).Ay)();var s=o(32856),c=o(44018),m=o(30437),u=o(98896),d=o(27887),g=o(60687);let f=(0,m.A)(),h=l("div",{name:"<PERSON><PERSON><PERSON>tack",slot:"Root"});function y(e){return(0,s.A)({props:e,name:"MuiStack",defaultTheme:f})}let v=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],A=({ownerState:e,theme:t})=>{let o={display:"flex",flexDirection:"column",...(0,u.NI)({theme:t},(0,u.kW)({values:e.direction,breakpoints:t.breakpoints.values}),e=>({flexDirection:e}))};if(e.spacing){let r=(0,d.LX)(t),p=Object.keys(t.breakpoints.values).reduce((t,o)=>(("object"==typeof e.spacing&&null!=e.spacing[o]||"object"==typeof e.direction&&null!=e.direction[o])&&(t[o]=!0),t),{}),a=(0,u.kW)({values:e.direction,base:p}),i=(0,u.kW)({values:e.spacing,base:p});"object"==typeof a&&Object.keys(a).forEach((e,t,o)=>{if(!a[e]){let r=t>0?a[o[t-1]]:"column";a[e]=r}}),o=(0,n.A)(o,(0,u.NI)({theme:t},i,(t,o)=>e.useFlexGap?{gap:(0,d._W)(r,t)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${v(o?a[o]:e.direction)}`]:(0,d._W)(r,t)}}))}return(0,u.iZ)(t.breakpoints,o)};var b=o(13555),w=o(84754);let x=function(e={}){let{createStyledComponent:t=h,useThemeProps:o=y,componentName:n="MuiStack"}=e,l=()=>(0,i.A)({root:["root"]},e=>(0,a.Ay)(n,e),{}),s=t(A);return r.forwardRef(function(e,t){let n=o(e),{component:a="div",direction:i="column",spacing:m=0,divider:u,children:d,className:f,useFlexGap:h=!1,...y}=(0,c.A)(n),v=l();return(0,g.jsx)(s,{as:a,ownerState:{direction:i,spacing:m,useFlexGap:h},ref:t,className:(0,p.A)(v.root,f),...y,children:u?function(e,t){let o=r.Children.toArray(e).filter(Boolean);return o.reduce((e,p,n)=>(e.push(p),n<o.length-1&&e.push(r.cloneElement(t,{key:`separator-${n}`})),e),[])}(d,u):d})})}({createStyledComponent:(0,b.Ay)("div",{name:"MuiStack",slot:"Root"}),useThemeProps:e=>(0,w.b)({props:e,name:"MuiStack"})})},60042:(e,t,o)=>{o.d(t,{A:()=>B});var r=o(43210),p=o(49384),n=o(31324),a=o(99282),i=o(2899),l=o(71779),s=o(37882),c=o(76070),m=o(13555),u=o(21360),d=o(45258),g=o(84754),f=o(61543),h=o(97752),y=o(89551),v=o(13139),A=o(6065),b=o(80931),w=o(24924),x=o(34414),T=o(4144),R=o(82816);function $(e){return(0,R.Ay)("MuiTooltip",e)}let k=(0,T.A)("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]);var M=o(60687);let O=e=>{let{classes:t,disableInteractive:o,arrow:r,touch:p,placement:n}=e,i={popper:["popper",!o&&"popperInteractive",r&&"popperArrow"],tooltip:["tooltip",r&&"tooltipArrow",p&&"touch",`tooltipPlacement${(0,f.A)(n.split("-")[0])}`],arrow:["arrow"]};return(0,a.A)(i,$,t)},S=(0,m.Ay)(y.A,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{let{ownerState:o}=e;return[t.popper,!o.disableInteractive&&t.popperInteractive,o.arrow&&t.popperArrow,!o.open&&t.popperClose]}})((0,d.A)(({theme:e})=>({zIndex:(e.vars||e).zIndex.tooltip,pointerEvents:"none",variants:[{props:({ownerState:e})=>!e.disableInteractive,style:{pointerEvents:"auto"}},{props:({open:e})=>!e,style:{pointerEvents:"none"}},{props:({ownerState:e})=>e.arrow,style:{[`&[data-popper-placement*="bottom"] .${k.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${k.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${k.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},[`&[data-popper-placement*="left"] .${k.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:({ownerState:e})=>e.arrow&&!e.isRtl,style:{[`&[data-popper-placement*="right"] .${k.arrow}`]:{left:0,marginLeft:"-0.71em"}}},{props:({ownerState:e})=>e.arrow&&!!e.isRtl,style:{[`&[data-popper-placement*="right"] .${k.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:e})=>e.arrow&&!e.isRtl,style:{[`&[data-popper-placement*="left"] .${k.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:e})=>e.arrow&&!!e.isRtl,style:{[`&[data-popper-placement*="left"] .${k.arrow}`]:{left:0,marginLeft:"-0.71em"}}}]}))),E=(0,m.Ay)("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{let{ownerState:o}=e;return[t.tooltip,o.touch&&t.touch,o.arrow&&t.tooltipArrow,t[`tooltipPlacement${(0,f.A)(o.placement.split("-")[0])}`]]}})((0,d.A)(({theme:e})=>({backgroundColor:e.vars?e.vars.palette.Tooltip.bg:(0,i.X4)(e.palette.grey[700],.92),borderRadius:(e.vars||e).shape.borderRadius,color:(e.vars||e).palette.common.white,fontFamily:e.typography.fontFamily,padding:"4px 8px",fontSize:e.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:e.typography.fontWeightMedium,[`.${k.popper}[data-popper-placement*="left"] &`]:{transformOrigin:"right center"},[`.${k.popper}[data-popper-placement*="right"] &`]:{transformOrigin:"left center"},[`.${k.popper}[data-popper-placement*="top"] &`]:{transformOrigin:"center bottom",marginBottom:"14px"},[`.${k.popper}[data-popper-placement*="bottom"] &`]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:({ownerState:e})=>e.arrow,style:{position:"relative",margin:0}},{props:({ownerState:e})=>e.touch,style:{padding:"8px 16px",fontSize:e.typography.pxToRem(14),lineHeight:`${Math.round(16/14*1e5)/1e5}em`,fontWeight:e.typography.fontWeightRegular}},{props:({ownerState:e})=>!e.isRtl,style:{[`.${k.popper}[data-popper-placement*="left"] &`]:{marginRight:"14px"},[`.${k.popper}[data-popper-placement*="right"] &`]:{marginLeft:"14px"}}},{props:({ownerState:e})=>!e.isRtl&&e.touch,style:{[`.${k.popper}[data-popper-placement*="left"] &`]:{marginRight:"24px"},[`.${k.popper}[data-popper-placement*="right"] &`]:{marginLeft:"24px"}}},{props:({ownerState:e})=>!!e.isRtl,style:{[`.${k.popper}[data-popper-placement*="left"] &`]:{marginLeft:"14px"},[`.${k.popper}[data-popper-placement*="right"] &`]:{marginRight:"14px"}}},{props:({ownerState:e})=>!!e.isRtl&&e.touch,style:{[`.${k.popper}[data-popper-placement*="left"] &`]:{marginLeft:"24px"},[`.${k.popper}[data-popper-placement*="right"] &`]:{marginRight:"24px"}}},{props:({ownerState:e})=>e.touch,style:{[`.${k.popper}[data-popper-placement*="top"] &`]:{marginBottom:"24px"}}},{props:({ownerState:e})=>e.touch,style:{[`.${k.popper}[data-popper-placement*="bottom"] &`]:{marginTop:"24px"}}}]}))),W=(0,m.Ay)("span",{name:"MuiTooltip",slot:"Arrow"})((0,d.A)(({theme:e})=>({overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:e.vars?e.vars.palette.Tooltip.bg:(0,i.X4)(e.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}}))),L=!1,j=new n.E,P={x:0,y:0};function I(e,t){return(o,...r)=>{t&&t(o,...r),e(o,...r)}}let B=r.forwardRef(function(e,t){let o=(0,g.b)({props:e,name:"MuiTooltip"}),{arrow:a=!1,children:i,classes:m,components:d={},componentsProps:f={},describeChild:T=!1,disableFocusListener:R=!1,disableHoverListener:$=!1,disableInteractive:k=!1,disableTouchListener:B=!1,enterDelay:C=100,enterNextDelay:N=0,enterTouchDelay:F=700,followCursor:z=!1,id:U,leaveDelay:X=0,leaveTouchDelay:D=1500,onClose:_,onOpen:G,open:H,placement:V="bottom",PopperComponent:Y,PopperProps:Z={},slotProps:q={},slots:J={},title:K,TransitionComponent:Q,TransitionProps:ee,...et}=o,eo=r.isValidElement(i)?i:(0,M.jsx)("span",{children:i}),er=(0,u.A)(),ep=(0,l.I)(),[en,ea]=r.useState(),[ei,el]=r.useState(null),es=r.useRef(!1),ec=k||z,em=(0,n.A)(),eu=(0,n.A)(),ed=(0,n.A)(),eg=(0,n.A)(),[ef,eh]=(0,w.A)({controlled:H,default:!1,name:"Tooltip",state:"open"}),ey=ef,ev=(0,b.A)(U),eA=r.useRef(),eb=(0,v.A)(()=>{void 0!==eA.current&&(document.body.style.WebkitUserSelect=eA.current,eA.current=void 0),eg.clear()});r.useEffect(()=>eb,[eb]);let ew=e=>{j.clear(),L=!0,eh(!0),G&&!ey&&G(e)},ex=(0,v.A)(e=>{j.start(800+X,()=>{L=!1}),eh(!1),_&&ey&&_(e),em.start(er.transitions.duration.shortest,()=>{es.current=!1})}),eT=e=>{es.current&&"touchstart"!==e.type||(en&&en.removeAttribute("title"),eu.clear(),ed.clear(),C||L&&N?eu.start(L?N:C,()=>{ew(e)}):ew(e))},eR=e=>{eu.clear(),ed.start(X,()=>{ex(e)})},[,e$]=r.useState(!1),ek=e=>{(0,s.A)(e.target)||(e$(!1),eR(e))},eM=e=>{en||ea(e.currentTarget),(0,s.A)(e.target)&&(e$(!0),eT(e))},eO=e=>{es.current=!0;let t=eo.props;t.onTouchStart&&t.onTouchStart(e)};r.useEffect(()=>{if(ey)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){"Escape"===e.key&&ex(e)}},[ex,ey]);let eS=(0,A.A)((0,c.A)(eo),ea,t);K||0===K||(ey=!1);let eE=r.useRef(),eW={},eL="string"==typeof K;T?(eW.title=ey||!eL||$?null:K,eW["aria-describedby"]=ey?ev:null):(eW["aria-label"]=eL?K:null,eW["aria-labelledby"]=ey&&!eL?ev:null);let ej={...eW,...et,...eo.props,className:(0,p.A)(et.className,eo.props.className),onTouchStart:eO,ref:eS,...z?{onMouseMove:e=>{let t=eo.props;t.onMouseMove&&t.onMouseMove(e),P={x:e.clientX,y:e.clientY},eE.current&&eE.current.update()}}:{}},eP={};B||(ej.onTouchStart=e=>{eO(e),ed.clear(),em.clear(),eb(),eA.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",eg.start(F,()=>{document.body.style.WebkitUserSelect=eA.current,eT(e)})},ej.onTouchEnd=e=>{eo.props.onTouchEnd&&eo.props.onTouchEnd(e),eb(),ed.start(D,()=>{ex(e)})}),!$&&(ej.onMouseOver=I(eT,ej.onMouseOver),ej.onMouseLeave=I(eR,ej.onMouseLeave),ec||(eP.onMouseOver=eT,eP.onMouseLeave=eR)),!R&&(ej.onFocus=I(eM,ej.onFocus),ej.onBlur=I(ek,ej.onBlur),ec||(eP.onFocus=eM,eP.onBlur=ek));let eI={...o,isRtl:ep,arrow:a,disableInteractive:ec,placement:V,PopperComponentProp:Y,touch:es.current},eB="function"==typeof q.popper?q.popper(eI):q.popper,eC=r.useMemo(()=>{let e=[{name:"arrow",enabled:!!ei,options:{element:ei,padding:4}}];return Z.popperOptions?.modifiers&&(e=e.concat(Z.popperOptions.modifiers)),eB?.popperOptions?.modifiers&&(e=e.concat(eB.popperOptions.modifiers)),{...Z.popperOptions,...eB?.popperOptions,modifiers:e}},[ei,Z.popperOptions,eB?.popperOptions]),eN=O(eI),eF="function"==typeof q.transition?q.transition(eI):q.transition,ez={slots:{popper:d.Popper,transition:d.Transition??Q,tooltip:d.Tooltip,arrow:d.Arrow,...J},slotProps:{arrow:q.arrow??f.arrow,popper:{...Z,...eB??f.popper},tooltip:q.tooltip??f.tooltip,transition:{...ee,...eF??f.transition}}},[eU,eX]=(0,x.A)("popper",{elementType:S,externalForwardedProps:ez,ownerState:eI,className:(0,p.A)(eN.popper,Z?.className)}),[eD,e_]=(0,x.A)("transition",{elementType:h.A,externalForwardedProps:ez,ownerState:eI}),[eG,eH]=(0,x.A)("tooltip",{elementType:E,className:eN.tooltip,externalForwardedProps:ez,ownerState:eI}),[eV,eY]=(0,x.A)("arrow",{elementType:W,className:eN.arrow,externalForwardedProps:ez,ownerState:eI,ref:el});return(0,M.jsxs)(r.Fragment,{children:[r.cloneElement(eo,ej),(0,M.jsx)(eU,{as:Y??y.A,placement:V,anchorEl:z?{getBoundingClientRect:()=>({top:P.y,left:P.x,right:P.x,bottom:P.y,width:0,height:0})}:en,popperRef:eE,open:!!en&&ey,id:ev,transition:!0,...eP,...eX,popperOptions:eC,children:({TransitionProps:e})=>(0,M.jsx)(eD,{timeout:er.transitions.duration.shorter,...e,...e_,children:(0,M.jsxs)(eG,{...eH,children:[K,a?(0,M.jsx)(eV,{...eY}):null]})})})]})})}};